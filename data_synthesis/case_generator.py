# data_synthesis/case_generator.py
import random
import json
import os
import threading
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, parse_llm_json

class CaseGenerator:
    """客户案例数据合成器"""
    
    def __init__(self):
        self.occupations = {
            "IT互联网": {"income_base": 12000, "stability": "较稳定"},
            "金融": {"income_base": 10000, "stability": "稳定"},
            "教育": {"income_base": 7000, "stability": "稳定"},
            "医疗": {"income_base": 9000, "stability": "稳定"},
            "服务业": {"income_base": 6000, "stability": "一般"},
            "制造业": {"income_base": 7500, "stability": "一般"},
            "政府机关": {"income_base": 8000, "stability": "很稳定"},
            "自由职业": {"income_base": 8500, "stability": "不稳定"}
        }
        
        self.purposes = ["房屋装修", "购买家具", "教育培训", "医疗支出", "旅游消费", 
                        "创业投资", "设备采购", "流动资金", "债务整合", "应急资金"]
        
        self.lock = threading.Lock()
        self.generated_count = 0
    
    def generate_single_case_with_llm(self, case_id: str) -> Dict[str, Any]:
        """使用LLM生成单个案例"""
        occupation_cat = random.choice(list(self.occupations.keys()))
        age = random.randint(22, 60)
        purpose = random.choice(self.purposes)
        
        prompt = f"""生成一个真实的贷款申请案例，JSON格式返回：
        
        案例设定：
        - 客户年龄：{age}岁
        - 职业类别：{occupation_cat}
        - 贷款用途：{purpose}
        
        请生成包含以下字段的JSON：
        {{
            "case_id": "{case_id}",
            "customer_profile": {{
                "age": {age},
                "occupation": "具体职业",
                "monthly_income": "月收入数值",
                "credit_score": "征信分数",
                "debt_to_income": "负债收入比",
                "employment_years": "工作年限"
            }},
            "application": {{
                "amount": "申请金额",
                "purpose": "{purpose}",
                "term": "贷款期限",
                "urgency": "紧急程度"
            }},
            "approval_result": {{
                "status": "批准/拒绝",
                "reason": "审批原因",
                "conditions": ["审批条件"]
            }}
        }}"""
        
        try:
            response = call_doubao_api_prompt(prompt)
            case_data = parse_llm_json(response)
            
            with self.lock:
                self.generated_count += 1
                if self.generated_count % 10 == 0:
                    print(f"已生成 {self.generated_count} 个案例...")
            
            return case_data
        except Exception as e:
            print(f"生成案例 {case_id} 失败: {e}")
            return self._generate_fallback_case(case_id, age, occupation_cat, purpose)
    
    def _generate_fallback_case(self, case_id: str, age: int, occupation_cat: str, purpose: str) -> Dict[str, Any]:
        """生成备用案例"""
        occ_info = self.occupations[occupation_cat]
        monthly_income = int(occ_info["income_base"] * random.uniform(0.7, 1.5))
        credit_score = random.randint(550, 850)
        
        return {
            "case_id": case_id,
            "customer_profile": {
                "age": age,
                "occupation": f"{occupation_cat}从业者",
                "monthly_income": monthly_income,
                "credit_score": credit_score,
                "debt_to_income": round(random.uniform(0.1, 0.8), 2),
                "employment_years": round(random.uniform(0.5, 15), 1)
            },
            "application": {
                "amount": random.randint(50000, 500000),
                "purpose": purpose,
                "term": f"{random.choice([12, 24, 36, 48, 60])}个月",
                "urgency": random.choice(["不急", "一般", "较急", "很急"])
            },
            "approval_result": {
                "status": "批准" if credit_score > 650 and monthly_income > 5000 else "拒绝",
                "reason": "综合评估结果",
                "conditions": ["按时还款", "保持联系方式畅通"]
            }
        }
    
    def generate_cases_concurrent(self, total_count: int = 100, max_workers: int = 10) -> List[Dict[str, Any]]:
        """并发生成案例数据"""
        print(f"开始并发生成 {total_count} 个案例，使用 {max_workers} 个线程...")
        
        cases = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_id = {
                executor.submit(self.generate_single_case_with_llm, f"CASE{i+1:05d}"): f"CASE{i+1:05d}"
                for i in range(total_count)
            }
            
            # 收集结果
            for future in as_completed(future_to_id):
                try:
                    case_data = future.result(timeout=60)
                    if case_data:
                        cases.append(case_data)
                except Exception as e:
                    case_id = future_to_id[future]
                    print(f"案例 {case_id} 生成失败: {e}")
        
        print(f"成功生成 {len(cases)} 个案例")
        return cases
    
    def save_cases(self, cases: List[Dict[str, Any]], filename: str = None):
        """保存案例数据"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"loan_cases_{timestamp}.json"
        
        # 确保目录存在
        output_dir = "data/synthesis"
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(cases, f, ensure_ascii=False, indent=2)
        
        print(f"已保存 {len(cases)} 个案例到 {filepath}")

if __name__ == "__main__":
    generator = CaseGenerator()
    
    # 生成案例数据
    cases = generator.generate_cases_concurrent(total_count=20, max_workers=20)
    
    # 显示示例
    for i, case in enumerate(cases[:3]):
        profile = case.get('customer_profile', {})
        result = case.get('approval_result', {})
        print(f"案例{i+1}: {profile.get('age', 'N/A')}岁, "
              f"收入{profile.get('monthly_income', 'N/A')}, "
              f"结果: {result.get('status', 'N/A')}")
    
    # 保存数据
    generator.save_cases(cases, "cases_samples.json")