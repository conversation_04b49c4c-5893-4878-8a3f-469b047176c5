# data_synthesis/policy_generator.py
import json
import random
import os
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, parse_llm_json

class PolicyGenerator:
    """监管政策数据生成器"""
    
    def __init__(self):
        self.issuers = ["中国银保监会", "中国人民银行", "国务院办公厅", "财政部", 
                       "国家发改委", "银保监会", "央行", "金融监管总局"]
        
        self.policy_types = ["管理办法", "实施细则", "指导意见", "通知", "公告",
                           "暂行规定", "监管要求", "风险提示", "合规指引"]
        
        self.focus_areas = ["个人贷款", "企业融资", "风险管理", "消费者保护",
                          "数据安全", "反洗钱", "利率管理", "征信管理"]
        
        self.lock = threading.Lock()
        self.generated_count = 0
    
    def generate_policy_with_llm(self, policy_id: str) -> Dict[str, Any]:
        """使用LLM生成监管政策"""
        issuer = random.choice(self.issuers)
        policy_type = random.choice(self.policy_types)
        focus_area = random.choice(self.focus_areas)
        effective_date = self._generate_effective_date()
        
        prompt = f"""生成一个监管政策文件，JSON格式返回：
        
        政策设定：
        - 发布机构：{issuer}
        - 政策类型：{policy_type}
        - 监管领域：{focus_area}
        - 生效时间：{effective_date}
        
        请生成包含以下字段的JSON：
        {{
            "policy_id": "{policy_id}",
            "title": "{focus_area}{policy_type}",
            "issuer": "{issuer}",
            "policy_type": "{policy_type}",
            "focus_area": "{focus_area}",
            "effective_date": "{effective_date}",
            "status": "政策状态",
            "content": {{
                "scope": "适用范围",
                "key_provisions": ["主要条款"],
                "compliance_requirements": ["合规要求"],
                "implementation_timeline": "实施时间",
                "penalties": ["违规处罚"],
                "exceptions": ["例外条款"]
            }},
            "business_impact": {{
                "affected_products": ["影响产品"],
                "operational_guidance": "操作指引",
                "implementation_cost": "实施成本",
                "business_adjustment_period": "调整期",
                "compliance_priority": "合规优先级",
                "industry_impact": "行业影响"
            }},
            "compliance_level": "合规等级",
            "update_frequency": "更新频率",
            "related_policies": ["相关政策"]
        }}"""
        
        try:
            response = call_doubao_api_prompt(prompt)
            policy_data = parse_llm_json(response)
            
            with self.lock:
                self.generated_count += 1
                if self.generated_count % 5 == 0:
                    print(f"已生成 {self.generated_count} 个政策...")
            
            return policy_data
        except Exception as e:
            print(f"生成政策 {policy_id} 失败: {e}")
            return self._generate_fallback_policy(policy_id, issuer, policy_type, focus_area, effective_date)
    
    def _generate_fallback_policy(self, policy_id: str, issuer: str, policy_type: str, 
                                 focus_area: str, effective_date: str) -> Dict[str, Any]:
        """生成备用政策"""
        return {
            "policy_id": policy_id,
            "title": f"{focus_area}{policy_type}",
            "issuer": issuer,
            "policy_type": policy_type,
            "focus_area": focus_area,
            "effective_date": effective_date,
            "status": random.choice(["生效", "草案", "即将生效"]),
            "content": {
                "scope": f"适用于{focus_area}相关业务",
                "key_provisions": self._get_key_provisions(focus_area),
                "compliance_requirements": self._get_compliance_requirements(focus_area),
                "implementation_timeline": f"{random.randint(3, 12)}个月内完成整改",
                "penalties": ["责令限期改正", "给予警告", "罚款"],
                "exceptions": ["紧急情况处理", "特殊业务豁免"]
            },
            "business_impact": {
                "affected_products": self._get_affected_products(focus_area),
                "operational_guidance": f"加强{focus_area}管理，完善相关制度",
                "implementation_cost": random.choice(["低", "中", "高"]),
                "business_adjustment_period": f"{random.randint(1, 6)}个月",
                "compliance_priority": random.choice(["高", "中", "低"]),
                "industry_impact": random.choice(["重大", "一般", "轻微"])
            },
            "compliance_level": random.choice(["强制", "推荐", "指导"]),
            "update_frequency": random.choice(["年度", "半年", "季度", "不定期"]),
            "related_policies": self._get_related_policies()
        }
    
    def _get_key_provisions(self, focus_area: str) -> List[str]:
        """获取主要条款"""
        provisions_map = {
            "个人贷款": ["贷款用途应当明确合法", "贷款金额应与还款能力匹配", "严格审查借款人资信"],
            "企业融资": ["加强企业真实融资需求审核", "建立健全授信管理制度", "完善担保措施"],
            "风险管理": ["建立全面风险管理体系", "完善风险识别评估机制", "加强风险监测报告"],
            "消费者保护": ["保障消费者知情权选择权", "建立投诉处理机制", "规范销售行为"]
        }
        return provisions_map.get(focus_area, ["加强业务管理", "完善制度建设", "提高服务质量"])
    
    def _get_compliance_requirements(self, focus_area: str) -> List[str]:
        """获取合规要求"""
        requirements_map = {
            "个人贷款": ["严格审查借款人资质", "建立贷后管理制度", "定期风险评估"],
            "企业融资": ["建立客户准入标准", "完善尽职调查程序", "加强贷后监管"],
            "风险管理": ["建立风险预警机制", "完善内控制度", "定期压力测试"],
            "消费者保护": ["加强信息披露", "完善投诉处理", "保护客户隐私"]
        }
        return requirements_map.get(focus_area, ["严格执行规定", "加强内部管理", "完善制度建设"])
    
    def _get_affected_products(self, focus_area: str) -> List[str]:
        """获取影响产品"""
        products_map = {
            "个人贷款": ["个人消费贷", "房贷", "车贷", "信用卡"],
            "企业融资": ["企业流动资金贷款", "项目贷款", "供应链金融"],
            "风险管理": ["所有贷款产品", "投资业务", "理财产品"],
            "消费者保护": ["零售银行业务", "个人理财", "保险产品"]
        }
        return products_map.get(focus_area, ["相关金融产品"])
    
    def _get_related_policies(self) -> List[str]:
        """获取相关政策"""
        policies = ["银行业监督管理法", "个人信息保护法", "反洗钱法", 
                   "征信业管理条例", "消费者权益保护法"]
        return random.sample(policies, random.randint(1, 3))
    
    def _generate_effective_date(self) -> str:
        """生成生效时间"""
        start_date = datetime.now() - timedelta(days=365)
        end_date = datetime.now() + timedelta(days=180)
        random_date = start_date + timedelta(days=random.randint(0, (end_date - start_date).days))
        return random_date.strftime("%Y-%m-%d")
    
    def generate_policy_update_with_llm(self, update_id: str, base_policy: Dict[str, Any]) -> Dict[str, Any]:
        """使用LLM生成政策更新"""
        update_type = random.choice(["修订", "补充", "废止", "延期", "澄清"])
        
        prompt = f"""生成一个政策更新文件，JSON格式返回：
        
        更新设定：
        - 原政策：{base_policy.get('title', '原政策')}
        - 更新类型：{update_type}
        - 原政策ID：{base_policy.get('policy_id', 'N/A')}
        
        请生成包含以下字段的JSON：
        {{
            "update_id": "{update_id}",
            "original_policy_id": "{base_policy.get('policy_id', 'N/A')}",
            "update_type": "{update_type}",
            "update_date": "更新日期",
            "update_content": "更新内容说明",
            "reason": "更新原因",
            "impact_assessment": "影响评估",
            "effective_immediately": "是否立即生效",
            "transition_period": "过渡期安排",
            "implementation_guidance": "实施指导"
        }}"""
        
        try:
            response = call_doubao_api_prompt(prompt)
            update_data = parse_llm_json(response)
            
            with self.lock:
                self.generated_count += 1
            
            return update_data
        except Exception as e:
            print(f"生成政策更新 {update_id} 失败: {e}")
            return self._generate_fallback_update(update_id, base_policy, update_type)
    
    def _generate_fallback_update(self, update_id: str, base_policy: Dict[str, Any], update_type: str) -> Dict[str, Any]:
        """生成备用政策更新"""
        content_map = {
            "修订": "对原条款进行调整，优化监管要求",
            "补充": "增加新的监管要求和操作指引",
            "废止": "因情况变化，原政策不再适用",
            "延期": "延长政策实施期限",
            "澄清": "对政策执行中的疑问进行解答"
        }
        
        return {
            "update_id": update_id,
            "original_policy_id": base_policy.get("policy_id", "N/A"),
            "update_type": update_type,
            "update_date": datetime.now().strftime("%Y-%m-%d"),
            "update_content": content_map.get(update_type, "政策内容更新"),
            "reason": random.choice(["市场环境变化", "监管经验积累", "技术发展需要"]),
            "impact_assessment": random.choice(["优化业务流程", "降低合规成本", "提高风险管控"]),
            "effective_immediately": random.choice([True, False]),
            "transition_period": f"{random.randint(1, 6)}个月",
            "implementation_guidance": "详见实施细则"
        }
    
    def generate_policies_concurrent(self, policy_count: int = 20, update_count: int = 5, max_workers: int = 8):
        """并发生成政策数据"""
        print(f"开始生成政策数据: {policy_count}个政策, {update_count}个更新...")
        
        policies, updates = [], []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 生成政策
            policy_futures = {
                executor.submit(self.generate_policy_with_llm, f"POLICY{i+1:03d}"): f"POLICY{i+1:03d}"
                for i in range(policy_count)
            }
            
            # 收集政策结果
            for future in as_completed(policy_futures):
                try:
                    policy_data = future.result(timeout=60)
                    if policy_data:
                        policies.append(policy_data)
                except Exception as e:
                    policy_id = policy_futures[future]
                    print(f"政策 {policy_id} 生成失败: {e}")
            
            # 生成政策更新（基于已生成的政策）
            if policies and update_count > 0:
                update_futures = {
                    executor.submit(self.generate_policy_update_with_llm, f"UPDATE{i+1:03d}", 
                                  random.choice(policies)): f"UPDATE{i+1:03d}"
                    for i in range(min(update_count, len(policies)))
                }
                
                # 收集更新结果
                for future in as_completed(update_futures):
                    try:
                        update_data = future.result(timeout=60)
                        if update_data:
                            updates.append(update_data)
                    except Exception as e:
                        update_id = update_futures[future]
                        print(f"政策更新 {update_id} 生成失败: {e}")
        
        return policies, updates
    
    def save_data(self, policies: List[Dict], updates: List[Dict]):
        """保存政策数据"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = "data/synthesis"
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存政策数据
        policy_file = os.path.join(output_dir, f"regulatory_policies_samples.json")
        with open(policy_file, 'w', encoding='utf-8') as f:
            json.dump(policies, f, ensure_ascii=False, indent=2)
        print(f"已保存 {len(policies)} 个政策到 {policy_file}")
        
        # 保存更新数据
        if updates:
            update_file = os.path.join(output_dir, f"policy_samples.json")
            with open(update_file, 'w', encoding='utf-8') as f:
                json.dump(updates, f, ensure_ascii=False, indent=2)
            print(f"已保存 {len(updates)} 个政策更新到 {update_file}")

if __name__ == "__main__":
    generator = PolicyGenerator()
    policies, updates = generator.generate_policies_concurrent(policy_count=10, update_count=3, max_workers=6)
    generator.save_data(policies, updates)