# data_synthesis/dialog_generator.py
import os
import json
import requests
from typing import Dict, List, Optional, Any, Tuple
import pandas as pd
import time
import re
import numpy as np
import random
from datetime import datetime, timedelta
import logging
import argparse
from dataclasses import dataclass
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
from queue import Queue
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, parse_llm_json

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('loan_dialogue_synthesis.log')
    ]
)
logger = logging.getLogger(__name__)

def format_time(seconds: float) -> str:
    """格式化时间显示"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        return f"{seconds//60:.0f}分{seconds%60:.1f}秒"
    else:
        return f"{seconds//3600:.0f}小时{(seconds%3600)//60:.0f}分"

class PerformanceTracker:
    """性能追踪器"""
    def __init__(self, total_samples: int, report_interval: int = 50):
        self.total_samples = total_samples
        self.report_interval = report_interval
        self.start_time = None
        self.last_report_time = None
        self.last_report_count = 0
        
        # 线程安全的计数器
        self._lock = threading.Lock()
        self._success_count = 0
        self._fail_count = 0
        self._total_processed = 0
        
        # 性能统计
        self.batch_times = []
        self.api_call_times = []
        self.task_details = []
        
        # 金融专业统计
        self.persona_stats = {}
        self.dialogue_type_stats = {}
        self.turns_stats = {}
        self.generation_method_stats = {}
        self.consultation_type_stats = {}
        self.product_type_stats = {}
        self.scenario_type_stats = {}

    def start(self):
        """开始计时"""
        self.start_time = time.time()
        self.last_report_time = self.start_time

    def record_task_start(self, task_id: int, task_info: dict):
        """记录任务开始"""
        with self._lock:
            self.task_details.append({
                'task_id': task_id,
                'start_time': time.time(),
                'status': 'processing',
                'info': task_info
            })
        logger.info(f"[开始] 任务{task_id}: 开始处理 - {task_info}")

    def record_task_success(self, task_id: int, result_data: dict, duration: float = None):
        """记录任务成功"""
        with self._lock:
            self._success_count += 1
            self._total_processed += 1
            
            # 更新金融统计
            self._update_financial_stats(result_data)
            
            # 更新任务详情
            for task in self.task_details:
                if task['task_id'] == task_id:
                    task['status'] = 'success'
                    task['end_time'] = time.time()
                    task['duration'] = duration or (task['end_time'] - task['start_time'])
                    logger.info(f"[成功] 任务{task_id}: 完成成功，耗时 {format_time(task['duration'])}")
                    break
            
            self._check_report()

    def record_task_failure(self, task_id: int, error: str = None):
        """记录任务失败"""
        with self._lock:
            self._fail_count += 1
            self._total_processed += 1
            
            # 更新任务详情
            for task in self.task_details:
                if task['task_id'] == task_id:
                    task['status'] = 'failed'
                    task['end_time'] = time.time()
                    task['error'] = error
                    logger.error(f"❌ 任务{task_id}: 失败 - {error}")
                    break
            
            self._check_report()

    def _update_financial_stats(self, result_data: dict):
        """更新金融专业统计"""
        metadata = result_data.get('metadata', {})
        
        # 统计对话类型
        dialogue_type = metadata.get('dialogue_type', '未知')
        self.dialogue_type_stats[dialogue_type] = self.dialogue_type_stats.get(dialogue_type, 0) + 1
        
        # 统计生成方法
        generation_method = metadata.get('generation_method', '未知')
        self.generation_method_stats[generation_method] = self.generation_method_stats.get(generation_method, 0) + 1
        
        # 统计对话轮数
        turns = metadata.get('dialogue_turns', 1)
        self.turns_stats[f"{turns}轮"] = self.turns_stats.get(f"{turns}轮", 0) + 1
        
        # 统计客户画像
        persona = metadata.get('customer_persona', '未知')
        self.persona_stats[persona] = self.persona_stats.get(persona, 0) + 1
        
        # 统计咨询类型
        consultation_type = metadata.get('consultation_type', '')
        if consultation_type:
            self.consultation_type_stats[consultation_type] = self.consultation_type_stats.get(consultation_type, 0) + 1
        
        # 统计产品类型
        product_type = metadata.get('product_type', '')
        if product_type:
            self.product_type_stats[product_type] = self.product_type_stats.get(product_type, 0) + 1
        
        # 统计场景类型
        scenario_type = metadata.get('scenario_type', '')
        if scenario_type:
            self.scenario_type_stats[scenario_type] = self.scenario_type_stats.get(scenario_type, 0) + 1

    def _check_report(self):
        """检查是否需要报告进度"""
        if self._total_processed % self.report_interval == 0:
            self._print_progress_report()

    def _print_progress_report(self):
        """打印进度报告"""
        current_time = time.time()
        total_elapsed = current_time - self.start_time
        
        # 计算速度
        overall_speed = self._total_processed / total_elapsed if total_elapsed > 0 else 0
        
        # 计算预计剩余时间
        remaining_samples = self.total_samples - self._total_processed
        estimated_remaining = remaining_samples / overall_speed if overall_speed > 0 else 0
        estimated_completion = datetime.now() + timedelta(seconds=estimated_remaining)
        
        print(f"\n[进度报告] 已完成 {self._total_processed}/{self.total_samples} 条数据")
        print(f"[总体进度] {(self._total_processed/self.total_samples)*100:.1f}% " + 
            "■" * int((self._total_processed/self.total_samples)*20))
        print(f"[总耗时] {format_time(total_elapsed)}")
        print(f"[总体速度] {overall_speed:.1f} 条/秒")
        print(f"[成功] {self._success_count} 条 ({(self._success_count/max(1,self._total_processed))*100:.1f}%)")
        print(f"[失败] {self._fail_count} 条")
        print(f"[预计剩余时间] {format_time(estimated_remaining)}")
        print(f"[预计完成时间] {estimated_completion.strftime('%Y-%m-%d %H:%M:%S')}")

    def print_final_stats(self):
        """打印最终统计"""
        total_time = time.time() - self.start_time
        
        print(f"\n金融对话数据合成完成!")
        print(f"总数据量: {self._total_processed}")
        print(f"成功: {self._success_count}")
        print(f"失败: {self._fail_count}")
        print(f"总耗时: {format_time(total_time)}")
        print(f"平均速度: {self._success_count/total_time:.2f} 条/秒")
        
        # 打印金融专业分布
        self._print_financial_distribution()

    def _print_financial_distribution(self):
        """打印金融专业分布统计"""
        print(f"\n金融对话数据集分布统计")
        print(f"有效数据量: {self._success_count}")
        
        # 对话类型分布
        if self.dialogue_type_stats:
            print(f"\n对话类型分布:")
            total = sum(self.dialogue_type_stats.values())
            for dialogue_type, count in sorted(self.dialogue_type_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total) * 100
                bar = "█" * int(percentage / 5)
                print(f"{dialogue_type:12}: {count:3d} 条 ({percentage:4.1f}%) {bar}")
        
        # 生成方法分布
        if self.generation_method_stats:
            print(f"\n生成方法分布:")
            total = sum(self.generation_method_stats.values())
            for method, count in sorted(self.generation_method_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total) * 100
                bar = "█" * int(percentage / 5)
                print(f"{method:16}: {count:3d} 条 ({percentage:4.1f}%) {bar}")
        
        # 咨询类型分布
        if self.consultation_type_stats:
            print(f"\n咨询类型分布:")
            total = sum(self.consultation_type_stats.values())
            for consultation_type, count in sorted(self.consultation_type_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total) * 100
                bar = "█" * int(percentage / 5)
                print(f"{consultation_type:12}: {count:3d} 条 ({percentage:4.1f}%) {bar}")
        
        # 产品类型分布
        if self.product_type_stats:
            print(f"\n产品类型分布:")
            total = sum(self.product_type_stats.values())
            for product_type, count in sorted(self.product_type_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total) * 100
                bar = "█" * int(percentage / 5)
                print(f"{product_type:12}: {count:3d} 条 ({percentage:4.1f}%) {bar}")
        
        # 对话场景分布
        if self.scenario_type_stats:
            print(f"\n对话场景分布:")
            total = sum(self.scenario_type_stats.values())
            for scenario, count in sorted(self.scenario_type_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total) * 100
                bar = "█" * int(percentage / 5)
                print(f"{scenario:12}: {count:3d} 条 ({percentage:4.1f}%) {bar}")
        
        # 对话轮数分布
        if self.turns_stats:
            print(f"\n对话轮数分布:")
            total = sum(self.turns_stats.values())
            for turns, count in sorted(self.turns_stats.items(), key=lambda x: int(x[0].replace('轮', ''))):
                percentage = (count / total) * 100
                bar = "█" * int(percentage / 5)
                print(f"{turns:6}: {count:3d} 条 ({percentage:4.1f}%) {bar}")
        
        # 客户画像分布
        if self.persona_stats:
            print(f"\n客户画像分布:")
            total = sum(self.persona_stats.values())
            for persona, count in sorted(self.persona_stats.items(), key=lambda x: x[1], reverse=True):
                percentage = (count / total) * 100
                bar = "█" * int(percentage / 5)
                print(f"{persona:12}: {count:3d} 条 ({percentage:4.1f}%) {bar}")

class LoanKnowledgeBase:
    """贷款知识库管理器"""
    
    def __init__(self):
        self.consultation_types = self._load_consultation_types()
        self.dialogue_scenarios = self._load_dialogue_scenarios()
        
    def _load_consultation_types(self) -> Dict[str, List[str]]:
        """加载咨询主题分类"""
        return {
            "产品咨询": [
                "个人消费贷款产品介绍和特点",
                "房屋抵押贷款产品优势和要求",
                "小微企业贷款产品类型和条件",
                "信用贷款和抵押贷款的区别",
                "线上申请和线下申请的流程差异",
                "不同贷款产品的适用人群",
                "贷款产品的额度范围和期限",
                "新产品推介和优惠活动",
                "产品组合和打包方案",
                "特色贷款产品介绍"
            ],
            "申请条件": [
                "个人贷款申请的基本条件要求",
                "征信记录对贷款申请的影响",
                "收入证明和工作稳定性要求",
                "担保人和抵押物的必要性",
                "不同职业申请贷款的特殊要求",
                "年龄限制和其他资格条件",
                "已有贷款对新申请的影响",
                "征信不良记录的补救措施",
                "提高申请成功率的方法",
                "特殊群体的申请条件"
            ],
            "利率查询": [
                "当前贷款利率水平和政策",
                "影响个人贷款利率的因素",
                "固定利率和浮动利率的选择",
                "利率优惠政策和条件",
                "不同期限贷款的利率差异",
                "提前还款的利率计算",
                "利率调整机制和通知方式",
                "利率比较和选择建议",
                "利息计算方法和实例",
                "利率风险和管理策略"
            ],
            "额度评估": [
                "贷款额度的评估标准和方法",
                "个人资质对额度的影响因素",
                "收入倍数和负债比例要求",
                "抵押物价值对额度的影响",
                "提高贷款额度的有效方法",
                "额度不足时的解决方案",
                "额度使用和管理建议",
                "额度调整的申请流程",
                "额度有效期和续期方式",
                "额度分配和使用策略"
            ],
            "申请流程": [
                "贷款申请的完整流程指导",
                "在线申请和线下申请的步骤",
                "申请材料准备和提交要求",
                "审批流程和时间节点",
                "面签和实地调查的安排",
                "审批结果查询和通知方式",
                "放款流程和到账时间",
                "申请被拒后的处理方式",
                "申请流程中的注意事项",
                "加快审批速度的方法"
            ],
            "材料准备": [
                "贷款申请所需材料清单",
                "身份证明和户籍证明要求",
                "收入证明和工作证明格式",
                "资产证明和财务报表要求",
                "征信报告获取和准备",
                "抵押物证明和评估报告",
                "担保材料和关系证明",
                "特殊职业的材料要求",
                "材料真实性和有效性",
                "材料补充和更新流程"
            ],
            "进度查询": [
                "申请进度查询的渠道和方式",
                "审批各阶段的处理时间",
                "进度延迟的常见原因",
                "加快审批进度的方法",
                "审批结果通知和确认",
                "补充材料的及时提供",
                "进度异常的处理方式",
                "客户经理联系和沟通",
                "进度查询的注意事项",
                "系统查询和人工查询"
            ],
            "还款方式": [
                "等额本息和等额本金的差异",
                "不同还款方式的适用场景",
                "提前还款的政策和费用",
                "还款日期和还款渠道",
                "自动扣款和手动还款设置",
                "还款计划调整和变更",
                "逾期还款的后果和处理",
                "还款能力评估和规划",
                "还款优化和策略建议",
                "特殊情况下的还款安排"
            ],
            "风险评估": [
                "贷款风险的识别和评估",
                "个人信用风险的管控",
                "市场风险和利率风险",
                "还款能力风险评估",
                "抵押物风险和价值波动",
                "政策风险和合规要求",
                "风险预警和监控机制",
                "风险缓释和保障措施",
                "风险承受能力评估",
                "风险管理建议和策略"
            ],
            "政策解读": [
                "最新贷款政策变化解读",
                "央行货币政策对贷款的影响",
                "房地产调控政策解读",
                "小微企业扶持政策",
                "个人征信政策变化",
                "利率市场化改革影响",
                "监管政策和合规要求",
                "税收政策和贷款关系",
                "地方政策和优惠措施",
                "政策趋势和预期"
            ]
        }
    
    def _load_dialogue_scenarios(self) -> Dict[str, List[str]]:
        """加载多轮对话场景"""
        return {
            "初次咨询": [
                "客户首次了解贷款产品，需要全面介绍",
                "客户有明确需求，需要推荐合适产品",
                "客户对贷款不太了解，需要基础教育",
                "客户比较多个产品，需要详细对比",
                "客户有特殊需求，需要定制化方案",
                "客户担心风险，需要充分沟通"
            ],
            "申请指导": [
                "指导客户准备申请材料",
                "帮助客户填写申请表格",
                "解答申请流程中的疑问",
                "协助客户提高申请成功率",
                "处理申请过程中的问题",
                "提供申请策略和建议"
            ],
            "进度跟踪": [
                "查询申请审批进度",
                "解释审批延迟原因",
                "协调加快审批速度",
                "处理审批中的异常情况",
                "更新客户申请状态",
                "安排后续流程事项"
            ],
            "方案设计": [
                "根据客户需求设计贷款方案",
                "优化还款计划和期限",
                "搭配多种金融产品",
                "平衡风险和收益",
                "考虑客户财务状况",
                "提供个性化建议"
            ],
            "问题解决": [
                "处理客户投诉和不满",
                "解决技术和操作问题",
                "协调内部资源和流程",
                "提供替代解决方案",
                "跟进问题处理结果",
                "预防类似问题再发生"
            ],
            "风险沟通": [
                "说明贷款风险和注意事项",
                "评估客户风险承受能力",
                "提供风险管理建议",
                "处理风险相关担忧",
                "制定风险应对策略",
                "建立风险监控机制"
            ],
            "售后服务": [
                "贷款发放后的服务跟进",
                "还款指导和提醒服务",
                "处理还款过程中的问题",
                "提供增值服务和建议",
                "维护长期客户关系",
                "推荐其他金融产品"
            ],
            "政策咨询": [
                "解读最新政策变化",
                "分析政策对客户的影响",
                "提供政策适应建议",
                "预测政策发展趋势",
                "协助客户把握政策机遇",
                "规避政策风险"
            ]
        }
    
    def get_consultation_topic(self, category: str = None) -> Tuple[str, str]:
        """获取咨询话题"""
        if category and category in self.consultation_types:
            topic = random.choice(self.consultation_types[category])
            return category, topic
        
        # 随机选择类别和话题
        random_category = random.choice(list(self.consultation_types.keys()))
        topic = random.choice(self.consultation_types[random_category])
        return random_category, topic
    
    def get_dialogue_scenario(self, scenario_type: str = None) -> Tuple[str, str]:
        """获取对话场景"""
        if scenario_type and scenario_type in self.dialogue_scenarios:
            scenario = random.choice(self.dialogue_scenarios[scenario_type])
            return scenario_type, scenario
        
        # 随机选择场景类型和具体场景
        random_type = random.choice(list(self.dialogue_scenarios.keys()))
        scenario = random.choice(self.dialogue_scenarios[random_type])
        return random_type, scenario

class LoanDataGenerator:
    """贷款数据生成器"""
    
    def __init__(self):
        self.product_types = ["个人消费贷", "房屋抵押贷", "小微企业贷", "信用贷", "车辆抵押贷", "经营贷", "装修贷", "教育贷"]
        self.loan_amounts = ["5万", "10万", "20万", "50万", "100万", "200万", "500万"]
        self.customer_types = ["首次申请", "老客户", "VIP客户", "小微企业主", "工薪族", "自由职业者"]
        self.urgency_levels = ["不急", "一般", "较急", "很急"]
        self.age_groups = {
            "青年": (22, 35),
            "中年": (36, 50), 
            "中老年": (51, 65)
        }

    def generate_customer_case(self, product_type: str = None, urgency: str = None) -> Dict:
        """生成客户案例数据（用于多轮对话场景）"""
        product = product_type or random.choice(self.product_types)
        urgent_level = urgency or random.choice(self.urgency_levels)
        age_group = random.choice(list(self.age_groups.keys()))
        age_range = self.age_groups[age_group]
        age = random.randint(age_range[0], age_range[1])
        
        case_data = {
            "product_type": product,
            "loan_amount": random.choice(self.loan_amounts),
            "customer_age": age,
            "age_group": age_group,
            "customer_type": random.choice(self.customer_types),
            "urgency_level": urgent_level,
            "loan_purpose": self._generate_loan_purpose(product),
            "income_level": self._generate_income_level(age_group),
            "credit_status": self._generate_credit_status(),
            "case_complexity": self._calculate_complexity(urgent_level, product)
        }
        
        return case_data

    def _generate_loan_purpose(self, product_type: str) -> str:
        """生成贷款用途"""
        purposes = {
            "个人消费贷": ["装修", "旅游", "教育", "医疗", "购买家电"],
            "房屋抵押贷": ["房屋装修", "子女教育", "投资理财", "经营周转"],
            "小微企业贷": ["设备采购", "流动资金", "扩大经营", "原料采购"],
            "信用贷": ["应急资金", "消费支出", "短期周转", "投资需求"],
            "车辆抵押贷": ["紧急资金", "生意周转", "其他投资", "家庭支出"],
            "经营贷": ["扩大规模", "设备更新", "库存采购", "市场拓展"],
            "装修贷": ["房屋装修", "家具购买", "家电更换", "装修升级"],
            "教育贷": ["子女教育", "技能培训", "出国留学", "继续教育"]
        }
        
        purpose_list = purposes.get(product_type, ["个人需求"])
        return random.choice(purpose_list)

    def _generate_income_level(self, age_group: str) -> str:
        """生成收入水平"""
        if age_group == "青年":
            return random.choice(["3千-5千", "5千-8千", "8千-1万"])
        elif age_group == "中年":
            return random.choice(["8千-1万5", "1万5-3万", "3万以上"])
        else:  # 中老年
            return random.choice(["5千-1万", "1万-2万", "2万以上"])

    def _generate_credit_status(self) -> str:
        """生成征信状况"""
        statuses = ["优秀", "良好", "一般", "待改善"]
        weights = [0.3, 0.4, 0.2, 0.1]  # 大部分客户征信较好
        return random.choices(statuses, weights=weights)[0]

    def _calculate_complexity(self, urgency: str, product_type: str) -> str:
        """计算案例复杂度"""
        complexity_score = 0
        
        # 紧急程度评分
        urgency_scores = {"不急": 1, "一般": 2, "较急": 3, "很急": 4}
        complexity_score += urgency_scores[urgency]
        
        # 产品复杂度评分
        product_scores = {
            "信用贷": 1, "个人消费贷": 2, "装修贷": 2, "教育贷": 2,
            "车辆抵押贷": 3, "房屋抵押贷": 4, "经营贷": 4, "小微企业贷": 5
        }
        complexity_score += product_scores.get(product_type, 3)
        
        if complexity_score <= 3:
            return "简单"
        elif complexity_score <= 6:
            return "中等"
        else:
            return "复杂"

class CustomerPersonas:
    """客户画像管理器"""
    
    def __init__(self):
        self.customer_personas = {
            "年轻上班族": {
                "characteristics": "收入稳定，接受新事物快，关注效率和便利性",
                "question_style": "直接、简洁、注重时效",
                "concerns": ["申请便利性", "审批速度", "利率优惠", "还款灵活性"],
                "financial_awareness": "中等",
                "risk_tolerance": "中等",
                "decision_factors": ["便利性", "速度", "利率"]
            },
            "中年企业主": {
                "characteristics": "有经营经验，注重实用性，看重资金使用效率",
                "question_style": "务实、详细、关注细节",
                "concerns": ["贷款额度", "还款方式", "资金成本", "申请条件"],
                "financial_awareness": "高",
                "risk_tolerance": "中高",
                "decision_factors": ["额度", "成本", "灵活性"]
            },
            "保守型客户": {
                "characteristics": "风险偏好低，谨慎稳重，重视安全性",
                "question_style": "谨慎、反复确认、详细了解",
                "concerns": ["安全性", "合规性", "风险控制", "还款保障"],
                "financial_awareness": "中等",
                "risk_tolerance": "低",
                "decision_factors": ["安全性", "稳定性", "保障性"]
            },
            "首次申请客户": {
                "characteristics": "缺乏贷款经验，需要详细指导，容易紧张",
                "question_style": "基础性、重复性、需要确认",
                "concerns": ["申请流程", "所需材料", "审批要求", "注意事项"],
                "financial_awareness": "低",
                "risk_tolerance": "低",
                "decision_factors": ["指导性", "清晰性", "支持度"]
            },
            "高净值客户": {
                "characteristics": "资产较多，需求复杂，追求个性化服务",
                "question_style": "专业、具体、高要求",
                "concerns": ["个性化方案", "优惠政策", "专属服务", "投资建议"],
                "financial_awareness": "高",
                "risk_tolerance": "中高",
                "decision_factors": ["专业性", "个性化", "服务质量"]
            },
            "小微企业主": {
                "characteristics": "资金需求急迫，流程要求简单，关注放款速度",
                "question_style": "急迫、直接、效率导向",
                "concerns": ["放款速度", "申请门槛", "资金周转", "政策支持"],
                "financial_awareness": "中高",
                "risk_tolerance": "高",
                "decision_factors": ["速度", "门槛", "额度"]
            }
        }

class LoanDataSynthesizer:
    """贷款数据合成器主类"""
    
    def __init__(self, use_doubao: bool = True):
        self.use_doubao = use_doubao
        self.knowledge_base = LoanKnowledgeBase()
        self.data_generator = LoanDataGenerator()
        self.customer_personas = CustomerPersonas()
        self.tracker = None

    def call_llm_api(self, prompt: str) -> str:
        """调用LLM API"""
        if self.use_doubao:
            return call_doubao_api_prompt(prompt)
        else:
            # 可以在这里添加其他LLM调用逻辑
            raise NotImplementedError("其他LLM接口待实现")

    def generate_consultation_qa(self, persona: str, consultation_info: Tuple[str, str]) -> Tuple[str, str, float]:
        """生成咨询问答"""
        start_time = time.time()
        
        persona_info = self.customer_personas.customer_personas.get(persona, {})
        category, topic = consultation_info
        
        prompt = f"""你是一个专业的金融对话合成专家，需要生成高质量的贷款咨询问答对。

咨询话题: {topic}
咨询类别: {category}
客户画像: {persona}
- 特征: {persona_info.get('characteristics', '')}
- 问题风格: {persona_info.get('question_style', '')}
- 关注点: {', '.join(persona_info.get('concerns', []))}
- 决策因素: {', '.join(persona_info.get('decision_factors', []))}

任务要求:
1. 生成一个围绕指定话题的常见贷款咨询问题
2. 生成专业准确的客服回答
3. 问题要体现客户画像的特点和表达习惯
4. 回答要专业权威，符合金融服务规范
5. 内容要有实用价值，解决客户实际需求
6. 确保金融信息准确，符合监管要求

请按以下格式生成完整的问答对:
问题: [客户问题]
回答: [客服专业回答]

请生成问答对:"""
        
        try:
            result = self.call_llm_api(prompt)
            duration = time.time() - start_time
            
            # 解析问题和回答
            question, answer = self._parse_qa_result(result)
            return question, answer, duration
            
        except Exception as e:
            logger.error(f"生成咨询问答失败: {str(e)}")
            raise

    def generate_direct_multi_turn_dialogue(self, persona: str, scenario_info: Tuple[str, str], 
                                          target_turns: int = 3, case_data: Dict = None) -> Tuple[List[Dict], float]:
        """直接生成完整多轮对话"""
        start_time = time.time()
        
        persona_info = self.customer_personas.customer_personas.get(persona, {})
        scenario_type, scenario_desc = scenario_info
        
        # 如果需要客户案例信息，则生成
        if case_data is None:
            case_data = self.data_generator.generate_customer_case()
        
        case_description = self._build_case_description(case_data)
        
        prompt = f"""你是一个专业的金融对话合成专家，需要直接生成完整的客户与贷款顾问多轮对话。

对话设定:
- 对话轮数: {target_turns}轮 (客户顾问各{target_turns}次发言)
- 客户画像: {persona}
  * 特征: {persona_info.get('characteristics', '')}
  * 问题风格: {persona_info.get('question_style', '')}
  * 关注点: {', '.join(persona_info.get('concerns', []))}
  * 决策因素: {', '.join(persona_info.get('decision_factors', []))}
- 对话场景: {scenario_type} - {scenario_desc}

客户案例背景:
{case_description}

对话要求:
1. 第一轮：客户提出贷款需求或咨询问题
2. 顾问：专业分析，了解更多信息
3. 后续轮次：客户根据顾问回答进行自然追问
4. 整个对话要体现客户画像特点和关注重点
5. 顾问回答要专业准确，符合金融服务规范
6. 对话要有逻辑性和连贯性，解决客户实际问题
7. 最后一轮要有明确的方案建议或后续行动计划

请生成完整的多轮对话，格式如下:
[
    {{"role": "customer", "content": "客户第1轮发言"}},
    {{"role": "advisor", "content": "顾问第1轮回复"}},
    {{"role": "customer", "content": "客户第2轮发言"}},
    {{"role": "advisor", "content": "顾问第2轮回复"}},
    ...
]

请生成对话（只输出JSON格式的对话数组）："""

        try:
            dialogue_text = self.call_llm_api(prompt)
            duration = time.time() - start_time
            
            # 解析JSON格式的对话
            try:
                dialogue = json.loads(dialogue_text)
                if not isinstance(dialogue, list):
                    raise ValueError("对话格式不正确")
                return dialogue, duration
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试简单解析
                logger.warning("JSON解析失败，尝试文本解析")
                dialogue = self._parse_dialogue_text(dialogue_text)
                return dialogue, duration
                
        except Exception as e:
            logger.error(f"直接生成多轮对话失败: {str(e)}")
            raise

    def build_multi_turn_dialogue(self, persona: str, scenario_info: Tuple[str, str], 
                                 target_turns: int = 3, case_data: Dict = None) -> Tuple[List[Dict], float]:
        """构建多轮对话（通过多次单轮对话迭代）"""
        start_time = time.time()
        
        dialogue = []
        conversation_context = ""
        
        persona_info = self.customer_personas.customer_personas.get(persona, {})
        scenario_type, scenario_desc = scenario_info
        
        # 如果需要客户案例信息，则生成
        if case_data is None:
            case_data = self.data_generator.generate_customer_case()
        
        case_description = self._build_case_description(case_data)
        
        for turn in range(target_turns):
            if turn == 0:
                # 第一轮：客户提出初始问题
                prompt = f"""你是一个专业的金融对话合成专家，需要生成客户与贷款顾问对话的第一轮。

客户画像: {persona}
- 特征: {persona_info.get('characteristics', '')}
- 问题风格: {persona_info.get('question_style', '')}
- 关注点: {', '.join(persona_info.get('concerns', []))}

对话场景: {scenario_type} - {scenario_desc}

客户案例背景:
{case_description}

任务：生成客户的第一个问题，要围绕对话场景和案例背景，体现客户画像特点。
                
请直接生成客户问题（不需要其他格式）："""
                
                customer_question = self.call_llm_api(prompt)
                dialogue.append({"role": "customer", "content": customer_question.strip()})
                conversation_context += f"客户: {customer_question.strip()}\n"
                
                # 顾问回答第一轮
                prompt = f"""你是一位专业的贷款顾问，需要回答客户的咨询问题。

客户画像: {persona}
对话场景: {scenario_type} - {scenario_desc}
客户问题: {customer_question.strip()}
                
客户案例背景:
{case_description}

请提供专业的顾问回答，注意：
1. 回答要专业准确，符合金融服务规范
2. 考虑客户画像特点，调整回答风格和重点
3. 可以询问更多信息以便进一步了解情况
4. 给出初步的分析、建议或指导

请生成顾问回答："""
                
                advisor_answer = self.call_llm_api(prompt)
                dialogue.append({"role": "advisor", "content": advisor_answer.strip()})
                conversation_context += f"顾问: {advisor_answer.strip()}\n"
                
            else:
                # 后续轮次：基于前面的对话继续
                prompt = f"""你是一个专业的金融对话合成专家，需要继续生成客户与贷款顾问的对话。

客户画像: {persona}
- 特征: {persona_info.get('characteristics', '')}
- 关注点: {', '.join(persona_info.get('concerns', []))}

对话场景: {scenario_type} - {scenario_desc}

前面的对话:
{conversation_context}

客户案例背景:
{case_description}

任务：生成客户的第{turn+1}轮问题。要求：
1. 基于顾问前面的回答进行自然的追问
2. 体现客户画像的特点和关注点
3. 可以是补充信息、询问细节、关心后续等
4. {"最后一轮要适当总结或表达感谢" if turn == target_turns-1 else ""}

请直接生成客户问题："""
                
                customer_question = self.call_llm_api(prompt)
                dialogue.append({"role": "customer", "content": customer_question.strip()})
                conversation_context += f"客户: {customer_question.strip()}\n"
                
                # 顾问回答
                prompt = f"""你是一位专业的贷款顾问，需要继续回答客户的咨询问题。

完整对话记录:
{conversation_context}

客户案例背景:
{case_description}

客户最新问题: {customer_question.strip()}

请提供专业的顾问回答，注意：
1. 基于前面的对话内容给出连贯的回答
2. 回答要专业准确，符合客户需求
3. {"给出明确的方案建议和后续行动计划" if turn == target_turns-1 else "可以进一步询问或给出具体建议"}

请生成顾问回答："""
                
                advisor_answer = self.call_llm_api(prompt)
                dialogue.append({"role": "advisor", "content": advisor_answer.strip()})
                conversation_context += f"顾问: {advisor_answer.strip()}\n"
        
        total_duration = time.time() - start_time
        return dialogue, total_duration

    def _parse_qa_result(self, result: str) -> Tuple[str, str]:
        """解析问答结果"""
        lines = result.split('\n')
        question = ""
        answer = ""
        current_section = None
        
        for line in lines:
            line = line.strip()
            if line.startswith('问题:') or line.startswith('Question:'):
                current_section = "question"
                question = line.replace('问题:', '').replace('Question:', '').strip()
            elif line.startswith('回答:') or line.startswith('Answer:'):
                current_section = "answer"
                answer = line.replace('回答:', '').replace('Answer:', '').strip()
            elif line and current_section == "question":
                question += " " + line
            elif line and current_section == "answer":
                answer += "\n" + line
        
        if not question or not answer:
            # 如果解析失败，尝试其他方式分割
            parts = result.split('回答:')
            if len(parts) >= 2:
                question_part = parts[0].replace('问题:', '').strip()
                answer_part = parts[1].strip()
                return question_part, answer_part
        
        return question.strip(), answer.strip()

    def _parse_dialogue_text(self, text: str) -> List[Dict]:
        """解析对话文本为结构化格式"""
        dialogue = []
        lines = text.split('\n')
        current_role = None
        current_content = ""
        
        for line in lines:
            line = line.strip()
            if line.startswith('客户:') or line.startswith('Customer:'):
                if current_role and current_content:
                    dialogue.append({"role": current_role, "content": current_content.strip()})
                current_role = "customer"
                current_content = line.replace('客户:', '').replace('Customer:', '').strip()
            elif line.startswith('顾问:') or line.startswith('Advisor:'):
                if current_role and current_content:
                    dialogue.append({"role": current_role, "content": current_content.strip()})
                current_role = "advisor"
                current_content = line.replace('顾问:', '').replace('Advisor:', '').strip()
            elif line and current_role:
                current_content += " " + line
        
        # 添加最后一个对话
        if current_role and current_content:
            dialogue.append({"role": current_role, "content": current_content.strip()})
        
        return dialogue

    def _build_case_description(self, case_data: Dict) -> str:
        """构建客户案例描述"""
        if not case_data:
            return "无具体客户案例信息"
            
        description = f"""客户案例背景:
            - 客户年龄: {case_data['customer_age']}岁 ({case_data['age_group']})
            - 客户类型: {case_data['customer_type']}
            - 贷款产品: {case_data['product_type']}
            - 贷款金额: {case_data['loan_amount']}
            - 贷款用途: {case_data['loan_purpose']}
            - 收入水平: {case_data['income_level']}
            - 征信状况: {case_data['credit_status']}
            - 紧急程度: {case_data['urgency_level']}
            - 案例复杂度: {case_data['case_complexity']}"""
        
        return description

    def generate_qa_pair(self, task_id: int, dialogue_type: str = "consultation", 
                        multi_turn_method: str = "direct") -> Dict:
        """生成问答对"""
        try:
            # 随机选择客户画像
            persona = random.choice(list(self.customer_personas.customer_personas.keys()))
            
            if dialogue_type == "consultation":
                # 咨询问答模式
                consultation_info = self.knowledge_base.get_consultation_topic()
                
                # 记录任务开始
                task_info = {
                    'dialogue_type': '咨询问答',
                    'persona': persona,
                    'consultation_type': consultation_info[0],
                    'consultation_topic': consultation_info[1]
                }
                
                if self.tracker:
                    self.tracker.record_task_start(task_id, task_info)
                
                logger.info(f"任务{task_id}: 开始生成咨询问答 - 画像: {persona}, 类别: {consultation_info[0]}")
                
                # 生成咨询问答
                question, answer, qa_duration = self.generate_consultation_qa(persona, consultation_info)
                
                result = {
                    "instruction": "你是一位专业的贷款顾问，请回答客户的贷款咨询问题，提供准确可靠的金融建议。",
                    "input": question,
                    "output": answer,
                    "metadata": {
                        "dialogue_type": "咨询问答",
                        "generation_method": "咨询生成",
                        "customer_persona": persona,
                        "consultation_type": consultation_info[0],
                        "consultation_topic": consultation_info[1],
                        "generation_time": datetime.now().isoformat(),
                        "durations": {
                            "total": qa_duration
                        },
                        "quality_indicators": {
                            "data_enhanced": True,
                            "persona_matched": True,
                            "consultation_type": True
                        }
                    }
                }
                
                total_duration = qa_duration
                
            else:  # multi-turn dialogue
                # 多轮对话模式
                scenario_info = self.knowledge_base.get_dialogue_scenario()
                case_data = self.data_generator.generate_customer_case()
                
                # 随机确定对话轮数（2-5轮）
                target_turns = random.randint(2, 5)
                
                # 记录任务开始
                task_info = {
                    'dialogue_type': '多轮对话',
                    'persona': persona,
                    'scenario_type': scenario_info[0],
                    'target_turns': target_turns,
                    'multi_turn_method': multi_turn_method,
                    'product_type': case_data['product_type'],
                    'urgency': case_data['urgency_level']
                }
                
                if self.tracker:
                    self.tracker.record_task_start(task_id, task_info)
                
                logger.info(f"任务{task_id}: 开始生成{target_turns}轮对话 - 画像: {persona}, 场景: {scenario_info[0]}, 方法: {multi_turn_method}")
                
                # 根据方法选择生成方式
                if multi_turn_method == "direct":
                    # 直接生成完整对话
                    dialogue, dialogue_duration = self.generate_direct_multi_turn_dialogue(
                        persona, scenario_info, target_turns, case_data)
                    generation_method = "直接生成"
                else:
                    # 迭代式生成
                    dialogue, dialogue_duration = self.build_multi_turn_dialogue(
                        persona, scenario_info, target_turns, case_data)
                    generation_method = "迭代生成"
                
                result = {
                    "instruction": "你是一位专业的贷款顾问，请与客户进行多轮专业对话，逐步了解需求并给出方案建议。",
                    "input": "多轮贷款咨询对话场景",
                    "output": dialogue,
                    "metadata": {
                        "dialogue_type": "多轮对话",
                        "generation_method": generation_method,
                        "customer_persona": persona,
                        "scenario_type": scenario_info[0],
                        "scenario_desc": scenario_info[1],
                        "dialogue_turns": len(dialogue) // 2,  # 实际轮数
                        "target_turns": target_turns,
                        "multi_turn_method": multi_turn_method,
                        "case_data": case_data,
                        "product_type": case_data['product_type'],
                        "customer_background": self._build_case_description(case_data),
                        "generation_time": datetime.now().isoformat(),
                        "durations": {
                            "total": dialogue_duration
                        },
                        "quality_indicators": {
                            "data_enhanced": True,
                            "persona_matched": True,
                            "multi_turn": True,
                            "case_complexity": case_data['case_complexity']
                        }
                    }
                }
                
                total_duration = dialogue_duration
            
            logger.info(f"任务{task_id}: 完成! 总耗时 {format_time(total_duration)}")
            
            if self.tracker:
                self.tracker.record_task_success(task_id, result, total_duration)
            
            return result
            
        except Exception as e:
            error_msg = f"生成失败: {str(e)}"
            if self.tracker:
                self.tracker.record_task_failure(task_id, error_msg)
            raise Exception(error_msg)

    def generate_dataset(self, total_samples: int, max_workers: int = 8, 
                        save_format: str = "both", output_file: str = None,
                        consultation_ratio: float = 0.4, multi_ratio: float = 0.6,
                        multi_turn_method: str = "direct") -> List[Dict]:
        """生成完整数据集"""
        logger.info(f"开始生成贷款对话数据集，目标样本数: {total_samples}")
        logger.info(f"数据类型分布 - 咨询问答: {consultation_ratio*100}%, 多轮对话: {multi_ratio*100}%")
        logger.info(f"多轮对话生成方法: {multi_turn_method}")
        
        # 计算各类型数量
        consultation_count = int(total_samples * consultation_ratio)
        multi_count = total_samples - consultation_count
        
        logger.info(f"具体数量 - 咨询问答: {consultation_count}, 多轮对话: {multi_count}")
        
        # 初始化性能追踪器
        self.tracker = PerformanceTracker(total_samples, report_interval=max(10, total_samples//20))
        self.tracker.start()
        
        consultation_results = []
        multi_results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            futures = []
            task_id = 0
            
            # 咨询问答任务
            for _ in range(consultation_count):
                future = executor.submit(self.generate_qa_pair, task_id, "consultation")
                futures.append(("consultation", future))
                task_id += 1
            
            # 多轮对话任务
            for _ in range(multi_count):
                future = executor.submit(self.generate_qa_pair, task_id, "multi", multi_turn_method)
                futures.append(("multi", future))
                task_id += 1
            
            # 收集结果
            for data_type, future in futures:
                try:
                    result = future.result(timeout=300)  # 5分钟超时
                    if data_type == "consultation":
                        consultation_results.append(result)
                    else:
                        multi_results.append(result)
                except Exception as e:
                    logger.error(f"任务失败: {str(e)}")
        
        # 打印最终统计
        self.tracker.print_final_stats()
        
        # 分别保存结果
        if consultation_results or multi_results:
            self._save_results_separately(consultation_results, multi_results, save_format, output_file)
        
        return consultation_results + multi_results

    def _save_results_separately(self, consultation_results: List[Dict], multi_results: List[Dict], 
                                save_format: str, output_file: str = None):
        """分别保存咨询问答和多轮对话结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = output_file or f"loan_dialogue_dataset_{timestamp}"
        
        # 保存咨询问答数据
        if consultation_results:
            if save_format in ["json", "both"]:
                consultation_json_filename = f"{base_filename}_consultation.json"
                with open(consultation_json_filename, 'w', encoding='utf-8') as f:
                    json.dump(consultation_results, f, ensure_ascii=False, indent=2)
                logger.info(f"咨询问答JSON数据已保存到: {consultation_json_filename}")
            
            if save_format in ["csv", "both"]:
                # 准备咨询问答CSV数据
                consultation_csv_data = []
                for item in consultation_results:
                    metadata = item['metadata']
                    csv_row = {
                        'instruction': item['instruction'],
                        'input': item['input'],
                        'output': item['output'],
                        'dialogue_type': metadata.get('dialogue_type', '咨询问答'),
                        'generation_method': metadata.get('generation_method', '咨询生成'),
                        'customer_persona': metadata['customer_persona'],
                        'consultation_type': metadata.get('consultation_type', ''),
                        'consultation_topic': metadata.get('consultation_topic', ''),
                        'generation_time': metadata['generation_time'],
                        'total_duration': metadata['durations'].get('total', 0)
                    }
                    consultation_csv_data.append(csv_row)
                
                consultation_csv_filename = f"{base_filename}_consultation.csv"
                consultation_df = pd.DataFrame(consultation_csv_data)
                consultation_df.to_csv(consultation_csv_filename, index=False, encoding='utf-8')
                logger.info(f"咨询问答CSV数据已保存到: {consultation_csv_filename}")
        
        # 保存多轮对话数据
        if multi_results:
            if save_format in ["json", "both"]:
                multi_json_filename = f"{base_filename}_multi_turn.json"
                with open(multi_json_filename, 'w', encoding='utf-8') as f:
                    json.dump(multi_results, f, ensure_ascii=False, indent=2)
                logger.info(f"多轮对话JSON数据已保存到: {multi_json_filename}")
            
            if save_format in ["csv", "both"]:
                # 准备多轮对话CSV数据
                multi_csv_data = []
                for item in multi_results:
                    metadata = item['metadata']
                    dialogue_text = json.dumps(item['output'], ensure_ascii=False) if isinstance(item['output'], list) else str(item['output'])
                    
                    csv_row = {
                        'instruction': item['instruction'],
                        'input': f"多轮对话 - {metadata.get('dialogue_turns', 0)}轮",
                        'output': dialogue_text,
                        'dialogue_type': metadata.get('dialogue_type', '多轮对话'),
                        'generation_method': metadata.get('generation_method', '未知'),
                        'customer_persona': metadata['customer_persona'],
                        'scenario_type': metadata.get('scenario_type', ''),
                        'scenario_desc': metadata.get('scenario_desc', ''),
                        'dialogue_turns': metadata.get('dialogue_turns', 0),
                        'target_turns': metadata.get('target_turns', 0),
                        'multi_turn_method': metadata.get('multi_turn_method', ''),
                        'product_type': metadata.get('product_type', ''),
                        'case_complexity': metadata.get('quality_indicators', {}).get('case_complexity', ''),
                        'generation_time': metadata['generation_time'],
                        'total_duration': metadata['durations'].get('total', 0)
                    }
                    multi_csv_data.append(csv_row)
                
                multi_csv_filename = f"{base_filename}_multi_turn.csv"
                multi_df = pd.DataFrame(multi_csv_data)
                multi_df.to_csv(multi_csv_filename, index=False, encoding='utf-8')
                logger.info(f"多轮对话CSV数据已保存到: {multi_csv_filename}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="贷款咨询对话数据合成工具")
    
    # 生成控制
    parser.add_argument("--total_samples", type=int, default=20, help="生成的总样本数")
    parser.add_argument("--max_workers", type=int, default=20, help="并行线程数")
    
    # 数据类型分布控制
    parser.add_argument("--consultation_ratio", type=float, default=0.5, help="咨询问答比例")
    parser.add_argument("--multi_ratio", type=float, default=0.5, help="多轮对话比例")
    
    # 多轮对话生成方法控制
    parser.add_argument("--multi_turn_method", type=str, choices=["iterative", "direct"], 
                       default="direct", help="多轮对话生成方法：iterative=迭代生成，direct=直接生成")
    
    # 输出控制
    parser.add_argument("--save_format", type=str, choices=["json", "csv", "both"], 
                       default="json", help="保存格式")
    parser.add_argument("--output_file", type=str, default="data/synthesis/loan_dialogue_synthesis_samples", help="输出文件前缀")
    
    args = parser.parse_args()
    
    # 验证比例总和
    total_ratio = args.consultation_ratio + args.multi_ratio
    if abs(total_ratio - 1.0) > 0.01:
        logger.error(f"数据类型比例总和应为1.0，当前为{total_ratio}")
        sys.exit(1)
    
    try:
        # 创建数据合成器
        synthesizer = LoanDataSynthesizer(use_doubao=True)
        
        # 生成数据集
        results = synthesizer.generate_dataset(
            total_samples=args.total_samples,
            max_workers=args.max_workers,
            save_format=args.save_format,
            output_file=args.output_file,
            consultation_ratio=args.consultation_ratio,
            multi_ratio=args.multi_ratio,
            multi_turn_method=args.multi_turn_method
        )
        
        logger.info(f"贷款对话数据合成完成! 成功生成 {len(results)} 条高质量对话数据")
        logger.info(f"数据已分别保存为咨询问答和多轮对话两个文件")
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()