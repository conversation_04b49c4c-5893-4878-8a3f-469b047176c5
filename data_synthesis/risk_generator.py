# data_synthesis/risk_generator.py
import json
import random
import os
import threading
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import math
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, parse_llm_json

class RiskGenerator:
    """风险评估数据生成器"""
    
    def __init__(self):
        self.risk_levels = ["很低", "低", "中", "高", "很高"]
        
        self.industry_risks = {
            "IT互联网": "中", "金融": "低", "教育": "低", "医疗": "低",
            "服务业": "中", "制造业": "中", "政府机关": "很低", "自由职业": "高"
        }
        
        self.lock = threading.Lock()
        self.generated_count = 0
    
    def generate_risk_case_with_llm(self, case_id: str) -> Dict[str, Any]:
        """使用LLM生成风险评估案例"""
        customer_age = random.randint(22, 65)
        customer_income = random.randint(5000, 30000)
        credit_score = random.randint(350, 800)
        
        prompt = f"""生成一个风险评估案例，JSON格式返回：
        
        客户基础信息：
        - 年龄：{customer_age}岁
        - 月收入：{customer_income}元
        - 征信分数：{credit_score}分
        
        请生成包含以下字段的JSON：
        {{
            "case_id": "{case_id}",
            "customer_info": {{
                "age": {customer_age},
                "monthly_income": {customer_income},
                "credit_score": {credit_score},
                "occupation": "职业",
                "application_amount": "申请金额"
            }},
            "assessment_input": {{
                "financial_factors": {{"debt_to_income": "负债率", "employment_years": "工作年限"}},
                "credit_factors": {{"overdue_records": "逾期次数", "credit_history_length": "征信历史长度"}},
                "behavioral_factors": {{"payment_score": "还款行为评分", "consumption_stability": "消费稳定性"}}
            }},
            "assessment_result": {{
                "total_risk_score": "总风险评分",
                "risk_level": "风险等级",
                "default_probability": "违约概率",
                "risk_factors": ["主要风险因子"],
                "mitigation_suggestions": ["风险缓释建议"]
            }},
            "business_decision": {{
                "final_decision": "最终决策",
                "approved_amount": "批准金额",
                "interest_rate": "利率",
                "conditions": ["审批条件"]
            }}
        }}"""
        
        try:
            response = call_doubao_api_prompt(prompt)
            case_data = parse_llm_json(response)
            
            with self.lock:
                self.generated_count += 1
                if self.generated_count % 10 == 0:
                    print(f"已生成 {self.generated_count} 个风险案例...")
            
            return case_data
        except Exception as e:
            print(f"生成风险案例 {case_id} 失败: {e}")
            return self._generate_fallback_risk_case(case_id, customer_age, customer_income, credit_score)
    
    def _generate_fallback_risk_case(self, case_id: str, age: int, income: int, credit_score: int) -> Dict[str, Any]:
        """生成备用风险案例"""
        # 简化的风险评分计算
        risk_score = 50
        if credit_score >= 750: risk_score += 25
        elif credit_score >= 650: risk_score += 10
        if income >= 15000: risk_score += 20
        elif income >= 10000: risk_score += 10
        
        risk_level = self._determine_risk_level(risk_score)
        
        return {
            "case_id": case_id,
            "customer_info": {
                "age": age,
                "monthly_income": income,
                "credit_score": credit_score,
                "occupation": random.choice(list(self.industry_risks.keys())),
                "application_amount": random.randint(50000, 500000)
            },
            "assessment_input": {
                "financial_factors": {
                    "debt_to_income": round(random.uniform(0.1, 0.8), 2),
                    "employment_years": random.randint(1, 15)
                },
                "credit_factors": {
                    "overdue_records": random.randint(0, 10),
                    "credit_history_length": random.randint(1, 20)
                },
                "behavioral_factors": {
                    "payment_score": random.randint(60, 100),
                    "consumption_stability": round(random.uniform(0.5, 1.0), 2)
                }
            },
            "assessment_result": {
                "total_risk_score": risk_score,
                "risk_level": risk_level,
                "default_probability": self._estimate_default_probability(risk_score),
                "risk_factors": self._identify_risk_factors(credit_score, income),
                "mitigation_suggestions": self._get_mitigation_suggestions(risk_level)
            },
            "business_decision": {
                "final_decision": "批准" if risk_score >= 60 else "拒绝",
                "approved_amount": random.randint(30000, 300000) if risk_score >= 60 else 0,
                "interest_rate": round(random.uniform(5.0, 15.0), 2) if risk_score >= 60 else None,
                "conditions": ["按时还款", "定期回访"] if risk_score >= 60 else []
            }
        }
    
    def _determine_risk_level(self, score: float) -> str:
        """确定风险等级"""
        if score >= 80: return "很低"
        elif score >= 65: return "低"
        elif score >= 45: return "中"
        elif score >= 25: return "高"
        else: return "很高"
    
    def _estimate_default_probability(self, score: float) -> float:
        """估算违约概率"""
        odds = math.exp((50 - score) / 20)
        probability = odds / (1 + odds)
        return round(max(0.001, min(0.500, probability)), 4)
    
    def _identify_risk_factors(self, credit_score: int, income: int) -> List[str]:
        """识别风险因子"""
        factors = []
        if credit_score < 650: factors.append("征信分数偏低")
        if income < 8000: factors.append("收入水平偏低")
        return factors or ["无明显风险因子"]
    
    def _get_mitigation_suggestions(self, risk_level: str) -> List[str]:
        """获取缓释建议"""
        suggestions_map = {
            "很高": ["拒绝申请", "建议提供担保"],
            "高": ["降低授信额度", "提高利率"],
            "中": ["标准条件审批", "加强贷后管理"],
            "低": ["适当提高额度", "优惠利率"],
            "很低": ["VIP客户待遇", "最优条件"]
        }
        return suggestions_map.get(risk_level, ["标准处理"])
    
    def generate_concurrent(self, case_count: int = 50, max_workers: int = 8) -> List[Dict[str, Any]]:
        """并发生成风险案例数据"""
        print(f"开始生成风险评估案例: {case_count}个案例...")
        
        cases = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交案例生成任务
            case_futures = {
                executor.submit(self.generate_risk_case_with_llm, f"RISK{i+1:05d}"): f"RISK{i+1:05d}"
                for i in range(case_count)
            }
            
            # 收集案例结果
            for future in as_completed(case_futures):
                try:
                    case_data = future.result(timeout=60)
                    if case_data:
                        cases.append(case_data)
                except Exception as e:
                    case_id = case_futures[future]
                    print(f"案例 {case_id} 生成失败: {e}")
        
        return cases
    
    
    def save_data(self, cases: List[Dict], filename: str = None):
        """保存风险案例数据"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"risk_cases_{timestamp}.json"

        output_dir = "data/synthesis"
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存案例数据
        case_file = os.path.join(output_dir, filename)
        with open(case_file, 'w', encoding='utf-8') as f:
            json.dump(cases, f, ensure_ascii=False, indent=2)
        print(f"已保存 {len(cases)} 个风险案例到 {case_file}")

if __name__ == "__main__":
    generator = RiskGenerator()
    cases = generator.generate_concurrent(case_count=20, max_workers=6)
    generator.save_data(cases, "risk_cases_samples.json")