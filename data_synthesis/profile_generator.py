# data_synthesis/profile_generator.py
import json
import random
import os
import threading
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, parse_llm_json

class ProfileGenerator:
    """客户画像数据生成器"""
    
    def __init__(self):
        self.occupations = {
            "IT互联网": ["软件工程师", "产品经理", "UI设计师", "数据分析师"],
            "金融": ["银行职员", "保险销售", "投资顾问", "财务分析师"],
            "教育": ["中学教师", "大学教授", "培训讲师", "教育顾问"],
            "医疗": ["医生", "护士", "药剂师", "医技人员"],
            "服务业": ["餐饮经理", "销售代表", "客服主管", "物业管理"],
            "制造业": ["工程师", "技术员", "质检员", "生产主管"],
            "政府机关": ["公务员", "事业单位", "国企员工", "军人"],
            "自由职业": ["自媒体", "设计师", "翻译", "咨询师"]
        }
        
        self.city_tiers = {
            "一线城市": ["北京", "上海", "广州", "深圳"],
            "新一线城市": ["成都", "杭州", "武汉", "西安", "苏州"],
            "二线城市": ["南京", "青岛", "大连", "厦门", "宁波"],
            "三线城市": ["海口", "兰州", "银川", "西宁", "石家庄"]
        }
        
        self.lock = threading.Lock()
        self.generated_count = 0
    
    def generate_profile_with_llm(self, profile_id: str) -> Dict[str, Any]:
        """使用LLM生成客户画像"""
        age = random.randint(22, 65)
        occupation_cat = random.choice(list(self.occupations.keys()))
        city_tier = random.choice(list(self.city_tiers.keys()))
        city = random.choice(self.city_tiers[city_tier])
        
        prompt = f"""生成一个详细的客户画像，JSON格式返回：
        
        基础设定：
        - 年龄：{age}岁
        - 职业类别：{occupation_cat}
        - 城市：{city}（{city_tier}）
        
        请生成包含以下字段的JSON：
        {{
            "profile_id": "{profile_id}",
            "basic_info": {{
                "age": {age},
                "gender": "性别",
                "education": "学历",
                "occupation": "具体职业",
                "city": "{city}",
                "city_tier": "{city_tier}",
                "marital_status": "婚姻状况"
            }},
            "income_info": {{
                "monthly_income": "月收入",
                "annual_income": "年收入",
                "income_stability": "收入稳定性",
                "income_sources": ["收入来源"]
            }},
            "assets": {{
                "total_assets": "总资产",
                "liquid_assets": "流动资产",
                "real_estate_value": "房产价值",
                "vehicle_value": "车辆价值",
                "investment_value": "投资价值"
            }},
            "debts": {{
                "total_debts": "总负债",
                "mortgage": "房贷余额",
                "car_loan": "车贷余额",
                "credit_card": "信用卡负债",
                "debt_to_income": "负债收入比"
            }},
            "credit_info": {{
                "credit_score": "征信分数",
                "credit_level": "征信等级",
                "overdue_records": "逾期记录数",
                "credit_cards_count": "信用卡数量",
                "credit_limit_total": "总信用额度"
            }},
            "risk_assessment": {{
                "risk_level": "风险等级",
                "risk_score": "风险评分",
                "risk_factors": ["风险因素"],
                "recommendation": "建议"
            }},
            "profile_tags": ["客户标签"]
        }}"""
        
        try:
            response = call_doubao_api_prompt(prompt)
            profile_data = parse_llm_json(response)
            
            with self.lock:
                self.generated_count += 1
                if self.generated_count % 10 == 0:
                    print(f"已生成 {self.generated_count} 个客户画像...")
            
            return profile_data
        except Exception as e:
            print(f"生成客户画像 {profile_id} 失败: {e}")
            return self._generate_fallback_profile(profile_id, age, occupation_cat, city, city_tier)
    
    def _generate_fallback_profile(self, profile_id: str, age: int, occupation_cat: str, 
                                  city: str, city_tier: str) -> Dict[str, Any]:
        """生成备用客户画像"""
        # 根据职业生成收入
        income_base_map = {
            "IT互联网": 12000, "金融": 10000, "教育": 7000, "医疗": 9000,
            "服务业": 6000, "制造业": 7500, "政府机关": 8000, "自由职业": 8500
        }
        
        base_income = income_base_map.get(occupation_cat, 6000)
        monthly_income = int(base_income * random.uniform(0.7, 1.8))
        credit_score = random.randint(550, 850)
        
        # 生成资产
        total_assets = monthly_income * random.uniform(10, 50)
        house_value = self._estimate_house_value(city_tier, monthly_income)
        
        return {
            "profile_id": profile_id,
            "basic_info": {
                "age": age,
                "gender": random.choice(["男", "女"]),
                "education": random.choice(["高中", "大专", "本科", "硕士", "博士"]),
                "occupation": random.choice(self.occupations[occupation_cat]),
                "city": city,
                "city_tier": city_tier,
                "marital_status": random.choice(["未婚", "已婚", "离异"])
            },
            "income_info": {
                "monthly_income": monthly_income,
                "annual_income": monthly_income * 12,
                "income_stability": self._assess_income_stability(occupation_cat),
                "income_sources": ["工资收入"]
            },
            "assets": {
                "total_assets": int(total_assets),
                "liquid_assets": int(monthly_income * random.uniform(1, 5)),
                "real_estate_value": house_value,
                "vehicle_value": random.randint(0, 300000) if random.random() < 0.6 else 0,
                "investment_value": int(monthly_income * random.uniform(0, 10))
            },
            "debts": {
                "total_debts": int(monthly_income * random.uniform(5, 30)),
                "mortgage": int(house_value * random.uniform(0.2, 0.7)) if house_value > 0 else 0,
                "car_loan": random.randint(0, 100000),
                "credit_card": random.randint(0, int(monthly_income * 2)),
                "debt_to_income": round(random.uniform(0.1, 0.7), 2)
            },
            "credit_info": {
                "credit_score": credit_score,
                "credit_level": self._classify_credit_level(credit_score),
                "overdue_records": random.randint(0, 5),
                "credit_cards_count": random.randint(1, 8),
                "credit_limit_total": random.randint(10000, 200000)
            },
            "risk_assessment": {
                "risk_level": self._assess_risk_level(credit_score, monthly_income),
                "risk_score": random.randint(30, 90),
                "risk_factors": self._identify_risk_factors(credit_score, monthly_income),
                "recommendation": "建议常规审批"
            },
            "profile_tags": self._generate_tags(age, monthly_income, city_tier)
        }
    
    def _estimate_house_value(self, city_tier: str, income: int) -> int:
        """估算房产价值"""
        base_values = {"一线城市": 5000000, "新一线城市": 2000000, "二线城市": 1000000, "三线城市": 500000}
        base_value = base_values.get(city_tier, 800000)
        
        if random.random() < 0.7:  # 70%概率有房
            return int(base_value * random.uniform(0.6, 1.4))
        return 0
    
    def _assess_income_stability(self, occupation_cat: str) -> str:
        """评估收入稳定性"""
        stability_map = {
            "政府机关": "很稳定", "教育": "稳定", "医疗": "稳定",
            "金融": "较稳定", "IT互联网": "较稳定", "制造业": "一般",
            "服务业": "一般", "自由职业": "不稳定"
        }
        return stability_map.get(occupation_cat, "一般")
    
    def _classify_credit_level(self, credit_score: int) -> str:
        """分类征信等级"""
        if credit_score >= 750: return "优秀"
        elif credit_score >= 700: return "良好"
        elif credit_score >= 650: return "一般"
        elif credit_score >= 600: return "较差"
        else: return "差"
    
    def _assess_risk_level(self, credit_score: int, income: int) -> str:
        """评估风险等级"""
        if credit_score >= 750 and income >= 15000: return "低风险"
        elif credit_score >= 650 and income >= 8000: return "中风险"
        else: return "高风险"
    
    def _identify_risk_factors(self, credit_score: int, income: int) -> List[str]:
        """识别风险因素"""
        factors = []
        if credit_score < 650: factors.append("征信分数偏低")
        if income < 8000: factors.append("收入水平偏低")
        return factors or ["无明显风险因素"]
    
    def _generate_tags(self, age: int, income: int, city_tier: str) -> List[str]:
        """生成客户标签"""
        tags = []
        if age < 30: tags.append("年轻客户")
        elif age > 50: tags.append("成熟客户")
        
        if income > 20000: tags.append("高收入")
        elif income > 10000: tags.append("中高收入")
        
        if city_tier in ["一线城市", "新一线城市"]: tags.append("一线城市")
        
        return tags
    
    def generate_profiles_concurrent(self, total_count: int = 100, max_workers: int = 10) -> List[Dict[str, Any]]:
        """并发生成客户画像"""
        print(f"开始并发生成 {total_count} 个客户画像，使用 {max_workers} 个线程...")
        
        profiles = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_id = {
                executor.submit(self.generate_profile_with_llm, f"PROFILE{i+1:05d}"): f"PROFILE{i+1:05d}"
                for i in range(total_count)
            }
            
            # 收集结果
            for future in as_completed(future_to_id):
                try:
                    profile_data = future.result(timeout=60)
                    if profile_data:
                        profiles.append(profile_data)
                except Exception as e:
                    profile_id = future_to_id[future]
                    print(f"客户画像 {profile_id} 生成失败: {e}")
        
        print(f"成功生成 {len(profiles)} 个客户画像")
        return profiles
    
    def save_profiles(self, profiles: List[Dict[str, Any]], filename: str = None):
        """保存客户画像数据"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"customer_profiles_{timestamp}.json"
        
        # 确保目录存在
        output_dir = "data/synthesis"
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(profiles, f, ensure_ascii=False, indent=2)
        
        print(f"已保存 {len(profiles)} 个客户画像到 {filepath}")

if __name__ == "__main__":
    generator = ProfileGenerator()
    
    # 生成客户画像
    profiles = generator.generate_profiles_concurrent(total_count=30, max_workers=8)
    
    # 显示示例
    for i, profile in enumerate(profiles[:3]):
        basic = profile.get('basic_info', {})
        income = profile.get('income_info', {})
        risk = profile.get('risk_assessment', {})
        print(f"客户{i+1}: {basic.get('age', 'N/A')}岁 {basic.get('occupation', 'N/A')}, "
              f"月收入{income.get('monthly_income', 'N/A')}, 风险等级: {risk.get('risk_level', 'N/A')}")
    
    # 保存数据
    generator.save_profiles(profiles, "profile_samples.json")