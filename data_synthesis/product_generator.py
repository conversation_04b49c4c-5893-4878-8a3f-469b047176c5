# data_synthesis/product_generator.py
import random
import json
import os
import threading
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, parse_llm_json

class ProductGenerator:
    """金融产品数据合成器"""
    
    def __init__(self):
        self.institutions = ["工商银行", "建设银行", "农业银行", "中国银行", "招商银行", 
                           "平安银行", "交通银行", "邮储银行", "民生银行", "光大银行"]
        
        self.product_types = {
            "消费贷": {"rate_range": (4.35, 18.0), "amount_range": (10000, 500000), "term_range": (6, 60)},
            "抵押贷": {"rate_range": (3.85, 8.5), "amount_range": (100000, 10000000), "term_range": (12, 360)},
            "经营贷": {"rate_range": (4.5, 12.0), "amount_range": (50000, 5000000), "term_range": (12, 60)},
            "信用卡": {"rate_range": (5.0, 24.0), "amount_range": (5000, 200000), "term_range": (1, 12)},
            "房贷": {"rate_range": (3.2, 6.5), "amount_range": (500000, 20000000), "term_range": (120, 360)},
            "车贷": {"rate_range": (4.0, 10.0), "amount_range": (50000, 1000000), "term_range": (12, 60)}
        }
        
        self.lock = threading.Lock()
        self.generated_count = 0
    
    def generate_product_with_llm(self, product_id: str) -> Dict[str, Any]:
        """使用LLM生成金融产品"""
        product_type = random.choice(list(self.product_types.keys()))
        institution = random.choice(self.institutions)
        type_config = self.product_types[product_type]
        
        prompt = f"""生成一个真实的{product_type}产品信息，JSON格式返回：
        
        产品设定：
        - 产品类型：{product_type}
        - 发行机构：{institution}
        - 利率范围：{type_config['rate_range'][0]}%-{type_config['rate_range'][1]}%
        - 额度范围：{type_config['amount_range'][0]}元-{type_config['amount_range'][1]}元
        
        请生成包含以下字段的JSON：
        {{
            "product_id": "{product_id}",
            "name": "{institution}{product_type}",
            "type": "{product_type}",
            "institution": "{institution}",
            "interest_rate": {{
                "min": "最低利率",
                "max": "最高利率",
                "type": "年化利率",
                "adjustment_factors": ["影响利率的因素"]
            }},
            "features": {{
                "amount_range": "额度范围",
                "term_range": "期限范围",
                "repayment_methods": ["还款方式"],
                "special_features": ["产品特色"],
                "target_customers": ["目标客户群"]
            }},
            "requirements": {{
                "basic_requirements": {{
                    "age_range": "年龄要求",
                    "income_requirement": "收入要求",
                    "credit_score": "征信要求",
                    "employment_years": "工作年限要求"
                }},
                "required_documents": ["所需材料"],
                "additional_conditions": ["其他条件"]
            }},
            "approval_info": {{
                "approval_rate": "通过率",
                "avg_approval_time": "平均审批时间",
                "approval_process": ["审批流程"]
            }},
            "fees": {{
                "application_fee": "申请费",
                "management_fee": "管理费",
                "early_repayment_fee": "提前还款费",
                "other_fees": ["其他费用"]
            }}
        }}"""
        
        try:
            response = call_doubao_api_prompt(prompt)
            product_data = parse_llm_json(response)
            
            with self.lock:
                self.generated_count += 1
                if self.generated_count % 10 == 0:
                    print(f"已生成 {self.generated_count} 个产品...")
            
            return product_data
        except Exception as e:
            print(f"生成产品 {product_id} 失败: {e}")
            return self._generate_fallback_product(product_id, product_type, institution, type_config)
    
    def _generate_fallback_product(self, product_id: str, product_type: str, 
                                  institution: str, type_config: Dict) -> Dict[str, Any]:
        """生成备用产品"""
        min_rate = round(random.uniform(*type_config["rate_range"][::-1]), 2)
        max_rate = round(random.uniform(*type_config["rate_range"]), 2)
        if min_rate > max_rate:
            min_rate, max_rate = max_rate, min_rate
        
        return {
            "product_id": product_id,
            "name": f"{institution}{product_type}",
            "type": product_type,
            "institution": institution,
            "interest_rate": {
                "min": min_rate,
                "max": max_rate,
                "type": "年化利率",
                "adjustment_factors": ["个人资质", "贷款期限", "担保方式"]
            },
            "features": {
                "amount_range": f"{type_config['amount_range'][0]//10000}万-{type_config['amount_range'][1]//10000}万元",
                "term_range": f"{type_config['term_range'][0]}-{type_config['term_range'][1]}个月",
                "repayment_methods": ["等额本息", "等额本金"],
                "special_features": self._get_special_features(product_type),
                "target_customers": self._get_target_customers(product_type)
            },
            "requirements": {
                "basic_requirements": {
                    "age_range": f"{random.randint(18, 25)}-{random.randint(55, 65)}岁",
                    "income_requirement": f"月收入≥{random.randint(3000, 8000)}元",
                    "credit_score": f"≥{random.randint(600, 700)}分",
                    "employment_years": f"≥{random.choice([0.5, 1, 2])}年"
                },
                "required_documents": ["身份证", "收入证明", "银行流水", "征信报告"],
                "additional_conditions": self._get_additional_conditions(product_type)
            },
            "approval_info": {
                "approval_rate": f"{random.randint(60, 90)}%",
                "avg_approval_time": f"{random.randint(1, 10)}个工作日",
                "approval_process": ["资料审核", "征信查询", "电话回访", "放款"]
            },
            "fees": {
                "application_fee": f"{random.randint(0, 500)}元",
                "management_fee": f"{random.uniform(0, 2):.2f}%/年",
                "early_repayment_fee": f"{random.uniform(0, 3):.2f}%",
                "other_fees": ["账户管理费", "逾期罚息"]
            }
        }
    
    def _get_special_features(self, product_type: str) -> List[str]:
        """获取产品特色"""
        features_map = {
            "消费贷": ["快速审批", "用途灵活", "无需抵押"],
            "抵押贷": ["额度高", "利率低", "期限长"],
            "经营贷": ["专为企业设计", "放款快", "循环使用"],
            "信用卡": ["积分奖励", "免息期", "分期付款"],
            "房贷": ["首套房优惠", "公积金组合", "等额递减"],
            "车贷": ["车辆抵押", "快速放款", "新车二手车均可"]
        }
        return features_map.get(product_type, ["标准产品"])
    
    def _get_target_customers(self, product_type: str) -> List[str]:
        """获取目标客户"""
        customers_map = {
            "消费贷": ["工薪族", "自由职业者", "小微企业主"],
            "抵押贷": ["有房人士", "高净值客户", "企业主"],
            "经营贷": ["小微企业", "个体工商户", "创业者"],
            "信用卡": ["年轻群体", "都市白领", "商务人士"],
            "房贷": ["刚需购房者", "改善型购房者", "投资客"],
            "车贷": ["购车族", "换车族", "商用车主"]
        }
        return customers_map.get(product_type, ["一般客户"])
    
    def _get_additional_conditions(self, product_type: str) -> List[str]:
        """获取附加条件"""
        conditions_map = {
            "消费贷": ["用途明确", "本地居住或工作"],
            "抵押贷": ["提供房产证", "房产评估"],
            "经营贷": ["营业执照", "经营流水"],
            "信用卡": ["良好征信记录"],
            "房贷": ["首付比例", "购房合同"],
            "车贷": ["车辆登记证", "保险单"]
        }
        return conditions_map.get(product_type, ["符合银行要求"])
    
    def generate_products_concurrent(self, total_count: int = 50, max_workers: int = 10) -> List[Dict[str, Any]]:
        """并发生成产品数据"""
        print(f"开始并发生成 {total_count} 个金融产品，使用 {max_workers} 个线程...")
        
        products = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_id = {
                executor.submit(self.generate_product_with_llm, f"PRODUCT{i+1:05d}"): f"PRODUCT{i+1:05d}"
                for i in range(total_count)
            }
            
            # 收集结果
            for future in as_completed(future_to_id):
                try:
                    product_data = future.result(timeout=60)
                    if product_data:
                        products.append(product_data)
                except Exception as e:
                    product_id = future_to_id[future]
                    print(f"产品 {product_id} 生成失败: {e}")
        
        print(f"成功生成 {len(products)} 个金融产品")
        return products
    
    def save_products(self, products: List[Dict[str, Any]], filename: str = None):
        """保存产品数据"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"loan_products_{timestamp}.json"
        
        # 确保目录存在
        output_dir = "data/synthesis"
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(products, f, ensure_ascii=False, indent=2)
        
        print(f"已保存 {len(products)} 个产品到 {filepath}")

if __name__ == "__main__":
    generator = ProductGenerator()
    
    # 生成产品数据
    products = generator.generate_products_concurrent(total_count=20, max_workers=10)
    
    # 显示示例
    for i, product in enumerate(products[:3]):
        print(f"产品{i+1}: {product.get('name', 'N/A')}, "
              f"类型: {product.get('type', 'N/A')}, "
              f"利率: {product.get('interest_rate', {}).get('min', 'N/A')}-{product.get('interest_rate', {}).get('max', 'N/A')}%")
    
    # 保存数据
    generator.save_products(products, "products_samples.json")