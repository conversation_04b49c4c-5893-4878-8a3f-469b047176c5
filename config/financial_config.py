# config/financial_config.py
from typing import Dict, List, Any
from dataclasses import dataclass
from .base_config import BaseConfig

@dataclass
class ProductConfig:
    """金融产品配置"""
    product_types: List[str] = None
    institutions: List[str] = None
    rate_ranges: Dict[str, tuple] = None
    amount_ranges: Dict[str, tuple] = None
    
    def __post_init__(self):
        if self.product_types is None:
            self.product_types = ["消费贷", "抵押贷", "经营贷", "信用卡", "房贷", "车贷"]
        
        if self.institutions is None:
            self.institutions = ["工商银行", "建设银行", "农业银行", "中国银行", "招商银行", "平安银行"]
        
        if self.rate_ranges is None:
            self.rate_ranges = {
                "消费贷": (4.35, 18.0),
                "抵押贷": (3.85, 8.5),
                "经营贷": (4.5, 12.0),
                "信用卡": (5.0, 24.0),
                "房贷": (3.2, 6.5),
                "车贷": (4.0, 10.0)
            }
        
        if self.amount_ranges is None:
            self.amount_ranges = {
                "消费贷": (10000, 500000),
                "抵押贷": (100000, 10000000),
                "经营贷": (50000, 5000000),
                "信用卡": (5000, 200000),
                "房贷": (100000, 20000000),
                "车贷": (50000, 1000000)
            }

@dataclass
class RiskConfig:
    """风险评估配置"""
    risk_levels: List[str] = None
    score_ranges: Dict[str, tuple] = None
    default_rates: Dict[str, tuple] = None
    factors: Dict[str, float] = None
    
    def __post_init__(self):
        if self.risk_levels is None:
            self.risk_levels = ["低风险", "中风险", "高风险", "极高风险"]
        
        if self.score_ranges is None:
            self.score_ranges = {
                "低风险": (80, 100),
                "中风险": (60, 79),
                "高风险": (40, 59),
                "极高风险": (0, 39)
            }
        
        if self.default_rates is None:
            self.default_rates = {
                "低风险": (0.01, 0.03),
                "中风险": (0.03, 0.08),
                "高风险": (0.08, 0.15),
                "极高风险": (0.15, 0.30)
            }
        
        if self.factors is None:
            self.factors = {
                "credit_score": 0.30,
                "monthly_income": 0.25,
                "debt_to_income": 0.20,
                "employment_years": 0.15,
                "age": 0.10
            }

@dataclass
class ComplianceConfig:
    """合规配置"""
    required_documents: List[str] = None
    regulatory_requirements: Dict[str, str] = None
    risk_warnings: List[str] = None
    
    def __post_init__(self):
        if self.required_documents is None:
            self.required_documents = [
                "身份证", "收入证明", "征信报告", "银行流水", 
                "工作证明", "房产证明", "学历证明"
            ]
        
        if self.regulatory_requirements is None:
            self.regulatory_requirements = {
                "用途审查": "贷款用途必须明确、合法",
                "还款能力评估": "贷款金额应与借款人还款能力匹配",
                "征信审查": "严格审查借款人征信状况",
                "贷后管理": "建立完善的贷后管理制度"
            }
        
        if self.risk_warnings is None:
            self.risk_warnings = [
                "借贷有风险，申请需谨慎",
                "请根据自身还款能力选择合适的贷款产品",
                "逾期还款将影响个人征信记录",
                "请仔细阅读贷款合同条款"
            ]

class FinancialConfig(BaseConfig):
    """金融业务配置"""
    
    def __init__(self):
        super().__init__()
        self.product = ProductConfig()
        self.risk = RiskConfig()
        self.compliance = ComplianceConfig()
        
        # 金融专业术语
        self.financial_terms = self._load_financial_terms()
        
        # 意图分类
        self.intent_categories = self._load_intent_categories()
        
        # 模板配置
        self.templates = self._load_templates()
    
    def _load_financial_terms(self) -> Dict[str, List[str]]:
        """加载金融专业术语"""
        return {
            "贷款类型": ["消费贷", "抵押贷", "经营贷", "信用贷", "房贷", "车贷"],
            "利率术语": ["年化利率", "月利率", "实际利率", "基准利率", "浮动利率"],
            "征信术语": ["征信报告", "信用评分", "逾期记录", "负债率", "还款记录"],
            "风险术语": ["风险等级", "违约率", "风险评估", "风险控制", "风险预警"],
            "合规术语": ["监管要求", "合规审查", "风险提示", "信息披露", "反洗钱"]
        }
    
    def _load_intent_categories(self) -> Dict[str, List[str]]:
        """加载意图分类"""
        return {
            "产品咨询": ["产品信息", "申请条件", "利率查询", "额度查询", "期限查询"],
            "资质评估": ["个人资质", "风险评估", "成功率预测", "额度预估"],
            "比较分析": ["产品对比", "利率比较", "条件比较", "机构比较"],
            "申请指导": ["申请流程", "材料准备", "操作指导", "注意事项"],
            "合规咨询": ["政策解读", "合规要求", "风险提示", "法规说明"]
        }
    
    def _load_templates(self) -> Dict[str, str]:
        """加载回答模板"""
        return {
            "产品介绍": "{product_name}是{institution}推出的{product_type}，年化利率{rate_range}，额度{amount_range}，期限{term_range}。主要特点：{features}。适合客户：{target_customers}。",
            "风险评估": "根据您的情况分析：个人资质{qualification}，风险等级{risk_level}，申请成功率约{success_rate}，建议产品{recommended_products}。注意事项：{risk_warnings}。",
            "产品对比": "产品对比分析：{comparison_table}。推荐理由：{recommendation_reason}。选择建议：{selection_advice}。",
            "申请指导": "申请流程：{application_process}。所需材料：{required_documents}。注意事项：{precautions}。预估时间：{estimated_time}。",
            "合规提示": "根据监管要求：{regulatory_requirements}。风险提示：{risk_warnings}。合规建议：{compliance_advice}。"
        }

if __name__ == "__main__":
    # 测试金融配置
    config = FinancialConfig()
    
    # 测试产品配置
    print("产品类型:", config.product.product_types)
    print("金融机构:", config.product.institutions)
    print("消费贷利率范围:", config.product.rate_ranges["消费贷"])
    
    # 测试风险配置
    print("\n风险等级:", config.risk.risk_levels)
    print("低风险评分范围:", config.risk.score_ranges["低风险"])
    print("风险因子权重:", config.risk.factors)
    
    # 测试金融术语
    print("\n贷款类型术语:", config.financial_terms["贷款类型"])
    
    # 测试意图分类
    print("\n产品咨询意图:", config.intent_categories["产品咨询"])
    
    # 测试模板
    print("\n产品介绍模板:")
    print(config.templates["产品介绍"])