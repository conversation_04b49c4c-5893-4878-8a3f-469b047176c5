# config/base_config.py
import os
from dataclasses import dataclass
from typing import Dict, Any, List

@dataclass
class EmbeddingConfig:
    """向量化配置"""
    model_name: str = "doubao-embedding-text-240715"
    dimension: int = 1536
    batch_size: int = 100
    max_length: int = 512

@dataclass
class RetrievalConfig:
    """检索配置"""
    vector_weight: float = 0.5
    bm25_weight: float = 0.3
    financial_weight: float = 0.2
    top_k: int = 10
    similarity_threshold: float = 0.6

@dataclass
class LLMConfig:
    """大模型配置"""
    model_name: str = "doubao-1-5-pro-32k-250115"
    max_tokens: int = 4000
    temperature: float = 0.1
    timeout: int = 30

@dataclass
class DatabaseConfig:
    """数据库配置"""
    vector_db_path: str = "./data/knowledge_base/vector_db"
    knowledge_db_path: str = "./data/knowledge_base/knowledge.db"
    index_path: str = "./data/knowledge_base/index"

class BaseConfig:
    """基础配置类"""
    
    def __init__(self):
        self.embedding = EmbeddingConfig()
        self.retrieval = RetrievalConfig()
        self.llm = LLMConfig()
        self.database = DatabaseConfig()
        
        # 系统配置
        self.debug = os.getenv("DEBUG", "False").lower() == "true"
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        
        # 数据路径
        self.data_dir = "./data"
        self.output_dir = "./output"
        self.cache_dir = "./cache"
        
        # 创建必要目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.data_dir,
            self.output_dir,
            self.cache_dir,
            self.database.vector_db_path,
            os.path.dirname(self.database.knowledge_db_path),
            self.database.index_path
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取配置字典"""
        return {
            "embedding": self.embedding.__dict__,
            "retrieval": self.retrieval.__dict__,
            "llm": self.llm.__dict__,
            "database": self.database.__dict__,
            "debug": self.debug,
            "log_level": self.log_level
        }
    
    def update_config(self, config_dict: Dict[str, Any]):
        """更新配置"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                if isinstance(getattr(self, key), object) and hasattr(getattr(self, key), '__dict__'):
                    # 更新子配置对象
                    for sub_key, sub_value in value.items():
                        setattr(getattr(self, key), sub_key, sub_value)
                else:
                    setattr(self, key, value)

if __name__ == "__main__":
    # 测试配置类
    config = BaseConfig()
    
    # 打印配置
    print("基础配置:")
    config_dict = config.get_config_dict()
    for key, value in config_dict.items():
        print(f"{key}: {value}")
    
    # 测试配置更新
    update_dict = {
        "embedding": {"dimension": 768},
        "retrieval": {"top_k": 20},
        "debug": True
    }
    config.update_config(update_dict)
    
    print("\n更新后配置:")
    print(f"Embedding dimension: {config.embedding.dimension}")
    print(f"Retrieval top_k: {config.retrieval.top_k}")
    print(f"Debug mode: {config.debug}")