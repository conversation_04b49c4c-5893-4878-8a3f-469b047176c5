# 金融贷款咨询RAG问答系统

## 项目概述

本项目是一个基于RAG（Retrieval-Augmented Generation，检索增强生成）技术的智能金融贷款咨询系统。该系统通过构建专业金融知识库，结合先进的检索和生成技术，为用户提供准确、专业、合规的金融贷款咨询服务。

### 核心特性

- 🔍 **智能检索**：混合向量检索和BM25检索，确保检索精度
- 🧠 **专业问答**：基于金融专业知识的智能问答系统
- ⚖️ **合规保障**：内置合规检查机制，确保回复符合监管要求
- 📊 **风险评估**：客户风险分析和产品匹配推荐
- 🔄 **多轮对话**：支持上下文理解的多轮对话能力
- 📈 **质量评估**：完整的对话质量评估体系
- 🛡️ **冲突解决**：自动检测和解决信息冲突

## 系统架构

```
financial-loan-consultant-rag-system/
├── config/                    # 配置模块
├── data_synthesis/           # 数据合成模块
├── knowledge_base/           # 知识库构建模块
├── retrieval/               # 检索模块
├── rag_system/              # RAG核心模块
├── dialog_evaluator.py      # 对话评估器
├── financial_analyzer.py    # 金融分析器
├── llm_api.py              # 大模型API接口
└── main.py                 # 主程序入口
```

## 技术栈

- **Python 3.8+**
- **向量数据库**：支持FAISS、Chroma等
- **大语言模型**：支持豆包、GPT、Claude等主流模型
- **文本处理**：Transformers、sentence-transformers
- **数据处理**：Pandas、NumPy
- **API框架**：FastAPI（可选）

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd financial-loan-consultant-rag-system

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置设置

复制并编辑配置文件：

```bash
cp config/base_config.py config/local_config.py
```

编辑 `config/local_config.py`，设置必要的参数：

```python
# API配置
DOUBAO_API_KEY = "your_api_key_here"
DOUBAO_BASE_URL = "https://ark.cn-beijing.volces.com/api/v3"

# 向量数据库配置
VECTOR_DB_TYPE = "faiss"  # 或 "chroma"
EMBEDDING_MODEL = "bge-large-zh"

# 其他配置...
```

### 3. 数据准备

生成示例金融数据：

```bash
python -c "
from data_synthesis.product_generator import ProductGenerator
from data_synthesis.case_generator import CaseGenerator

# 生成产品数据
product_gen = ProductGenerator()
products = product_gen.generate_product_data(50)

# 生成案例数据
case_gen = CaseGenerator()
cases = case_gen.generate_case_data(100)

print('数据生成完成')
"
```

### 4. 构建知识库

```bash
python -c "
from knowledge_base.knowledge_base_manager import KnowledgeBaseManager

# 构建知识库
kb_manager = KnowledgeBaseManager()
kb_manager.build_knowledge_base()

print('知识库构建完成')
"
```

### 5. 运行系统

```bash
python main.py
```

## 详细功能介绍

### 数据合成模块

系统提供完整的金融数据合成功能：

- **产品信息生成**：贷款产品的详细信息
- **客户案例生成**：真实的客户申请和审批案例
- **对话数据生成**：客服咨询对话记录
- **政策文件生成**：监管政策和合规要求
- **风险评估生成**：风险模型和评估案例

### 知识库构建

智能的知识库管理系统：

- **多源数据整合**：整合产品、案例、政策等多种数据源
- **智能文档切分**：根据语义和逻辑结构切分文档
- **向量化索引**：构建高效的向量检索索引
- **增量更新**：支持知识库的增量更新

### 检索系统

先进的混合检索架构：

- **向量检索**：基于语义相似度的向量检索
- **关键词检索**：BM25算法的精确匹配
- **重排序**：多重排序策略优化检索结果
- **冲突解决**：自动检测和解决信息冲突

### RAG核心系统

完整的RAG处理流程：

- **查询理解**：意图识别和槽位填充
- **查询重写**：上下文感知的查询优化
- **检索增强**：多策略检索和结果融合
- **回复生成**：基于模板和上下文的回复生成

### 专业策略模块

金融领域专业处理：

- **风险评估策略**：客户风险分析和评级
- **产品推荐策略**：个性化产品匹配和推荐
- **合规处理策略**：确保回复符合监管要求
- **冲突解决策略**：处理信息不一致问题

## 使用示例

### 基本问答

```python
from main import FinancialRAGSystem

# 初始化系统
rag_system = FinancialRAGSystem()

# 单轮问答
response = rag_system.answer_query("个人消费贷款的利率是多少？")
print(response)

# 多轮对话
conversation = [
    {"role": "user", "content": "我想了解个人消费贷款"},
    {"role": "assistant", "content": "个人消费贷款是..."},
    {"role": "user", "content": "申请条件是什么？"}
]

response = rag_system.continue_conversation(conversation)
print(response)
```

### 风险评估

```python
from rag_system.risk_strategy import RiskStrategy

risk_strategy = RiskStrategy()

# 客户信息
customer_profile = {
    "age": 32,
    "monthly_income": 15000,
    "credit_score": 720,
    "debt_to_income": 0.3
}

# 申请信息
application_info = {
    "amount": 200000,
    "purpose": "装修",
    "term_months": 36
}

# 风险评估
assessment = risk_strategy.assess_customer_risk(customer_profile, application_info)
print(f"风险等级: {assessment['risk_level']}")
print(f"风险评分: {assessment['risk_score']}")
```

### 产品推荐

```python
from rag_system.product_strategy import ProductStrategy

product_strategy = ProductStrategy()

# 可用产品列表
available_products = [
    {
        "name": "个人消费贷款",
        "type": "消费贷",
        "interest_rate": {"min": 4.35, "max": 18.0},
        "features": {"amount_range": "1-50万"}
    }
]

# 获取推荐
recommendations = product_strategy.recommend_products(
    customer_profile, application_info, available_products
)

for rec in recommendations:
    print(f"推荐产品: {rec['product']['name']}")
    print(f"匹配度: {rec['match_score']:.2f}")
```

### 对话质量评估

```python
from dialog_evaluator import DialogEvaluator

evaluator = DialogEvaluator()

# 评估单个回复
query = "个人消费贷款的利率是多少？"
response = "个人消费贷款年化利率通常在4.35%-18%之间..."

evaluation = evaluator.evaluate_single_response(query, response)
print(f"评估得分: {evaluation['weighted_score']}/100")
print(f"评估等级: {evaluation['grade']}")
```

### 金融分析

```python
from financial_analyzer import FinancialAnalyzer

analyzer = FinancialAnalyzer()

# 客户财务数据
customer_data = {
    "monthly_income": 15000,
    "monthly_expenses": 8000,
    "total_assets": 800000,
    "total_debts": 200000,
    "credit_score": 720
}

# 财务健康分析
health_analysis = analyzer.analyze_customer_financial_health(customer_data)
print(f"财务健康评分: {health_analysis['health_score']}/100")

# 贷款承受能力分析
loan_request = {"amount": 300000, "term_months": 60, "purpose": "装修"}
affordability = analyzer.analyze_loan_affordability(customer_data, loan_request)
print(f"推荐额度: {affordability['recommended_amount']['recommended_amount']}元")
```

## 配置说明

### 基础配置

```python
# config/financial_config.py

# 检索配置
RETRIEVAL_CONFIG = {
    "top_k": 10,
    "similarity_threshold": 0.7,
    "rerank_top_k": 5
}

# 生成配置
GENERATION_CONFIG = {
    "max_tokens": 2000,
    "temperature": 0.1,
    "top_p": 0.9
}

# 评估配置
EVALUATION_CONFIG = {
    "dimensions": ["准确性", "相关性", "完整性", "专业性", "可理解性", "合规性"],
    "weights": [0.25, 0.20, 0.20, 0.15, 0.10, 0.10]
}
```

### 大模型配置

支持多种大模型API：

```python
# 豆包配置
DOUBAO_CONFIG = {
    "api_key": "your_api_key",
    "base_url": "https://ark.cn-beijing.volces.com/api/v3",
    "model": "ep-20241227140424-cg7dw"
}

# OpenAI配置
OPENAI_CONFIG = {
    "api_key": "your_api_key",
    "base_url": "https://api.openai.com/v1",
    "model": "gpt-4"
}
```

## 部署指南

### Docker部署

1. 构建镜像：

```bash
docker build -t financial-rag-system .
```

2. 运行容器：

```bash
docker run -d \
  --name financial-rag \
  -p 8000:8000 \
  -v ./data:/app/data \
  -e DOUBAO_API_KEY=your_api_key \
  financial-rag-system
```

### 生产环境部署

1. 使用gunicorn部署：

```bash
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app --bind 0.0.0.0:8000
```

2. 配置nginx反向代理：

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 性能优化

### 检索优化

- **索引优化**：使用HNSW算法提升向量检索速度
- **缓存机制**：对频繁查询结果进行缓存
- **并行处理**：多线程并行检索和重排序

### 生成优化

- **模板缓存**：缓存常用回复模板
- **流式生成**：支持流式输出提升用户体验
- **批量处理**：批量处理多个查询请求

### 系统监控

```python
# 监控指标
MONITORING_METRICS = {
    "query_latency": "查询响应时间",
    "retrieval_accuracy": "检索准确率", 
    "generation_quality": "生成质量",
    "system_throughput": "系统吞吐量"
}
```

## 测试

### 单元测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定模块测试
python -m pytest tests/test_retrieval.py

# 生成测试覆盖率报告
python -m pytest --cov=. --cov-report=html
```

### 集成测试

```bash
# 端到端测试
python tests/integration/test_e2e.py

# 性能测试
python tests/performance/test_performance.py
```

### 质量评估

```bash
# 对话质量评估
python scripts/evaluate_dialog_quality.py

# 检索质量评估  
python scripts/evaluate_retrieval_quality.py
```

## 常见问题

### Q: 如何添加新的金融产品？

A: 在 `data_synthesis/product_generator.py` 中添加新的产品模板，然后重新构建知识库。

### Q: 如何自定义评估维度？

A: 修改 `dialog_evaluator.py` 中的 `evaluation_dimensions` 配置，添加新的评估维度和权重。

### Q: 如何提升检索准确率？

A: 可以调整检索参数、优化向量模型、增加训练数据，或使用领域特定的嵌入模型。

### Q: 如何确保合规性？

A: 系统内置合规检查模块，会自动检测和修正不合规内容。同时建议定期更新合规规则。
