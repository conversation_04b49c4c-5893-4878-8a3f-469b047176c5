# retrieval/hybrid_retriever.py
import json
import sqlite3
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import re
try:
    from .vector_retriever import VectorRetriever
    from .bm25_retriever import BM25Retriever
    from .financial_retriever import FinancialRetriever
except Exception as e:
    from vector_retriever import VectorRetriever
    from bm25_retriever import BM25Retriever
    from financial_retriever import FinancialRetriever
import os 
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_embedding_api

class HybridRetriever:
    """混合检索器"""
    
    def __init__(self, db_path: str, index_dir: str = "./data/knowledge_base/index"):
        self.db_path = db_path
        self.index_dir = index_dir
        
        # 初始化各个检索器
        self.vector_retriever = VectorRetriever(db_path, index_dir)
        self.bm25_retriever = BM25Retriever(db_path, index_dir)
        self.financial_retriever = FinancialRetriever(db_path, index_dir)
        
        # 默认权重配置
        self.weights = {
            'vector': 0.5,
            'bm25': 0.3,
            'financial': 0.2
        }
        
        # 相似度阈值
        self.similarity_threshold = 0.1
        
        # 查询历史（用于优化）
        self.query_history = []
    
    def _safe_extract_number(self, value: Any) -> float:
        """安全提取数字值 - 增强版"""
        if value is None:
            return 0.0
        
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            # 去除非数字字符
            value = value.strip()
            if not value:
                return 0.0
            
            # 处理中文数字单位
            if '万' in value:
                match = re.search(r'(\d+(?:\.\d+)?)', value.replace('万', ''))
                if match:
                    return float(match.group(1)) * 10000
            elif '千' in value:
                match = re.search(r'(\d+(?:\.\d+)?)', value.replace('千', ''))
                if match:
                    return float(match.group(1)) * 1000
            
            # 提取纯数字
            match = re.search(r'(\d+(?:\.\d+)?)', value)
            if match:
                return float(match.group(1))
        
        return 0.0
    
    def search(self, query: str, content_types: Optional[List[str]] = None,
               top_k: int = 10, weights: Optional[Dict[str, float]] = None) -> List[Dict[str, Any]]:
        """混合检索"""
        try:
            # 使用自定义权重或默认权重
            search_weights = weights if weights else self.weights
            
            # 各个检索器检索结果
            vector_results = self._get_vector_results(query, content_types, top_k * 2)
            bm25_results = self._get_bm25_results(query, content_types, top_k * 2)
            financial_results = self._get_financial_results(query, content_types, top_k * 2)
            
            # 融合检索结果
            final_results = self._merge_results(
                vector_results, bm25_results, financial_results, 
                search_weights, top_k
            )
            
            # 记录查询历史
            self._record_query(query, len(final_results))
            
            return final_results
            
        except Exception as e:
            print(f"混合检索失败: {e}")
            return []
    
    def search_with_intent(self, query: str, intent: str, content_types: Optional[List[str]] = None,
                          top_k: int = 10) -> List[Dict[str, Any]]:
        """基于意图的检索"""
        # 根据意图调整权重
        intent_weights = self._get_intent_weights(intent)
        
        # 根据意图过滤内容类型
        if not content_types:
            content_types = self._get_intent_content_types(intent)
        
        return self.search(query, content_types, top_k, intent_weights)
    
    def search_similar_cases(self, customer_profile: Dict[str, Any], 
                           top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相似客户案例"""
        # 构建客户画像查询
        query = self._build_profile_query(customer_profile)
        
        # 专门搜索案例类型
        content_types = ['case_profile', 'case_application', 'case_result', 'case_analysis', 'profile_basic', 'profile_risk']
        
        # 调整权重，更注重金融专业性
        weights = {'vector': 0.4, 'bm25': 0.2, 'financial': 0.4}
        
        results = self.search(query, content_types, top_k * 2, weights)
        
        # 进一步过滤和排序
        filtered_results = self._filter_similar_cases(results, customer_profile)
        
        return filtered_results[:top_k]
    
    def search_product_comparison(self, product_types: List[str], 
                                comparison_attributes: List[str],
                                top_k: int = 10) -> List[Dict[str, Any]]:
        """产品对比检索"""
        # 构建对比查询
        query = f"{'/'.join(product_types)} {'/'.join(comparison_attributes)} 对比"
        
        # 专门搜索产品信息
        content_types = ['product_basic_info', 'product_requirements', 'product_features']
        
        # 调整权重，更注重关键词匹配
        weights = {'vector': 0.3, 'bm25': 0.5, 'financial': 0.2}
        
        results = self.search(query, content_types, top_k * 2, weights)
        
        # 按产品类型分组
        grouped_results = self._group_by_product_type(results, product_types)
        
        return grouped_results
    
    def _get_vector_results(self, query: str, content_types: Optional[List[str]], 
                           top_k: int) -> List[Tuple[Dict[str, Any], float]]:
        """获取向量检索结果"""
        try:
            results = self.vector_retriever.search(query, content_types, top_k)
            return [(result, result.get('similarity_score', 0.0)) for result in results]
        except Exception as e:
            print(f"向量检索失败: {e}")
            return []
    
    def _get_bm25_results(self, query: str, content_types: Optional[List[str]], 
                         top_k: int) -> List[Tuple[Dict[str, Any], float]]:
        """获取BM25检索结果"""
        try:
            results = self.bm25_retriever.search(query, content_types, top_k)
            return [(result, result.get('bm25_score', 0.0)) for result in results]
        except Exception as e:
            print(f"BM25检索失败: {e}")
            return []
    
    def _get_financial_results(self, query: str, content_types: Optional[List[str]], 
                              top_k: int) -> List[Tuple[Dict[str, Any], float]]:
        """获取金融专业检索结果"""
        try:
            results = self.financial_retriever.search(query, content_types, top_k)
            return [(result, result.get('financial_score', 0.0)) for result in results]
        except Exception as e:
            print(f"金融专业检索失败: {e}")
            return []
    
    def _merge_results(self, vector_results: List[Tuple[Dict[str, Any], float]],
                      bm25_results: List[Tuple[Dict[str, Any], float]],
                      financial_results: List[Tuple[Dict[str, Any], float]],
                      weights: Dict[str, float], top_k: int) -> List[Dict[str, Any]]:
        """融合检索结果"""
        # 收集所有chunk_id及其得分
        chunk_scores = {}
        chunk_data = {}
        
        # 处理向量检索结果
        for result, score in vector_results:
            chunk_id = result.get('chunk_id')
            if chunk_id:
                chunk_scores[chunk_id] = chunk_scores.get(chunk_id, 0) + weights['vector'] * score
                chunk_data[chunk_id] = result
        
        # 处理BM25检索结果
        for result, score in bm25_results:
            chunk_id = result.get('chunk_id')
            if chunk_id:
                chunk_scores[chunk_id] = chunk_scores.get(chunk_id, 0) + weights['bm25'] * score
                if chunk_id not in chunk_data:
                    chunk_data[chunk_id] = result
        
        # 处理金融专业检索结果
        for result, score in financial_results:
            chunk_id = result.get('chunk_id')
            if chunk_id:
                chunk_scores[chunk_id] = chunk_scores.get(chunk_id, 0) + weights['financial'] * score
                if chunk_id not in chunk_data:
                    chunk_data[chunk_id] = result
        
        # 按综合得分排序
        sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1], reverse=True)
        
        # 过滤低分结果并构建最终结果
        final_results = []
        for chunk_id, score in sorted_chunks[:top_k]:
            if score >= self.similarity_threshold:
                result = chunk_data[chunk_id].copy()
                result['final_score'] = score
                result['score_breakdown'] = self._get_score_breakdown(
                    chunk_id, vector_results, bm25_results, financial_results, weights
                )
                final_results.append(result)
        
        return final_results
    
    def _get_score_breakdown(self, chunk_id: str, 
                           vector_results: List[Tuple[Dict[str, Any], float]],
                           bm25_results: List[Tuple[Dict[str, Any], float]],
                           financial_results: List[Tuple[Dict[str, Any], float]],
                           weights: Dict[str, float]) -> Dict[str, float]:
        """获取得分详细分解"""
        breakdown = {'vector': 0.0, 'bm25': 0.0, 'financial': 0.0}
        
        # 查找各个检索器的得分
        for result, score in vector_results:
            if result.get('chunk_id') == chunk_id:
                breakdown['vector'] = weights['vector'] * score
                break
        
        for result, score in bm25_results:
            if result.get('chunk_id') == chunk_id:
                breakdown['bm25'] = weights['bm25'] * score
                break
        
        for result, score in financial_results:
            if result.get('chunk_id') == chunk_id:
                breakdown['financial'] = weights['financial'] * score
                break
        
        return breakdown
    
    def _get_intent_weights(self, intent: str) -> Dict[str, float]:
        """根据意图获取权重配置"""
        intent_weight_map = {
            '产品咨询': {'vector': 0.4, 'bm25': 0.4, 'financial': 0.2},
            '资质评估': {'vector': 0.6, 'bm25': 0.2, 'financial': 0.2},
            '比较分析': {'vector': 0.3, 'bm25': 0.5, 'financial': 0.2},
            '申请指导': {'vector': 0.5, 'bm25': 0.3, 'financial': 0.2},
            '合规咨询': {'vector': 0.4, 'bm25': 0.3, 'financial': 0.3},
            '风险评估': {'vector': 0.5, 'bm25': 0.2, 'financial': 0.3}
        }
        
        return intent_weight_map.get(intent, self.weights)
    
    def _get_intent_content_types(self, intent: str) -> List[str]:
        """根据意图获取内容类型"""
        intent_content_map = {
            '产品咨询': ['product_basic_info', 'product_features', 'product_requirements'],
            '资质评估': ['case_profile', 'case_result', 'case_analysis', 'profile_basic', 'profile_risk'],
            '比较分析': ['product_basic_info', 'product_features'],
            '申请指导': ['product_requirements', 'dialog_qa'],
            '合规咨询': ['policy_paragraph'],
            '风险评估': ['case_analysis', 'risk_analysis', 'risk_case_full']
        }
        
        return intent_content_map.get(intent, [])
    
    def _build_profile_query(self, customer_profile: Dict[str, Any]) -> str:
        """构建客户画像查询"""
        query_parts = []
        
        age = customer_profile.get('age')
        if age:
            age_num = self._safe_extract_number(age)
            if age_num > 0:
                query_parts.append(f"{int(age_num)}岁")
        
        occupation = customer_profile.get('occupation')
        if occupation:
            query_parts.append(str(occupation))
        
        monthly_income = customer_profile.get('monthly_income')
        if monthly_income:
            income_num = self._safe_extract_number(monthly_income)
            if income_num > 0:
                query_parts.append(f"月收入{int(income_num)}")
        
        credit_score = customer_profile.get('credit_score')
        if credit_score:
            credit_num = self._safe_extract_number(credit_score)
            if credit_num >= 700:
                query_parts.append("征信良好")
            elif credit_num >= 600:
                query_parts.append("征信一般")
            else:
                query_parts.append("征信不良")
        
        return " ".join(query_parts)
    
    def _filter_similar_cases(self, results: List[Dict[str, Any]], 
                            customer_profile: Dict[str, Any]) -> List[Dict[str, Any]]:
        """过滤相似案例"""
        filtered = []
        
        for result in results:
            # 计算相似度
            similarity = self._calculate_profile_similarity(result, customer_profile)
            
            if similarity > 0.1:  # 降低相似度阈值
                result['profile_similarity'] = similarity
                filtered.append(result)
        
        # 按相似度排序
        filtered.sort(key=lambda x: x.get('profile_similarity', 0), reverse=True)
        
        return filtered
    
    def _calculate_profile_similarity(self, result: Dict[str, Any], 
                                    customer_profile: Dict[str, Any]) -> float:
        """计算客户画像相似度 - 修复类型错误"""
        try:
            metadata = result.get('metadata', {})
            
            # 尝试从不同位置获取客户画像信息
            case_profile = {}
            
            # 从metadata中获取customer_profile
            if 'customer_profile' in metadata:
                case_profile = metadata['customer_profile']
            
            # 从直接metadata中获取画像信息
            elif any(key in metadata for key in ['age', 'monthly_income', 'credit_score', 'occupation']):
                case_profile = {
                    'age': metadata.get('age'),
                    'monthly_income': metadata.get('monthly_income'),
                    'credit_score': metadata.get('credit_score'),
                    'occupation': metadata.get('occupation')
                }
            
            # 如果还是没有找到，尝试从content中提取
            elif not case_profile:
                content = result.get('content', '')
                case_profile = self._extract_profile_from_content(content)
            
            if not case_profile:
                return 0.1  # 给予基础相似度
            
            similarity = 0.0
            factors = 0
            
            # 年龄相似度 - 修复类型错误
            if 'age' in customer_profile and 'age' in case_profile:
                try:
                    customer_age = self._safe_extract_number(customer_profile['age'])
                    case_age = self._safe_extract_number(case_profile['age'])
                    
                    if customer_age > 0 and case_age > 0:
                        age_diff = abs(customer_age - case_age)
                        age_similarity = max(0, 1 - age_diff / 20)  # 20岁差距为0相似度
                        similarity += age_similarity
                        factors += 1
                except Exception as e:
                    print(f"年龄相似度计算失败: {e}")
            
            # 收入相似度 - 修复类型错误
            if 'monthly_income' in customer_profile and 'monthly_income' in case_profile:
                try:
                    customer_income = self._safe_extract_number(customer_profile['monthly_income'])
                    case_income = self._safe_extract_number(case_profile['monthly_income'])
                    
                    if customer_income > 0 and case_income > 0:
                        income_ratio = min(customer_income, case_income) / max(customer_income, case_income)
                        similarity += income_ratio
                        factors += 1
                except Exception as e:
                    print(f"收入相似度计算失败: {e}")
            
            # 征信相似度 - 修复类型错误
            if 'credit_score' in customer_profile and 'credit_score' in case_profile:
                try:
                    customer_credit = self._safe_extract_number(customer_profile['credit_score'])
                    case_credit = self._safe_extract_number(case_profile['credit_score'])
                    
                    if customer_credit > 0 and case_credit > 0:
                        score_diff = abs(customer_credit - case_credit)
                        credit_similarity = max(0, 1 - score_diff / 150)  # 150分差距为0相似度
                        similarity += credit_similarity
                        factors += 1
                except Exception as e:
                    print(f"征信相似度计算失败: {e}")
            
            # 职业相似度 - 安全处理字符串比较
            if 'occupation' in customer_profile and 'occupation' in case_profile:
                try:
                    customer_occupation = str(customer_profile['occupation']).lower().strip()
                    case_occupation = str(case_profile['occupation']).lower().strip()
                    
                    if customer_occupation and case_occupation:
                        if customer_occupation == case_occupation:
                            similarity += 1.0
                        elif customer_occupation in case_occupation or case_occupation in customer_occupation:
                            similarity += 0.7
                        else:
                            similarity += 0.3  # 不同职业给予基础分
                        factors += 1
                except Exception as e:
                    print(f"职业相似度计算失败: {e}")
            
            # 如果没有任何匹配因素，给予基础相似度
            if factors == 0:
                return 0.1
            
            return similarity / factors
            
        except Exception as e:
            print(f"计算相似度失败: {e}")
            return 0.1  # 返回基础相似度而不是0.0
    
    def _extract_profile_from_content(self, content: str) -> Dict[str, Any]:
        """从内容中提取客户画像信息 - 增强版"""
        profile = {}
        
        try:
            # 提取年龄 - 返回数字而不是字符串
            age_match = re.search(r'(\d+)岁', content)
            if age_match:
                profile['age'] = int(age_match.group(1))
            
            # 提取收入 - 统一处理单位
            income_patterns = [
                r'月收入[：:]?(\d+(?:\.\d+)?)[万千]?元?',
                r'收入[：:]?(\d+(?:\.\d+)?)[万千]?元?',
                r'(\d+(?:\.\d+)?)[万千]?元?[/／]月'
            ]
            
            for pattern in income_patterns:
                income_matches = re.findall(pattern, content)
                if income_matches:
                    income = float(income_matches[0])
                    if '万' in content:
                        income *= 10000
                    elif '千' in content:
                        income *= 1000
                    profile['monthly_income'] = income
                    break
            
            # 提取征信评分 - 返回数字
            credit_patterns = [
                r'征信[评评价]?分[：:]?(\d+)',
                r'信用[评评价]?分[：:]?(\d+)',
                r'征信[：:]?(\d+)分'
            ]
            
            for pattern in credit_patterns:
                credit_matches = re.findall(pattern, content)
                if credit_matches:
                    profile['credit_score'] = int(credit_matches[0])
                    break
            
            # 提取职业信息 - 返回字符串
            occupations = [
                '工程师', '教师', '医生', '公务员', '银行员工', 
                '销售', '经理', '分析师', '程序员', '设计师'
            ]
            for occupation in occupations:
                if occupation in content:
                    profile['occupation'] = occupation
                    break
        
        except Exception as e:
            print(f"从内容提取画像失败: {e}")
        
        return profile
    
    def _group_by_product_type(self, results: List[Dict[str, Any]], 
                             product_types: List[str]) -> List[Dict[str, Any]]:
        """按产品类型分组"""
        grouped = {ptype: [] for ptype in product_types}
        ungrouped = []
        
        for result in results:
            metadata = result.get('metadata', {})
            product_type = metadata.get('product_type', '')
            
            matched = False
            for ptype in product_types:
                if ptype in product_type or product_type in ptype:
                    grouped[ptype].append(result)
                    matched = True
                    break
            
            if not matched:
                ungrouped.append(result)
        
        # 重新组织结果
        final_results = []
        for ptype in product_types:
            final_results.extend(grouped[ptype])
        
        final_results.extend(ungrouped)
        
        return final_results
    
    def _record_query(self, query: str, result_count: int):
        """记录查询历史"""
        import datetime
        self.query_history.append({
            'query': query,
            'result_count': result_count,
            'timestamp': datetime.datetime.now().isoformat()
        })
        
        # 保持历史记录数量限制
        if len(self.query_history) > 1000:
            self.query_history = self.query_history[-500:]
    
    def get_query_statistics(self) -> Dict[str, Any]:
        """获取查询统计"""
        if not self.query_history:
            return {}
        
        total_queries = len(self.query_history)
        avg_results = sum(q['result_count'] for q in self.query_history) / total_queries
        
        return {
            'total_queries': total_queries,
            'average_results': avg_results,
            'recent_queries': self.query_history[-10:]
        }

if __name__ == "__main__":
    # 测试混合检索器
    retriever = HybridRetriever("./data/knowledge_base/knowledge.db", "./data/knowledge_base/index")
    
    # 测试基本检索
    print("测试基本检索...")
    results = retriever.search("个人消费贷款利率", top_k=5)
    print(f"检索到 {len(results)} 个结果")
    
    for result in results:
        print(f"- {result.get('chunk_id', 'N/A')}: {result.get('content', '')[:50]}...")
        print(f"  综合得分: {result.get('final_score', 0):.3f}")
        print(f"  得分详情: {result.get('score_breakdown', {})}")
    
    # 测试意图检索
    print("\n测试意图检索...")
    intent_results = retriever.search_with_intent("贷款条件", "产品咨询", top_k=3)
    print(f"意图检索到 {len(intent_results)} 个结果")
    
    # 测试相似案例检索
    print("\n测试相似案例检索...")
    test_profile = {
        'age': 30,
        'occupation': '软件工程师',
        'monthly_income': 12000,
        'credit_score': 720
    }
    
    similar_cases = retriever.search_similar_cases(test_profile, top_k=3)
    print(f"找到 {len(similar_cases)} 个相似案例")
    
    for case in similar_cases:
        similarity = case.get('profile_similarity', 0)
        print(f"- 相似度: {similarity:.3f}, 内容: {case.get('content', '')[:50]}...")