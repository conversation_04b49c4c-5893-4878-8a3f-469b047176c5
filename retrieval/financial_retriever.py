# retrieval/financial_retriever.py
import os
import json
import sqlite3
from typing import Dict, List, Any, Optional
import re
from collections import defaultdict

class FinancialRetriever:
    """金融专业检索器"""
    
    def __init__(self, db_path: str, index_dir: str = "./data/knowledge_base/index"):
        self.db_path = db_path
        self.index_dir = index_dir
        
        # 金融专业术语库
        self.financial_terms = self._load_financial_terms()
        
        # 金融实体识别模式
        self.entity_patterns = self._load_entity_patterns()
        
        # 金融场景权重
        self.scenario_weights = self._load_scenario_weights()
        
        # 检索参数
        self.min_financial_score = 0.1
    
    def _load_financial_terms(self) -> Dict[str, List[str]]:
        """加载金融专业术语"""
        return {
            '产品类型': [
                '消费贷', '抵押贷', '经营贷', '信用贷', '房贷', '车贷', 
                '信用卡', '储蓄卡', '理财产品', '保险'
            ],
            '利率相关': [
                '年化利率', '月利率', '日利率', '实际利率', '名义利率', 
                '浮动利率', '固定利率', 'LPR', '基准利率', '优惠利率'
            ],
            '金额相关': [
                '贷款额度', '授信额度', '最高额度', '最低额度', '放款金额',
                '本金', '利息', '手续费', '服务费', '违约金'
            ],
            '期限相关': [
                '贷款期限', '还款期限', '宽限期', '展期', '提前还款',
                '逾期', '到期', '续贷', '循环授信'
            ],
            '征信相关': [
                '征信报告', '信用记录', '信用评分', '征信查询', '逾期记录',
                '呆账', '坏账', '黑名单', '白户', '征信修复'
            ],
            '风险相关': [
                '风险评估', '风险等级', '风险控制', '风险预警', '违约风险',
                '信用风险', '操作风险', '市场风险', '流动性风险'
            ],
            '审批相关': [
                '贷款审批', '资质审查', '尽职调查', '贷前调查', '贷中管理',
                '贷后管理', '放款', '拒贷', '批准', '条件批准'
            ],
            '还款相关': [
                '等额本息', '等额本金', '先息后本', '按月付息', '到期还本',
                '分期还款', '一次性还款', '提前还款', '部分还款'
            ],
            '担保相关': [
                '抵押', '质押', '保证', '担保人', '抵押物', '担保物',
                '房产抵押', '车辆抵押', '存单质押', '连带责任'
            ],
            '机构相关': [
                '商业银行', '政策性银行', '村镇银行', '信用社', '小贷公司',
                '消费金融公司', '融资租赁', '保理公司', 'P2P', '互联网金融'
            ]
        }
    
    def _load_entity_patterns(self) -> Dict[str, str]:
        """加载金融实体识别模式"""
        return {
            '利率': r'(\d+\.?\d*)%?的?(年化|月|日)?利率',
            '金额': r'(\d+\.?\d*)(万|千|元|块)',
            '期限': r'(\d+)(年|个月|月|天|日)',
            '评分': r'征信评?分(\d+)分?',
            '收入': r'(月|年)收入(\d+\.?\d*)(万|千|元)',
            '年龄': r'(\d+)岁',
            '机构': r'(工商银行|建设银行|农业银行|中国银行|招商银行|平安银行|交通银行)',
            '产品': r'(个人|企业)?(消费|抵押|经营|信用)?贷款?'
        }
    
    def _load_scenario_weights(self) -> Dict[str, float]:
        """加载金融场景权重"""
        return {
            '产品咨询': 2.0,
            '利率查询': 1.8,
            '额度评估': 1.6,
            '条件咨询': 1.5,
            '流程咨询': 1.3,
            '风险评估': 1.4,
            '案例参考': 1.2,
            '政策解读': 1.1
        }
    
    def search(self, query: str, content_types: Optional[List[str]] = None,
               top_k: int = 10) -> List[Dict[str, Any]]:
        """金融专业检索"""
        try:
            # 金融实体识别
            entities = self._extract_financial_entities(query)
            
            # 金融术语识别
            terms = self._identify_financial_terms(query)
            
            # 场景识别
            scenario = self._identify_scenario(query)
            
            # 构建专业查询
            enhanced_query = self._enhance_query(query, entities, terms)
            
            # 执行检索
            results = self._financial_search(enhanced_query, entities, terms, scenario, content_types)
            
            # 计算金融专业得分
            scored_results = self._calculate_financial_scores(results, entities, terms, scenario)
            
            # 排序和过滤
            final_results = self._rank_financial_results(scored_results, top_k)
            
            return final_results
            
        except Exception as e:
            print(f"金融专业检索失败: {e}")
            return []
    
    def search_by_product_type(self, product_type: str, attributes: Dict[str, Any],
                              top_k: int = 10) -> List[Dict[str, Any]]:
        """按产品类型检索"""
        try:
            # 构建产品查询
            query_parts = [product_type]
            
            for attr, value in attributes.items():
                if attr == 'rate_range' and isinstance(value, tuple):
                    query_parts.append(f"利率{value[0]}%-{value[1]}%")
                elif attr == 'amount_range' and isinstance(value, tuple):
                    query_parts.append(f"额度{value[0]}-{value[1]}")
                elif attr == 'institution':
                    query_parts.append(str(value))
                else:
                    query_parts.append(str(value))
            
            query = " ".join(query_parts)
            
            # 专门搜索产品信息
            content_types = ['product_basic_info', 'product_features', 'product_requirements']
            
            return self.search(query, content_types, top_k)
            
        except Exception as e:
            print(f"产品类型检索失败: {e}")
            return []
    
    def search_similar_risk_cases(self, risk_profile: Dict[str, Any],
                                 top_k: int = 5) -> List[Dict[str, Any]]:
        """搜索相似风险案例"""
        try:
            # 构建风险查询
            query_parts = []
            
            credit_score = risk_profile.get('credit_score')
            if credit_score:
                if credit_score >= 700:
                    query_parts.append("征信良好")
                elif credit_score >= 600:
                    query_parts.append("征信一般")
                else:
                    query_parts.append("征信不良")
            
            income = risk_profile.get('monthly_income')
            if income:
                if income >= 10000:
                    query_parts.append("高收入")
                elif income >= 5000:
                    query_parts.append("中等收入")
                else:
                    query_parts.append("低收入")
            
            debt_ratio = risk_profile.get('debt_to_income')
            if debt_ratio:
                if debt_ratio <= 0.3:
                    query_parts.append("负债率低")
                elif debt_ratio <= 0.5:
                    query_parts.append("负债率中等")
                else:
                    query_parts.append("负债率高")
            
            query = " ".join(query_parts)
            
            # 专门搜索案例分析
            content_types = ['case_analysis', 'case_result', 'risk_analysis', 'risk_case_full']
            
            results = self.search(query, content_types, top_k * 2)
            
            # 进一步过滤相似风险案例
            filtered_results = self._filter_risk_cases(results, risk_profile)
            
            return filtered_results[:top_k]
            
        except Exception as e:
            print(f"风险案例检索失败: {e}")
            return []
    
    def _extract_financial_entities(self, query: str) -> Dict[str, List[str]]:
        """提取金融实体"""
        entities = defaultdict(list)
        
        try:
            for entity_type, pattern in self.entity_patterns.items():
                matches = re.findall(pattern, query)
                if matches:
                    entities[entity_type] = matches
        
        except Exception as e:
            print(f"金融实体提取失败: {e}")
        
        return dict(entities)
    
    def _identify_financial_terms(self, query: str) -> List[str]:
        """识别金融术语"""
        identified_terms = []
        
        try:
            query_lower = query.lower()
            
            for category, terms in self.financial_terms.items():
                for term in terms:
                    if term in query_lower:
                        identified_terms.append(term)
        
        except Exception as e:
            print(f"金融术语识别失败: {e}")
        
        return identified_terms
    
    def _identify_scenario(self, query: str) -> str:
        """识别金融场景"""
        try:
            query_lower = query.lower()
            
            # 场景关键词映射
            scenario_keywords = {
                '产品咨询': ['产品', '介绍', '什么是', '有哪些'],
                '利率查询': ['利率', '费率', '成本', '多少钱'],
                '额度评估': ['额度', '能贷多少', '最高', '最低'],
                '条件咨询': ['条件', '要求', '资格', '门槛'],
                '流程咨询': ['流程', '步骤', '怎么申请', '如何'],
                '风险评估': ['风险', '安全', '可靠', '成功率'],
                '案例参考': ['案例', '例子', '经验', '参考'],
                '政策解读': ['政策', '规定', '法规', '监管']
            }
            
            for scenario, keywords in scenario_keywords.items():
                if any(keyword in query_lower for keyword in keywords):
                    return scenario
            
            return '一般咨询'
            
        except Exception as e:
            print(f"场景识别失败: {e}")
            return '一般咨询'
    
    def _enhance_query(self, original_query: str, entities: Dict[str, List[str]], 
                      terms: List[str]) -> str:
        """增强查询"""
        try:
            query_parts = [original_query]
            
            # 添加同义词
            for term in terms:
                synonyms = self._get_term_synonyms(term)
                query_parts.extend(synonyms)
            
            # 添加相关术语
            for entity_type, entity_values in entities.items():
                related_terms = self._get_related_terms(entity_type, entity_values)
                query_parts.extend(related_terms)
            
            return " ".join(set(query_parts))
            
        except Exception as e:
            print(f"查询增强失败: {e}")
            return original_query
    
    def _get_term_synonyms(self, term: str) -> List[str]:
        """获取术语同义词"""
        synonym_map = {
            '消费贷': ['消费贷款', '个人消费贷', '信用贷'],
            '抵押贷': ['抵押贷款', '房产抵押贷', '抵押借款'],
            '经营贷': ['经营贷款', '企业贷款', '商业贷款'],
            '年化利率': ['年利率', '年息', '年化收益率'],
            '征信': ['信用记录', '征信报告', '信用状况'],
            '额度': ['贷款额度', '授信额度', '放款额度']
        }
        
        return synonym_map.get(term, [])
    
    def _get_related_terms(self, entity_type: str, entity_values: List[str]) -> List[str]:
        """获取相关术语"""
        related_terms = []
        
        if entity_type == '利率':
            related_terms.extend(['年化利率', '利息', '费率', '成本'])
        elif entity_type == '金额':
            related_terms.extend(['额度', '放款', '本金'])
        elif entity_type == '期限':
            related_terms.extend(['还款期限', '贷款期限', '分期'])
        elif entity_type == '机构':
            related_terms.extend(['银行', '金融机构', '放贷机构'])
        
        return related_terms
    
    def _financial_search(self, query: str, entities: Dict[str, List[str]], 
                         terms: List[str], scenario: str, 
                         content_types: Optional[List[str]]) -> List[Dict[str, Any]]:
        """执行金融搜索"""
        results = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 构建SQL查询
                where_conditions = []
                params = []
                
                # 内容匹配
                content_conditions = []
                for term in terms:
                    content_conditions.append("content LIKE ?")
                    params.append(f"%{term}%")
                
                if content_conditions:
                    where_conditions.append(f"({' OR '.join(content_conditions)})")
                
                # 关键词匹配
                if terms:
                    keyword_conditions = []
                    for term in terms:
                        keyword_conditions.append("keywords LIKE ?")
                        params.append(f'%"{term}"%')
                    
                    if keyword_conditions:
                        where_conditions.append(f"({' OR '.join(keyword_conditions)})")
                
                # 内容类型过滤
                if content_types:
                    placeholders = ','.join(['?' for _ in content_types])
                    where_conditions.append(f"content_type IN ({placeholders})")
                    params.extend(content_types)
                
                # 构建完整SQL
                if where_conditions:
                    sql = '''
                        SELECT chunk_id, content, content_type, keywords, category_path, metadata
                        FROM chunks 
                        WHERE {}
                        ORDER BY 
                            CASE 
                                WHEN content_type LIKE '%product%' THEN 1
                                WHEN content_type LIKE '%case%' THEN 2
                                WHEN content_type LIKE '%policy%' THEN 3
                                ELSE 4
                            END
                    '''.format(' OR '.join(where_conditions))
                    
                    cursor.execute(sql, params)
                    
                    for row in cursor.fetchall():
                        chunk_id, content, content_type, keywords_json, category_path, metadata_json = row
                        
                        # 解析JSON数据
                        try:
                            keywords = json.loads(keywords_json) if keywords_json else []
                            metadata = json.loads(metadata_json) if metadata_json else {}
                        except:
                            keywords = []
                            metadata = {}
                        
                        result = {
                            'chunk_id': chunk_id,
                            'content': content,
                            'content_type': content_type,
                            'keywords': keywords,
                            'category_path': category_path,
                            'metadata': metadata,
                            'retrieval_method': 'financial'
                        }
                        
                        results.append(result)
        
        except Exception as e:
            print(f"金融搜索失败: {e}")
        
        return results
    
    def _calculate_financial_scores(self, results: List[Dict[str, Any]], 
                                   entities: Dict[str, List[str]], 
                                   terms: List[str], scenario: str) -> List[Dict[str, Any]]:
        """计算金融专业得分"""
        scored_results = []
        
        try:
            scenario_weight = self.scenario_weights.get(scenario, 1.0)
            
            for result in results:
                score = 0.0
                
                content = result.get('content', '').lower()
                keywords = result.get('keywords', [])
                metadata = result.get('metadata', {})
                
                # 术语匹配得分
                term_score = 0.0
                for term in terms:
                    if term in content:
                        term_score += 1.0
                    if term in keywords:
                        term_score += 0.5
                
                # 实体匹配得分
                entity_score = 0.0
                for entity_type, entity_values in entities.items():
                    for entity_value in entity_values:
                        if str(entity_value) in content:
                            entity_score += 0.8
                
                # 内容类型权重
                content_type_score = 0.0
                content_type = result.get('content_type', '')
                if 'product' in content_type:
                    content_type_score = 1.5
                elif 'case' in content_type:
                    content_type_score = 1.2
                elif 'policy' in content_type:
                    content_type_score = 1.0
                else:
                    content_type_score = 0.8
                
                # 金融专业性得分
                financial_score = 0.0
                for category, category_terms in self.financial_terms.items():
                    matched_terms = sum(1 for term in category_terms if term in content)
                    financial_score += matched_terms * 0.3
                
                # 综合得分
                final_score = (term_score * 2.0 + entity_score * 1.5 + 
                              financial_score * 1.0 + content_type_score) * scenario_weight
                
                result = result.copy()
                result['financial_score'] = final_score
                result['score_details'] = {
                    'term_score': term_score,
                    'entity_score': entity_score,
                    'financial_score': financial_score,
                    'content_type_score': content_type_score,
                    'scenario_weight': scenario_weight
                }
                
                scored_results.append(result)
        
        except Exception as e:
            print(f"计算金融得分失败: {e}")
        
        return scored_results
    
    def _rank_financial_results(self, results: List[Dict[str, Any]], top_k: int) -> List[Dict[str, Any]]:
        """排序金融结果"""
        # 按金融得分过滤
        filtered = [r for r in results if r.get('financial_score', 0) >= self.min_financial_score]
        
        # 按得分排序
        filtered.sort(key=lambda x: x.get('financial_score', 0), reverse=True)
        
        # 去重
        seen_chunks = set()
        unique_results = []
        for result in filtered:
            chunk_id = result.get('chunk_id')
            if chunk_id not in seen_chunks:
                seen_chunks.add(chunk_id)
                unique_results.append(result)
        
        return unique_results[:top_k]
    
    def _filter_risk_cases(self, results: List[Dict[str, Any]], 
                          risk_profile: Dict[str, Any]) -> List[Dict[str, Any]]:
        """过滤风险案例"""
        filtered = []
        
        try:
            for result in results:
                metadata = result.get('metadata', {})
                case_profile = metadata.get('customer_profile', {})
                
                if self._is_similar_risk_profile(case_profile, risk_profile):
                    risk_similarity = self._calculate_risk_similarity(case_profile, risk_profile)
                    result = result.copy()
                    result['risk_similarity'] = risk_similarity
                    filtered.append(result)
            
            # 按风险相似度排序
            filtered.sort(key=lambda x: x.get('risk_similarity', 0), reverse=True)
        
        except Exception as e:
            print(f"过滤风险案例失败: {e}")
        
        return filtered
    
    def _is_similar_risk_profile(self, case_profile: Dict[str, Any], 
                                target_profile: Dict[str, Any]) -> bool:
        """判断是否为相似风险画像"""
        try:
            # 征信评分相似性
            case_credit = case_profile.get('credit_score', 0)
            target_credit = target_profile.get('credit_score', 0)
            if abs(case_credit - target_credit) > 100:
                return False
            
            # 收入水平相似性
            case_income = case_profile.get('monthly_income', 0)
            target_income = target_profile.get('monthly_income', 0)
            if case_income > 0 and target_income > 0:
                income_ratio = min(case_income, target_income) / max(case_income, target_income)
                if income_ratio < 0.5:
                    return False
            
            return True
            
        except Exception as e:
            print(f"风险画像相似性判断失败: {e}")
            return False
    
    def _calculate_risk_similarity(self, case_profile: Dict[str, Any], 
                                  target_profile: Dict[str, Any]) -> float:
        """计算风险相似度"""
        try:
            similarity = 0.0
            factors = 0
            
            # 征信评分相似度
            if 'credit_score' in case_profile and 'credit_score' in target_profile:
                case_credit = case_profile['credit_score']
                target_credit = target_profile['credit_score']
                credit_diff = abs(case_credit - target_credit)
                credit_similarity = max(0, 1 - credit_diff / 150)
                similarity += credit_similarity
                factors += 1
            
            # 收入相似度
            if 'monthly_income' in case_profile and 'monthly_income' in target_profile:
                case_income = case_profile['monthly_income']
                target_income = target_profile['monthly_income']
                income_ratio = min(case_income, target_income) / max(case_income, target_income)
                similarity += income_ratio
                factors += 1
            
            # 负债率相似度
            if 'debt_to_income' in case_profile and 'debt_to_income' in target_profile:
                case_debt = case_profile['debt_to_income']
                target_debt = target_profile['debt_to_income']
                debt_diff = abs(case_debt - target_debt)
                debt_similarity = max(0, 1 - debt_diff / 0.5)
                similarity += debt_similarity
                factors += 1
            
            return similarity / factors if factors > 0 else 0.0
            
        except Exception as e:
            print(f"计算风险相似度失败: {e}")
            return 0.0

if __name__ == "__main__":
    # 测试金融专业检索器
    retriever = FinancialRetriever("./data/knowledge_base/knowledge.db", "./data/knowledge_base/index")
    
    # 测试基本金融检索
    print("测试金融专业检索...")
    results = retriever.search("个人消费贷款年化利率", top_k=5)
    print(f"检索到 {len(results)} 个结果")
    
    for result in results:
        print(f"- {result.get('chunk_id', 'N/A')}: {result.get('content', '')[:50]}...")
        print(f"  金融得分: {result.get('financial_score', 0):.3f}")
        if 'score_details' in result:
            details = result['score_details']
            print(f"  得分详情: 术语={details.get('term_score', 0):.2f}, "
                  f"实体={details.get('entity_score', 0):.2f}, "
                  f"专业性={details.get('financial_score', 0):.2f}")
    
    # 测试产品类型检索
    print("\n测试产品类型检索...")
    product_results = retriever.search_by_product_type(
        "消费贷", 
        {"rate_range": (5.0, 15.0), "institution": "工商银行"}, 
        top_k=3
    )
    print(f"产品检索到 {len(product_results)} 个结果")
    
    # 测试风险案例检索
    print("\n测试风险案例检索...")
    risk_profile = {
        'credit_score': 680,
        'monthly_income': 8000,
        'debt_to_income': 0.4
    }
    
    risk_cases = retriever.search_similar_risk_cases(risk_profile, top_k=3)
    print(f"风险案例检索到 {len(risk_cases)} 个结果")
    
    for case in risk_cases:
        similarity = case.get('risk_similarity', 0)
        print(f"- 风险相似度: {similarity:.3f}, 内容: {case.get('content', '')[:50]}...")