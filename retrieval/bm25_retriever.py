# retrieval/bm25_retriever.py
import os
import json
import pickle
import sqlite3
import math
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter
import jieba

class BM25Retriever:
    """BM25关键词检索器"""
    
    def __init__(self, db_path: str, index_dir: str = "./data/knowledge_base/index"):
        self.db_path = db_path
        self.index_dir = index_dir
        
        # BM25参数
        self.k1 = 1.5
        self.b = 0.75
        
        # 检索参数
        self.min_score = 0.1
        
        # 停用词 - 优先初始化
        self.stopwords = self._load_stopwords()
        
        # 加载索引
        self.keyword_index = self._load_keyword_index()
        self.doc_stats = self._load_doc_stats()
        
        # 验证索引有效性
        if not self.keyword_index or self.doc_stats.get('doc_count', 0) == 0:
            print("索引无效，自动重建...")
            self.rebuild_index()
    
    def _load_keyword_index(self) -> Dict[str, List[str]]:
        """加载关键词索引"""
        try:
            index_path = os.path.join(self.index_dir, "keyword_index.pkl")
            if os.path.exists(index_path):
                with open(index_path, 'rb') as f:
                    return pickle.load(f)
            else:
                print(f"关键词索引文件不存在: {index_path}")
                return self._build_keyword_index()
        except Exception as e:
            print(f"加载关键词索引失败: {e}")
            return self._build_keyword_index()
    
    def _build_keyword_index(self) -> Dict[str, List[str]]:
        """构建关键词索引"""
        keyword_index = defaultdict(list)
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT chunk_id, content FROM chunks')
                
                for row in cursor.fetchall():
                    chunk_id, content = row
                    
                    # 分词
                    terms = self._tokenize(content)
                    
                    # 建立倒排索引
                    for term in set(terms):  # 使用set去重
                        keyword_index[term].append(chunk_id)
                
                # 保存索引
                os.makedirs(self.index_dir, exist_ok=True)
                index_path = os.path.join(self.index_dir, "keyword_index.pkl")
                with open(index_path, 'wb') as f:
                    pickle.dump(dict(keyword_index), f)
                    
                print(f"构建关键词索引完成，包含 {len(keyword_index)} 个词项")
        
        except Exception as e:
            print(f"构建关键词索引失败: {e}")
        
        return dict(keyword_index)
    
    def _load_doc_stats(self) -> Dict[str, Any]:
        """加载文档统计信息"""
        try:
            stats_path = os.path.join(self.index_dir, "doc_stats.pkl")
            if os.path.exists(stats_path):
                with open(stats_path, 'rb') as f:
                    stats = pickle.load(f)
                    # 验证统计信息的有效性
                    if stats.get('doc_count', 0) > 0 and stats.get('avg_doc_length', 0) > 0:
                        return stats
                    else:
                        print("文档统计信息无效，重新计算")
                        return self._calculate_doc_stats()
            else:
                print("文档统计文件不存在，重新计算")
                return self._calculate_doc_stats()
        except Exception as e:
            print(f"加载文档统计失败: {e}")
            return self._calculate_doc_stats()
    
    def _calculate_doc_stats(self) -> Dict[str, Any]:
        """计算文档统计信息"""
        stats = {
            'doc_count': 0,
            'avg_doc_length': 0,
            'doc_lengths': {},
            'term_doc_freq': defaultdict(int)
        }
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT chunk_id, content FROM chunks')
                
                total_length = 0
                doc_count = 0
                
                for row in cursor.fetchall():
                    chunk_id, content = row
                    
                    # 分词
                    terms = self._tokenize(content)
                    doc_length = len(terms)
                    
                    # 跳过空文档
                    if doc_length == 0:
                        continue
                    
                    stats['doc_lengths'][chunk_id] = doc_length
                    total_length += doc_length
                    doc_count += 1
                    
                    # 计算词项文档频率
                    unique_terms = set(terms)
                    for term in unique_terms:
                        stats['term_doc_freq'][term] += 1
                
                stats['doc_count'] = doc_count
                stats['avg_doc_length'] = total_length / doc_count if doc_count > 0 else 1.0  # 避免除零错误
                
                # 保存统计信息
                os.makedirs(self.index_dir, exist_ok=True)
                stats_path = os.path.join(self.index_dir, "doc_stats.pkl")
                with open(stats_path, 'wb') as f:
                    pickle.dump(stats, f)
                    
                print(f"计算文档统计完成：文档数={doc_count}, 平均长度={stats['avg_doc_length']:.2f}")
        
        except Exception as e:
            print(f"计算文档统计失败: {e}")
            # 返回默认值避免后续错误
            stats = {
                'doc_count': 1,
                'avg_doc_length': 1.0,
                'doc_lengths': {},
                'term_doc_freq': defaultdict(int)
            }
        
        return stats
    
    def _load_stopwords(self) -> set:
        """加载停用词"""
        stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个',
            '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好',
            '自己', '这', '那', '些', '什么', '怎么', '可以', '能', '能够', '如何', '为什么'
        }
        return stopwords
    
    def search(self, query: str, content_types: Optional[List[str]] = None, 
               top_k: int = 10) -> List[Dict[str, Any]]:
        """BM25检索"""
        try:
            # 检查索引是否有效
            if not self.keyword_index:
                print("关键词索引为空，尝试重建")
                self.keyword_index = self._build_keyword_index()
            
            if not self.doc_stats or self.doc_stats.get('doc_count', 0) == 0:
                print("文档统计信息无效，尝试重算")
                self.doc_stats = self._calculate_doc_stats()
            
            # 查询分词
            query_terms = self._tokenize(query)
            if not query_terms:
                print(f"查询分词结果为空: {query}")
                return []
            
            print(f"查询分词结果: {query_terms}")
            
            # 计算BM25得分
            doc_scores = self._calculate_bm25_scores(query_terms)
            
            if not doc_scores:
                print("BM25得分计算结果为空")
                return []
            
            print(f"计算得分的文档数: {len(doc_scores)}")
            
            # 获取检索结果
            results = self._get_bm25_results(doc_scores, content_types)
            
            # 过滤和排序
            filtered_results = self._filter_bm25_results(results, top_k)
            
            return filtered_results
            
        except Exception as e:
            print(f"BM25检索失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def search_exact_phrase(self, phrase: str, content_types: Optional[List[str]] = None,
                           top_k: int = 10) -> List[Dict[str, Any]]:
        """精确短语检索"""
        try:
            results = []
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 构建SQL查询
                sql = 'SELECT chunk_id, content, content_type, keywords, category_path, metadata FROM chunks WHERE content LIKE ?'
                params = [f'%{phrase}%']
                
                if content_types:
                    placeholders = ','.join(['?' for _ in content_types])
                    sql += f' AND content_type IN ({placeholders})'
                    params.extend(content_types)
                
                cursor.execute(sql, params)
                
                for row in cursor.fetchall():
                    chunk_id, content, content_type, keywords_json, category_path, metadata_json = row
                    
                    # 计算短语匹配度
                    phrase_score = self._calculate_phrase_score(content, phrase)
                    
                    if phrase_score > 0:
                        try:
                            keywords = json.loads(keywords_json) if keywords_json else []
                            metadata = json.loads(metadata_json) if metadata_json else {}
                        except:
                            keywords = []
                            metadata = {}
                        
                        result = {
                            'chunk_id': chunk_id,
                            'content': content,
                            'content_type': content_type,
                            'keywords': keywords,
                            'category_path': category_path,
                            'metadata': metadata,
                            'bm25_score': phrase_score,
                            'retrieval_method': 'phrase_match'
                        }
                        
                        results.append(result)
            
            # 按得分排序
            results.sort(key=lambda x: x['bm25_score'], reverse=True)
            
            return results[:top_k]
            
        except Exception as e:
            print(f"精确短语检索失败: {e}")
            return []
    
    def search_with_filters(self, query: str, filters: Dict[str, Any],
                           top_k: int = 10) -> List[Dict[str, Any]]:
        """带过滤条件的检索"""
        try:
            # 基础BM25检索
            base_results = self.search(query, top_k=top_k * 3)
            
            # 应用过滤条件
            filtered_results = []
            
            for result in base_results:
                if self._match_filters(result, filters):
                    filtered_results.append(result)
            
            return filtered_results[:top_k]
            
        except Exception as e:
            print(f"过滤检索失败: {e}")
            return []
    
    def _tokenize(self, text: str) -> List[str]:
        """文本分词"""
        try:
            if not text or not text.strip():
                return []
            
            # 使用jieba分词
            words = jieba.lcut(text)
            
            # 过滤停用词和短词
            filtered_words = []
            for word in words:
                word = word.strip()
                if len(word) > 1 and word not in self.stopwords:
                    filtered_words.append(word.lower())
            
            return filtered_words
            
        except Exception as e:
            print(f"分词失败: {e}")
            return []
    
    def _calculate_bm25_scores(self, query_terms: List[str]) -> Dict[str, float]:
        """计算BM25得分"""
        doc_scores = defaultdict(float)
        
        try:
            N = self.doc_stats.get('doc_count', 1)
            avg_dl = self.doc_stats.get('avg_doc_length', 1.0)
            
            # 确保分母不为0
            if avg_dl <= 0:
                avg_dl = 1.0
            
            print(f"文档总数: {N}, 平均文档长度: {avg_dl}")
            
            for term in query_terms:
                if term in self.keyword_index:
                    # 获取包含该词的文档
                    docs_with_term = self.keyword_index[term]
                    df = len(docs_with_term)  # 文档频率
                    
                    if df == 0:
                        continue
                    
                    # 计算IDF，确保分母不为0
                    idf = math.log(max(1, (N - df + 0.5)) / max(1, (df + 0.5)))
                    
                    print(f"词项 '{term}': 文档频率={df}, IDF={idf:.3f}")
                    
                    for doc_id in docs_with_term:
                        if doc_id in self.doc_stats['doc_lengths']:
                            # 计算词频
                            tf = self._get_term_frequency(doc_id, term)
                            
                            # 文档长度
                            dl = self.doc_stats['doc_lengths'][doc_id]
                            
                            if dl <= 0:
                                dl = 1
                            
                            # BM25公式
                            numerator = tf * (self.k1 + 1)
                            denominator = tf + self.k1 * (1 - self.b + self.b * (dl / avg_dl))
                            
                            if denominator > 0:
                                score = idf * numerator / denominator
                                doc_scores[doc_id] += score
            
            print(f"计算BM25得分完成，共 {len(doc_scores)} 个文档有得分")
        
        except Exception as e:
            print(f"计算BM25得分失败: {e}")
            import traceback
            traceback.print_exc()
        
        return doc_scores
    
    def _get_term_frequency(self, doc_id: str, term: str) -> int:
        """获取词项在文档中的频率"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT content FROM chunks WHERE chunk_id = ?', (doc_id,))
                
                row = cursor.fetchone()
                if row:
                    content = row[0]
                    terms = self._tokenize(content)
                    return terms.count(term)
                
                return 0
                
        except Exception as e:
            print(f"获取词频失败: {e}")
            return 0
    
    def _get_bm25_results(self, doc_scores: Dict[str, float], 
                         content_types: Optional[List[str]]) -> List[Dict[str, Any]]:
        """获取BM25检索结果"""
        results = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for doc_id, score in doc_scores.items():
                    cursor.execute('''
                        SELECT content, content_type, keywords, category_path, metadata
                        FROM chunks WHERE chunk_id = ?
                    ''', (doc_id,))
                    
                    row = cursor.fetchone()
                    if row:
                        content, content_type, keywords_json, category_path, metadata_json = row
                        
                        # 内容类型过滤
                        if content_types and content_type not in content_types:
                            continue
                        
                        # 解析JSON数据
                        try:
                            keywords = json.loads(keywords_json) if keywords_json else []
                            metadata = json.loads(metadata_json) if metadata_json else {}
                        except:
                            keywords = []
                            metadata = {}
                        
                        result = {
                            'chunk_id': doc_id,
                            'content': content,
                            'content_type': content_type,
                            'keywords': keywords,
                            'category_path': category_path,
                            'metadata': metadata,
                            'bm25_score': score,
                            'retrieval_method': 'bm25'
                        }
                        
                        results.append(result)
        
        except Exception as e:
            print(f"获取BM25结果失败: {e}")
        
        return results
    
    def _filter_bm25_results(self, results: List[Dict[str, Any]], top_k: int) -> List[Dict[str, Any]]:
        """过滤BM25结果"""
        # 按得分过滤
        filtered = [r for r in results if r.get('bm25_score', 0) >= self.min_score]
        
        # 按得分排序
        filtered.sort(key=lambda x: x.get('bm25_score', 0), reverse=True)
        
        # 去重
        seen_chunks = set()
        unique_results = []
        for result in filtered:
            chunk_id = result.get('chunk_id')
            if chunk_id not in seen_chunks:
                seen_chunks.add(chunk_id)
                unique_results.append(result)
        
        return unique_results[:top_k]
    
    def _calculate_phrase_score(self, content: str, phrase: str) -> float:
        """计算短语匹配得分"""
        try:
            content_lower = content.lower()
            phrase_lower = phrase.lower()
            
            # 计算短语出现次数
            phrase_count = content_lower.count(phrase_lower)
            
            if phrase_count == 0:
                return 0.0
            
            # 基础得分
            base_score = phrase_count * 2.0
            
            # 长度奖励（短语越长，匹配越精确）
            length_bonus = len(phrase) / 10.0
            
            # 位置奖励（出现在开头的得分更高）
            position_bonus = 0.0
            first_occurrence = content_lower.find(phrase_lower)
            if first_occurrence >= 0:
                position_bonus = 1.0 / (1.0 + first_occurrence / 100.0)
            
            return base_score + length_bonus + position_bonus
            
        except Exception as e:
            print(f"计算短语得分失败: {e}")
            return 0.0
    
    def _match_filters(self, result: Dict[str, Any], filters: Dict[str, Any]) -> bool:
        """检查结果是否匹配过滤条件"""
        try:
            metadata = result.get('metadata', {})
            
            for filter_key, filter_value in filters.items():
                if filter_key == 'content_type':
                    if result.get('content_type') != filter_value:
                        return False
                
                elif filter_key == 'category_path':
                    category_path = result.get('category_path', '')
                    if filter_value not in category_path:
                        return False
                
                elif filter_key == 'keywords':
                    keywords = result.get('keywords', [])
                    if isinstance(filter_value, list):
                        if not any(kw in keywords for kw in filter_value):
                            return False
                    else:
                        if filter_value not in keywords:
                            return False
                
                elif filter_key in metadata:
                    if metadata[filter_key] != filter_value:
                        return False
            
            return True
            
        except Exception as e:
            print(f"过滤匹配失败: {e}")
            return False
    
    def get_term_statistics(self, term: str) -> Dict[str, Any]:
        """获取词项统计信息"""
        stats = {
            'term': term,
            'document_frequency': 0,
            'total_frequency': 0,
            'documents': []
        }
        
        try:
            if term in self.keyword_index:
                docs = self.keyword_index[term]
                stats['document_frequency'] = len(docs)
                
                total_freq = 0
                for doc_id in docs:
                    tf = self._get_term_frequency(doc_id, term)
                    total_freq += tf
                    stats['documents'].append({'doc_id': doc_id, 'frequency': tf})
                
                stats['total_frequency'] = total_freq
                
                # 按频率排序
                stats['documents'].sort(key=lambda x: x['frequency'], reverse=True)
        
        except Exception as e:
            print(f"获取词项统计失败: {e}")
        
        return stats
    
    def suggest_terms(self, prefix: str, max_suggestions: int = 10) -> List[str]:
        """词项建议"""
        suggestions = []
        
        try:
            prefix_lower = prefix.lower()
            
            for term in self.keyword_index.keys():
                if term.startswith(prefix_lower):
                    suggestions.append(term)
            
            # 按文档频率排序
            suggestions.sort(key=lambda x: len(self.keyword_index.get(x, [])), reverse=True)
            
            return suggestions[:max_suggestions]
            
        except Exception as e:
            print(f"词项建议失败: {e}")
            return []
    
    def rebuild_index(self):
        """重建索引"""
        try:
            print("开始重建BM25索引...")
            
            # 重建关键词索引
            self.keyword_index = self._build_keyword_index()
            
            # 重新计算文档统计
            self.doc_stats = self._calculate_doc_stats()
            
            print("BM25索引重建完成")
            
        except Exception as e:
            print(f"重建索引失败: {e}")

if __name__ == "__main__":
    # 测试BM25检索器
    retriever = BM25Retriever("./data/knowledge_base/knowledge.db", "./data/knowledge_base/index")
    
    # 检查索引状态
    print(f"关键词索引大小: {len(retriever.keyword_index)}")
    print(f"文档统计: {retriever.doc_stats}")
    
    # 如果索引有问题，重建索引
    if not retriever.keyword_index or retriever.doc_stats.get('doc_count', 0) == 0:
        print("索引有问题，开始重建...")
        retriever.rebuild_index()
    
    # 测试基本检索
    print("\n测试BM25检索...")
    results = retriever.search("个人贷款 利率", top_k=5)
    print(f"检索到 {len(results)} 个结果")
    
    for result in results:
        print(f"- {result.get('chunk_id', 'N/A')}: {result.get('content', '')[:50]}...")
        print(f"  BM25得分: {result.get('bm25_score', 0):.3f}")
        print(f"  类型: {result.get('content_type', 'N/A')}")
    
    # 测试精确短语检索
    print("\n测试精确短语检索...")
    phrase_results = retriever.search_exact_phrase("个人消费贷款", top_k=3)
    print(f"短语检索到 {len(phrase_results)} 个结果")
    
    # 测试词项统计
    print("\n测试词项统计...")
    term_stats = retriever.get_term_statistics("贷款")
    print(f"词项'贷款'统计: 文档频率={term_stats['document_frequency']}, 总频率={term_stats['total_frequency']}")
    
    # 测试词项建议
    print("\n测试词项建议...")
    suggestions = retriever.suggest_terms("贷", max_suggestions=5)
    print(f"以'贷'开头的词项建议: {suggestions}")
    
    # 测试带过滤条件的检索
    print("\n测试过滤检索...")
    filters = {'content_type': 'product_basic_info'}
    filtered_results = retriever.search_with_filters("银行产品", filters, top_k=3)
    print(f"过滤检索到 {len(filtered_results)} 个结果")