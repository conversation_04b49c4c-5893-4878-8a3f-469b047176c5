# retrieval/vector_retriever.py
import os
import json
import pickle
import sqlite3
from typing import Dict, List, Any, Optional
import numpy as np
import faiss
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_embedding_api

class VectorRetriever:
    """向量检索器"""
    
    def __init__(self, db_path: str, index_dir: str = "./data/knowledge_base/index"):
        self.db_path = db_path
        self.index_dir = index_dir
        
        # 加载索引
        self.vector_index = self._load_vector_index()
        self.chunk_mapping = self._load_chunk_mapping()
        
        # 检索参数
        self.similarity_threshold = 0.3
        self.max_results = 100
    
    def _load_vector_index(self) -> Optional[faiss.Index]:
        """加载向量索引"""
        try:
            index_path = os.path.join(self.index_dir, "vector_index.faiss")
            if os.path.exists(index_path):
                return faiss.read_index(index_path)
            else:
                print(f"向量索引文件不存在: {index_path}")
                return None
        except Exception as e:
            print(f"加载向量索引失败: {e}")
            return None
    
    def _load_chunk_mapping(self) -> Optional[List[str]]:
        """加载chunk映射"""
        try:
            mapping_path = os.path.join(self.index_dir, "chunk_mapping.pkl")
            if os.path.exists(mapping_path):
                with open(mapping_path, 'rb') as f:
                    return pickle.load(f)
            else:
                print(f"chunk映射文件不存在: {mapping_path}")
                return None
        except Exception as e:
            print(f"加载chunk映射失败: {e}")
            return None
    
    def search(self, query: str, content_types: Optional[List[str]] = None, 
               top_k: int = 10) -> List[Dict[str, Any]]:
        """向量检索"""
        try:
            if not self.vector_index or not self.chunk_mapping:
                print("向量索引或映射未加载")
                return []
            
            # 获取查询向量
            query_vector = self._get_query_vector(query)
            if query_vector is None:
                return []
            
            # 执行向量搜索
            scores, indices = self._vector_search(query_vector, min(top_k * 5, self.max_results))
            
            # 获取检索结果
            results = self._get_search_results(indices, scores, content_types)
            
            # 过滤和排序
            filtered_results = self._filter_results(results, top_k)
            
            return filtered_results
            
        except Exception as e:
            print(f"向量检索失败: {e}")
            return []
    
    def search_by_embedding(self, embedding: List[float], content_types: Optional[List[str]] = None,
                           top_k: int = 10) -> List[Dict[str, Any]]:
        """通过向量检索"""
        try:
            if not self.vector_index or not self.chunk_mapping:
                return []
            
            # 转换为numpy数组并归一化
            query_vector = np.array([embedding], dtype=np.float32)
            faiss.normalize_L2(query_vector)
            
            # 执行向量搜索
            scores, indices = self._vector_search(query_vector, min(top_k * 5, self.max_results))
            
            # 获取检索结果
            results = self._get_search_results(indices, scores, content_types)
            
            # 过滤和排序
            filtered_results = self._filter_results(results, top_k)
            
            return filtered_results
            
        except Exception as e:
            print(f"向量检索失败: {e}")
            return []
    
    def find_similar_chunks(self, chunk_id: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """查找相似的文档块"""
        try:
            # 获取指定chunk的向量
            chunk_embedding = self._get_chunk_embedding(chunk_id)
            if chunk_embedding is None:
                return []
            
            # 使用该向量进行检索
            results = self.search_by_embedding(chunk_embedding, top_k=top_k + 1)
            
            # 排除自己
            filtered_results = [r for r in results if r.get('chunk_id') != chunk_id]
            
            return filtered_results[:top_k]
            
        except Exception as e:
            print(f"查找相似chunk失败: {e}")
            return []
    
    def _get_query_vector(self, query: str) -> Optional[np.ndarray]:
        """获取查询向量"""
        try:
            # 调用embedding API
            embedding = call_embedding_api(query)
            
            if not embedding or len(embedding) == 0:
                print("获取查询向量失败")
                return None
            
            # 转换为numpy数组并归一化
            query_vector = np.array([embedding], dtype=np.float32)
            faiss.normalize_L2(query_vector)
            
            return query_vector
            
        except Exception as e:
            print(f"获取查询向量失败: {e}")
            return None
    
    def _vector_search(self, query_vector: np.ndarray, k: int) -> tuple:
        """执行向量搜索"""
        try:
            # 确保k不超过索引大小
            actual_k = min(k, self.vector_index.ntotal)
            
            # 执行搜索
            scores, indices = self.vector_index.search(query_vector, actual_k)
            
            return scores[0], indices[0]
            
        except Exception as e:
            print(f"向量搜索失败: {e}")
            return np.array([]), np.array([])
    
    def _get_search_results(self, indices: np.ndarray, scores: np.ndarray, 
                           content_types: Optional[List[str]]) -> List[Dict[str, Any]]:
        """获取搜索结果"""
        results = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for idx, score in zip(indices, scores):
                    if idx < len(self.chunk_mapping):
                        chunk_id = self.chunk_mapping[idx]
                        
                        # 从数据库获取chunk信息
                        cursor.execute('''
                            SELECT content, content_type, keywords, category_path, metadata
                            FROM chunks WHERE chunk_id = ?
                        ''', (chunk_id,))
                        
                        row = cursor.fetchone()
                        if row:
                            content, content_type, keywords_json, category_path, metadata_json = row
                            
                            # 内容类型过滤
                            if content_types and content_type not in content_types:
                                continue
                            
                            # 解析JSON数据
                            try:
                                keywords = json.loads(keywords_json) if keywords_json else []
                                metadata = json.loads(metadata_json) if metadata_json else {}
                            except:
                                keywords = []
                                metadata = {}
                            
                            result = {
                                'chunk_id': chunk_id,
                                'content': content,
                                'content_type': content_type,
                                'keywords': keywords,
                                'category_path': category_path,
                                'metadata': metadata,
                                'similarity_score': float(score),
                                'retrieval_method': 'vector'
                            }
                            
                            results.append(result)
        
        except Exception as e:
            print(f"获取搜索结果失败: {e}")
        
        return results
    
    def _filter_results(self, results: List[Dict[str, Any]], top_k: int) -> List[Dict[str, Any]]:
        """过滤和排序结果"""
        # 按相似度过滤
        filtered = [r for r in results if r.get('similarity_score', 0) >= self.similarity_threshold]
        
        # 按相似度排序
        filtered.sort(key=lambda x: x.get('similarity_score', 0), reverse=True)
        
        # 去重（基于chunk_id）
        seen_chunks = set()
        unique_results = []
        for result in filtered:
            chunk_id = result.get('chunk_id')
            if chunk_id not in seen_chunks:
                seen_chunks.add(chunk_id)
                unique_results.append(result)
        
        return unique_results[:top_k]
    
    def _get_chunk_embedding(self, chunk_id: str) -> Optional[List[float]]:
        """获取指定chunk的向量"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT embedding FROM chunks WHERE chunk_id = ?', (chunk_id,))
                
                row = cursor.fetchone()
                if row and row[0]:
                    embedding_blob = row[0]
                    embedding_str = embedding_blob.decode('utf-8')
                    embedding = json.loads(embedding_str)
                    return embedding
                
                return None
                
        except Exception as e:
            print(f"获取chunk向量失败: {e}")
            return None
    
    def get_index_statistics(self) -> Dict[str, Any]:
        """获取索引统计信息"""
        stats = {}
        
        if self.vector_index:
            stats['total_vectors'] = self.vector_index.ntotal
            stats['vector_dimension'] = self.vector_index.d
            stats['index_type'] = type(self.vector_index).__name__
        
        if self.chunk_mapping:
            stats['total_chunks'] = len(self.chunk_mapping)
        
        # 数据库统计
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT COUNT(*) FROM chunks WHERE embedding IS NOT NULL')
                stats['chunks_with_embeddings'] = cursor.fetchone()[0]
                
                cursor.execute('SELECT content_type, COUNT(*) FROM chunks GROUP BY content_type')
                stats['chunks_by_type'] = dict(cursor.fetchall())
        
        except Exception as e:
            print(f"获取数据库统计失败: {e}")
        
        return stats
    
    def update_similarity_threshold(self, threshold: float):
        """更新相似度阈值"""
        self.similarity_threshold = max(0.0, min(1.0, threshold))
    
    def batch_search(self, queries: List[str], content_types: Optional[List[str]] = None,
                    top_k: int = 10) -> List[List[Dict[str, Any]]]:
        """批量检索"""
        results = []
        
        for query in queries:
            query_results = self.search(query, content_types, top_k)
            results.append(query_results)
        
        return results

if __name__ == "__main__":
    # 测试向量检索器
    retriever = VectorRetriever("./data/knowledge_base/knowledge.db", "./data/knowledge_base/index")
    
    # 检查索引状态
    stats = retriever.get_index_statistics()
    print(f"索引统计: {stats}")
    
    if stats.get('total_vectors', 0) > 0:
        # 测试基本检索
        print("\n测试基本检索...")
        results = retriever.search("个人贷款利率", top_k=5)
        print(f"检索到 {len(results)} 个结果")
        
        for result in results:
            print(f"- {result.get('chunk_id', 'N/A')}: {result.get('content', '')[:50]}...")
            print(f"  相似度: {result.get('similarity_score', 0):.3f}")
            print(f"  类型: {result.get('content_type', 'N/A')}")
        
        # 测试内容类型过滤
        print("\n测试内容类型过滤...")
        filtered_results = retriever.search("消费贷", content_types=['product_basic_info'], top_k=3)
        print(f"过滤后检索到 {len(filtered_results)} 个结果")
        
        # 测试相似chunk查找
        if results:
            first_chunk_id = results[0].get('chunk_id')
            if first_chunk_id:
                print(f"\n查找与 {first_chunk_id} 相似的chunk...")
                similar_chunks = retriever.find_similar_chunks(first_chunk_id, top_k=3)
                print(f"找到 {len(similar_chunks)} 个相似chunk")
    else:
        print("向量索引为空或未加载成功")