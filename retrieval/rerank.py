# retrieval/rerank.py
import json
from typing import Dict, List, Any, Optional
import numpy as np
import os 
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, parse_llm_json

class Reranker:
    """重排序器"""
    
    def __init__(self):
        # 重排序权重配置
        self.rerank_weights = {
            'relevance': 0.4,      # 相关性
            'quality': 0.3,        # 内容质量
            'freshness': 0.1,      # 时效性
            'authority': 0.2       # 权威性
        }
        
        # 内容质量评估标准
        self.quality_criteria = {
            'completeness': 0.3,   # 完整性
            'accuracy': 0.4,       # 准确性
            'clarity': 0.3         # 清晰度
        }
        
        # 权威性评分标准
        self.authority_scores = {
            'product_basic_info': 0.9,
            'policy_paragraph': 0.95,
            'case_analysis': 0.8,
            'dialog_qa': 0.7,
            'general': 0.6
        }
    
    def rerank(self, query: str, results: List[Dict[str, Any]], 
               method: str = "weighted", top_k: Optional[int] = None) -> List[Dict[str, Any]]:
        """重排序检索结果"""
        try:
            if not results:
                return []
            
            if method == "weighted":
                reranked = self._weighted_rerank(query, results)
            elif method == "llm":
                reranked = self._llm_rerank(query, results)
            elif method == "learning_to_rank":
                reranked = self._learning_to_rank(query, results)
            else:
                reranked = self._weighted_rerank(query, results)
            
            return reranked[:top_k] if top_k else reranked
            
        except Exception as e:
            print(f"重排序失败: {e}")
            return results
    
    def rerank_by_intent(self, query: str, intent: str, results: List[Dict[str, Any]],
                        top_k: Optional[int] = None) -> List[Dict[str, Any]]:
        """基于意图的重排序"""
        try:
            # 根据意图调整权重
            intent_weights = self._get_intent_weights(intent)
            
            # 使用调整后的权重进行重排序
            original_weights = self.rerank_weights.copy()
            self.rerank_weights.update(intent_weights)
            
            reranked = self._weighted_rerank(query, results)
            
            # 恢复原始权重
            self.rerank_weights = original_weights
            
            return reranked[:top_k] if top_k else reranked
            
        except Exception as e:
            print(f"意图重排序失败: {e}")
            return results
    
    def rerank_financial_results(self, query: str, results: List[Dict[str, Any]],
                                customer_profile: Optional[Dict[str, Any]] = None,
                                top_k: Optional[int] = None) -> List[Dict[str, Any]]:
        """金融专业重排序"""
        try:
            scored_results = []
            
            for result in results:
                # 计算金融专业相关性
                financial_score = self._calculate_financial_relevance(query, result)
                
                # 客户匹配度
                customer_match = 0.0
                if customer_profile:
                    customer_match = self._calculate_customer_match(result, customer_profile)
                
                # 风险适配度
                risk_score = self._calculate_risk_suitability(result, customer_profile)
                
                # 综合得分
                final_score = (
                    financial_score * 0.4 +
                    customer_match * 0.3 +
                    risk_score * 0.2 +
                    result.get('final_score', 0) * 0.1
                )
                
                result_copy = result.copy()
                result_copy['rerank_score'] = final_score
                result_copy['financial_relevance'] = financial_score
                result_copy['customer_match'] = customer_match
                result_copy['risk_suitability'] = risk_score
                
                scored_results.append(result_copy)
            
            # 按重排序得分排序
            scored_results.sort(key=lambda x: x.get('rerank_score', 0), reverse=True)
            
            return scored_results[:top_k] if top_k else scored_results
            
        except Exception as e:
            print(f"金融重排序失败: {e}")
            return results
    
    def _weighted_rerank(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """加权重排序"""
        scored_results = []
        
        try:
            for result in results:
                # 计算各项得分
                relevance_score = self._calculate_relevance(query, result)
                quality_score = self._calculate_quality(result)
                freshness_score = self._calculate_freshness(result)
                authority_score = self._calculate_authority(result)
                
                # 加权综合得分
                weighted_score = (
                    relevance_score * self.rerank_weights['relevance'] +
                    quality_score * self.rerank_weights['quality'] +
                    freshness_score * self.rerank_weights['freshness'] +
                    authority_score * self.rerank_weights['authority']
                )
                
                result_copy = result.copy()
                result_copy['rerank_score'] = weighted_score
                result_copy['relevance_score'] = relevance_score
                result_copy['quality_score'] = quality_score
                result_copy['freshness_score'] = freshness_score
                result_copy['authority_score'] = authority_score
                
                scored_results.append(result_copy)
            
            # 按得分排序
            scored_results.sort(key=lambda x: x.get('rerank_score', 0), reverse=True)
            
        except Exception as e:
            print(f"加权重排序失败: {e}")
        
        return scored_results
    
    def _llm_rerank(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """LLM重排序"""
        try:
            if len(results) <= 1:
                return results
            
            # 准备LLM输入
            result_texts = []
            for i, result in enumerate(results):
                content = result.get('content', '')[:200]  # 限制长度
                result_texts.append(f"{i}: {content}")
            
            prompt = f"""
            请根据查询"{query}"对以下检索结果进行重排序，按相关性从高到低排列。
            只需要返回序号的重新排列，格式为JSON数组，例如[2,0,1,3]。
            
            检索结果：
            {chr(10).join(result_texts)}
            """
            
            response = call_doubao_api_prompt(prompt)
            
            # 解析LLM返回的排序
            try:
                if response.strip().startswith('['):
                    new_order = json.loads(response.strip())
                else:
                    # 尝试从响应中提取JSON数组
                    import re
                    match = re.search(r'\[[\d,\s]+\]', response)
                    if match:
                        new_order = json.loads(match.group())
                    else:
                        return results
                
                # 应用新排序
                if len(new_order) == len(results):
                    reranked = [results[i] for i in new_order if 0 <= i < len(results)]
                    
                    # 添加LLM重排序标记
                    for i, result in enumerate(reranked):
                        result = result.copy()
                        result['llm_rank'] = i
                        reranked[i] = result
                    
                    return reranked
                
            except (json.JSONDecodeError, IndexError, KeyError):
                print("LLM重排序结果解析失败")
            
            return results
            
        except Exception as e:
            print(f"LLM重排序失败: {e}")
            return results
    
    def _learning_to_rank(self, query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """学习排序（简化版）"""
        try:
            # 提取特征
            features_matrix = []
            for result in results:
                features = self._extract_ranking_features(query, result)
                features_matrix.append(features)
            
            if not features_matrix:
                return results
            
            # 简化的排序算法（基于特征加权）
            feature_weights = [0.3, 0.25, 0.2, 0.15, 0.1]  # 假设有5个特征
            
            scored_results = []
            for i, result in enumerate(results):
                if i < len(features_matrix):
                    features = features_matrix[i]
                    score = sum(f * w for f, w in zip(features, feature_weights[:len(features)]))
                    
                    result_copy = result.copy()
                    result_copy['ltr_score'] = score
                    result_copy['features'] = features
                    scored_results.append(result_copy)
            
            # 按LTR得分排序
            scored_results.sort(key=lambda x: x.get('ltr_score', 0), reverse=True)
            
            return scored_results
            
        except Exception as e:
            print(f"学习排序失败: {e}")
            return results
    
    def _calculate_relevance(self, query: str, result: Dict[str, Any]) -> float:
        """计算相关性得分"""
        try:
            content = result.get('content', '').lower()
            query_lower = query.lower()
            
            # 精确匹配
            exact_match = 1.0 if query_lower in content else 0.0
            
            # 词项匹配
            query_terms = query_lower.split()
            content_terms = content.split()
            
            matched_terms = sum(1 for term in query_terms if term in content_terms)
            term_match = matched_terms / len(query_terms) if query_terms else 0.0
            
            # 关键词匹配
            keywords = result.get('keywords', [])
            keyword_match = sum(1 for term in query_terms if any(term in kw.lower() for kw in keywords))
            keyword_match = keyword_match / len(query_terms) if query_terms else 0.0
            
            # 综合相关性
            relevance = exact_match * 0.5 + term_match * 0.3 + keyword_match * 0.2
            
            return min(1.0, relevance)
            
        except Exception as e:
            print(f"计算相关性失败: {e}")
            return 0.0
    
    def _calculate_quality(self, result: Dict[str, Any]) -> float:
        """计算内容质量得分"""
        try:
            content = result.get('content', '')
            
            # 完整性评估
            completeness = min(1.0, len(content) / 200)  # 假设200字符为完整
            
            # 准确性评估（基于内容类型）
            content_type = result.get('content_type', '')
            if 'product' in content_type or 'policy' in content_type:
                accuracy = 0.9  # 产品和政策信息假定准确性较高
            elif 'case' in content_type:
                accuracy = 0.8  # 案例信息准确性中等
            else:
                accuracy = 0.7  # 其他内容准确性一般
            
            # 清晰度评估（基于内容结构）
            clarity = 0.8  # 简化评估
            if '：' in content or '。' in content:
                clarity += 0.1
            if len(content.split()) > 5:
                clarity += 0.1
            
            clarity = min(1.0, clarity)
            
            # 加权质量得分
            quality = (
                completeness * self.quality_criteria['completeness'] +
                accuracy * self.quality_criteria['accuracy'] +
                clarity * self.quality_criteria['clarity']
            )
            
            return quality
            
        except Exception as e:
            print(f"计算质量得分失败: {e}")
            return 0.5
    
    def _calculate_freshness(self, result: Dict[str, Any]) -> float:
        """计算时效性得分"""
        try:
            # 简化的时效性评估
            content_type = result.get('content_type', '')
            
            # 不同类型内容的时效性权重
            if 'policy' in content_type:
                return 0.9  # 政策信息时效性重要
            elif 'product' in content_type:
                return 0.8  # 产品信息时效性较重要
            elif 'case' in content_type:
                return 0.6  # 案例信息时效性一般
            else:
                return 0.7  # 其他信息时效性中等
                
        except Exception as e:
            print(f"计算时效性失败: {e}")
            return 0.5
    
    def _calculate_authority(self, result: Dict[str, Any]) -> float:
        """计算权威性得分"""
        try:
            content_type = result.get('content_type', '')
            
            # 根据内容类型确定权威性
            return self.authority_scores.get(content_type, self.authority_scores['general'])
            
        except Exception as e:
            print(f"计算权威性失败: {e}")
            return 0.5
    
    def _calculate_financial_relevance(self, query: str, result: Dict[str, Any]) -> float:
        """计算金融专业相关性"""
        try:
            content = result.get('content', '').lower()
            metadata = result.get('metadata', {})
            
            # 金融术语匹配
            financial_terms = [
                '贷款', '利率', '额度', '征信', '银行', '金融', '申请', '审批',
                '风险', '担保', '抵押', '还款', '期限', '费用'
            ]
            
            term_matches = sum(1 for term in financial_terms if term in content)
            term_score = min(1.0, term_matches / 5)
            
            # 产品相关性
            product_score = 0.0
            if 'product' in result.get('content_type', ''):
                product_score = 0.8
            elif 'case' in result.get('content_type', ''):
                product_score = 0.6
            
            # 元数据相关性
            metadata_score = 0.0
            if 'product_type' in metadata or 'institution' in metadata:
                metadata_score = 0.5
            
            return (term_score * 0.5 + product_score * 0.3 + metadata_score * 0.2)
            
        except Exception as e:
            print(f"计算金融相关性失败: {e}")
            return 0.0
    
    def _calculate_customer_match(self, result: Dict[str, Any], 
                                 customer_profile: Optional[Dict[str, Any]]) -> float:
        """计算客户匹配度"""
        try:
            if not customer_profile:
                return 0.5
            
            metadata = result.get('metadata', {})
            
            # 如果是案例，计算画像相似度
            if 'customer_profile' in metadata:
                case_profile = metadata['customer_profile']
                return self._calculate_profile_similarity(case_profile, customer_profile)
            
            # 如果是产品，计算适配度
            elif 'requirements' in metadata:
                requirements = metadata['requirements']
                return self._calculate_requirement_match(requirements, customer_profile)
            
            return 0.5
            
        except Exception as e:
            print(f"计算客户匹配度失败: {e}")
            return 0.5
    
    def _calculate_profile_similarity(self, case_profile: Dict[str, Any], 
                                    customer_profile: Dict[str, Any]) -> float:
        """计算客户画像相似度"""
        try:
            similarity = 0.0
            factors = 0
            
            # 年龄相似度
            if 'age' in case_profile and 'age' in customer_profile:
                age_diff = abs(case_profile['age'] - customer_profile['age'])
                age_similarity = max(0, 1 - age_diff / 20)
                similarity += age_similarity
                factors += 1
            
            # 收入相似度
            if 'monthly_income' in case_profile and 'monthly_income' in customer_profile:
                case_income = case_profile['monthly_income']
                customer_income = customer_profile['monthly_income']
                income_ratio = min(case_income, customer_income) / max(case_income, customer_income)
                similarity += income_ratio
                factors += 1
            
            # 征信相似度
            if 'credit_score' in case_profile and 'credit_score' in customer_profile:
                score_diff = abs(case_profile['credit_score'] - customer_profile['credit_score'])
                credit_similarity = max(0, 1 - score_diff / 150)
                similarity += credit_similarity
                factors += 1
            
            return similarity / factors if factors > 0 else 0.5
            
        except Exception as e:
            print(f"计算画像相似度失败: {e}")
            return 0.5
    
    def _calculate_requirement_match(self, requirements: Dict[str, Any], 
                                   customer_profile: Dict[str, Any]) -> float:
        """计算需求匹配度"""
        try:
            match_score = 0.0
            total_requirements = 0
            
            # 检查各项要求
            for req_key, req_value in requirements.items():
                total_requirements += 1
                
                if req_key == 'credit_score' and 'credit_score' in customer_profile:
                    # 解析征信要求
                    if '≥' in str(req_value):
                        min_score = int(req_value.replace('≥', ''))
                        if customer_profile['credit_score'] >= min_score:
                            match_score += 1.0
                        else:
                            match_score += 0.5  # 部分满足
                
                elif req_key == 'monthly_income' and 'monthly_income' in customer_profile:
                    # 解析收入要求
                    if '≥' in str(req_value):
                        min_income = int(req_value.replace('≥', '').replace('元', ''))
                        if customer_profile['monthly_income'] >= min_income:
                            match_score += 1.0
                        else:
                            match_score += 0.3
                
                elif req_key == 'age_range' and 'age' in customer_profile:
                    # 解析年龄要求
                    age_range = str(req_value)
                    if '-' in age_range:
                        min_age, max_age = age_range.split('-')
                        min_age = int(min_age)
                        max_age = int(max_age.replace('岁', ''))
                        if min_age <= customer_profile['age'] <= max_age:
                            match_score += 1.0
                        else:
                            match_score += 0.2
                
                else:
                    match_score += 0.5  # 默认部分满足
            
            return match_score / total_requirements if total_requirements > 0 else 0.5
            
        except Exception as e:
            print(f"计算需求匹配度失败: {e}")
            return 0.5
    
    def _calculate_risk_suitability(self, result: Dict[str, Any], 
                                   customer_profile: Optional[Dict[str, Any]]) -> float:
        """计算风险适配度"""
        try:
            if not customer_profile:
                return 0.5
            
            # 简化的风险评估
            credit_score = customer_profile.get('credit_score', 650)
            monthly_income = customer_profile.get('monthly_income', 5000)
            
            # 客户风险等级
            if credit_score >= 700 and monthly_income >= 10000:
                customer_risk = 'low'
            elif credit_score >= 600 and monthly_income >= 5000:
                customer_risk = 'medium'
            else:
                customer_risk = 'high'
            
            # 内容风险适配
            content_type = result.get('content_type', '')
            metadata = result.get('metadata', {})
            
            risk_suitability = 0.5
            
            if 'product' in content_type:
                # 产品风险适配
                product_type = metadata.get('product_type', '')
                if customer_risk == 'low':
                    risk_suitability = 0.9  # 低风险客户适合所有产品
                elif customer_risk == 'medium':
                    if product_type in ['消费贷', '车贷']:
                        risk_suitability = 0.8
                    else:
                        risk_suitability = 0.6
                else:  # high risk
                    if product_type in ['抵押贷']:
                        risk_suitability = 0.7
                    else:
                        risk_suitability = 0.3
            
            elif 'case' in content_type:
                # 案例风险适配
                case_risk = metadata.get('risk_level', '')
                if customer_risk == case_risk:
                    risk_suitability = 0.9
                else:
                    risk_suitability = 0.4
            
            return risk_suitability
            
        except Exception as e:
            print(f"计算风险适配度失败: {e}")
            return 0.5
    
    def _get_intent_weights(self, intent: str) -> Dict[str, float]:
        """获取意图相关的权重配置"""
        intent_weight_map = {
            '产品咨询': {'relevance': 0.5, 'quality': 0.3, 'authority': 0.2, 'freshness': 0.0},
            '资质评估': {'relevance': 0.4, 'quality': 0.2, 'authority': 0.1, 'freshness': 0.3},
            '比较分析': {'relevance': 0.6, 'quality': 0.3, 'authority': 0.1, 'freshness': 0.0},
            '申请指导': {'relevance': 0.4, 'quality': 0.4, 'authority': 0.2, 'freshness': 0.0},
            '合规咨询': {'relevance': 0.3, 'quality': 0.2, 'authority': 0.4, 'freshness': 0.1}
        }
        
        return intent_weight_map.get(intent, self.rerank_weights)
    
    def _extract_ranking_features(self, query: str, result: Dict[str, Any]) -> List[float]:
        """提取排序特征"""
        try:
            features = []
            
            # 特征1：相关性得分
            features.append(self._calculate_relevance(query, result))
            
            # 特征2：内容长度（归一化）
            content_length = len(result.get('content', ''))
            features.append(min(1.0, content_length / 500))
            
            # 特征3：关键词匹配数
            keywords = result.get('keywords', [])
            query_terms = query.lower().split()
            keyword_matches = sum(1 for term in query_terms if any(term in kw.lower() for kw in keywords))
            features.append(keyword_matches / len(query_terms) if query_terms else 0.0)
            
            # 特征4：内容类型权重
            content_type = result.get('content_type', '')
            type_weights = {
                'product_basic_info': 1.0,
                'case_analysis': 0.8,
                'policy_paragraph': 0.9,
                'dialog_qa': 0.6
            }
            features.append(type_weights.get(content_type, 0.5))
            
            # 特征5：原始检索得分
            original_score = result.get('final_score', result.get('similarity_score', 
                           result.get('bm25_score', result.get('financial_score', 0))))
            features.append(min(1.0, original_score))
            
            return features
            
        except Exception as e:
            print(f"提取排序特征失败: {e}")
            return [0.5] * 5

if __name__ == "__main__":
    # 测试重排序器
    reranker = Reranker()
    
    # 模拟检索结果
    test_results = [
        {
            'chunk_id': 'CHUNK001',
            'content': '个人消费贷款是银行提供的信用贷款产品，年化利率4.35%-18%，额度1-50万元。',
            'content_type': 'product_basic_info',
            'keywords': ['个人消费贷', '利率', '额度'],
            'metadata': {'product_type': '消费贷', 'institution': '工商银行'},
            'final_score': 0.8
        },
        {
            'chunk_id': 'CHUNK002',
            'content': '客户张三，30岁软件工程师，月收入12000元，申请消费贷20万元获批。',
            'content_type': 'case_analysis',
            'keywords': ['案例', '软件工程师', '消费贷'],
            'metadata': {'customer_profile': {'age': 30, 'monthly_income': 12000, 'credit_score': 720}},
            'final_score': 0.6
        },
        {
            'chunk_id': 'CHUNK003',
            'content': '银保监会规定个人贷款用途应当明确、合法，金额应与还款能力匹配。',
            'content_type': 'policy_paragraph',
            'keywords': ['银保监会', '个人贷款', '合规'],
            'metadata': {'policy_type': '监管政策'},
            'final_score': 0.7
        }
    ]
    
    query = "个人消费贷款利率"
    
    # 测试加权重排序
    print("测试加权重排序...")
    weighted_results = reranker.rerank(query, test_results, method="weighted")
    
    for i, result in enumerate(weighted_results):
        print(f"{i+1}. {result['chunk_id']}: {result['content'][:50]}...")
        print(f"   重排序得分: {result.get('rerank_score', 0):.3f}")
        print(f"   相关性: {result.get('relevance_score', 0):.3f}, "
              f"质量: {result.get('quality_score', 0):.3f}, "
              f"权威性: {result.get('authority_score', 0):.3f}")
    
    # 测试意图重排序
    print("\n测试意图重排序...")
    intent_results = reranker.rerank_by_intent(query, "产品咨询", test_results)
    
    for i, result in enumerate(intent_results):
        print(f"{i+1}. {result['chunk_id']}: 重排序得分 {result.get('rerank_score', 0):.3f}")
    
    # 测试金融专业重排序
    print("\n测试金融专业重排序...")
    customer_profile = {'age': 32, 'monthly_income': 10000, 'credit_score': 680}
    financial_results = reranker.rerank_financial_results(query, test_results, customer_profile)
    
    for i, result in enumerate(financial_results):
        print(f"{i+1}. {result['chunk_id']}: 综合得分 {result.get('rerank_score', 0):.3f}")
        print(f"   金融相关性: {result.get('financial_relevance', 0):.3f}, "
              f"客户匹配: {result.get('customer_match', 0):.3f}, "
              f"风险适配: {result.get('risk_suitability', 0):.3f}")