[{"case_id": "RISK00001", "customer_info": {"age": 56, "monthly_income": 25487, "credit_score": 746, "occupation": "企业高级管理人员", "application_amount": "500000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "20%", "employment_years": "30年"}, "credit_factors": {"overdue_records": "0次", "credit_history_length": "25年"}, "behavioral_factors": {"payment_score": "90分", "consumption_stability": "高"}}, "assessment_result": {"total_risk_score": "20分", "risk_level": "低风险", "default_probability": "2%", "risk_factors": ["年龄较大可能面临退休后收入减少风险"], "mitigation_suggestions": ["要求提供额外的资产证明", "缩短贷款期限"]}, "business_decision": {"final_decision": "批准", "approved_amount": "400000元", "interest_rate": "4%", "conditions": ["提供房产作为抵押", "贷款期限不超过5年"]}}, {"case_id": "RISK00003", "customer_info": {"age": 65, "monthly_income": 20618, "credit_score": 659, "occupation": "退休人员", "application_amount": "500000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "20%", "employment_years": "35年"}, "credit_factors": {"overdue_records": "0次", "credit_history_length": "20年"}, "behavioral_factors": {"payment_score": "80分", "consumption_stability": "较稳定"}}, "assessment_result": {"total_risk_score": "60分", "risk_level": "中等风险", "default_probability": "15%", "risk_factors": ["年龄较大，还款能力可能随年龄增长而下降"], "mitigation_suggestions": ["要求提供抵押物或担保人"]}, "business_decision": {"final_decision": "批准", "approved_amount": "300000元", "interest_rate": "8%", "conditions": ["需提供房产作为抵押物", "贷款期限不超过5年"]}}, {"case_id": "RISK00002", "customer_info": {"age": 46, "monthly_income": 8030, "credit_score": 359, "occupation": "企业职员", "application_amount": "100000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "40%", "employment_years": "15年"}, "credit_factors": {"overdue_records": "3次", "credit_history_length": "10年"}, "behavioral_factors": {"payment_score": "60分", "consumption_stability": "一般"}}, "assessment_result": {"total_risk_score": "70分", "risk_level": "中等", "default_probability": "20%", "risk_factors": ["征信分数较低", "有逾期记录"], "mitigation_suggestions": ["要求客户提供抵押物", "增加共同借款人"]}, "business_decision": {"final_decision": "批准", "approved_amount": "60000元", "interest_rate": "8%", "conditions": ["客户需提供价值80000元的房产作为抵押", "共同借款人需有稳定收入"]}}, {"case_id": "RISK00004", "customer_info": {"age": 33, "monthly_income": 9573, "credit_score": 623, "occupation": "软件工程师", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "30%", "employment_years": "8年"}, "credit_factors": {"overdue_records": "2次", "credit_history_length": "6年"}, "behavioral_factors": {"payment_score": "70分", "consumption_stability": "中等"}}, "assessment_result": {"total_risk_score": "60分", "risk_level": "中等风险", "default_probability": "15%", "risk_factors": ["有2次逾期记录"], "mitigation_suggestions": ["要求客户提供额外的担保措施", "缩短还款期限"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000元", "interest_rate": "8%", "conditions": ["客户需提供房产作为抵押", "每月按时还款"]}}, {"case_id": "RISK00005", "customer_info": {"age": 64, "monthly_income": 23825, "credit_score": 418, "occupation": "退休人员", "application_amount": "500000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "40%", "employment_years": "38年"}, "credit_factors": {"overdue_records": "3次", "credit_history_length": "20年"}, "behavioral_factors": {"payment_score": "60分", "consumption_stability": "一般"}}, "assessment_result": {"total_risk_score": "70分", "risk_level": "中高风险", "default_probability": "20%", "risk_factors": ["年龄较大临近退休", "征信分数较低", "有逾期记录"], "mitigation_suggestions": ["要求提供抵押物", "增加共同借款人"]}, "business_decision": {"final_decision": "有条件批准", "approved_amount": "300000元", "interest_rate": "8%", "conditions": ["提供价值不低于400000元的房产作为抵押", "增加有稳定收入的共同借款人"]}}, {"case_id": "RISK00006", "customer_info": {"age": 57, "monthly_income": 27114, "credit_score": 568, "occupation": "企业中层管理人员", "application_amount": "500000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "40%", "employment_years": "25年"}, "credit_factors": {"overdue_records": "3次", "credit_history_length": "18年"}, "behavioral_factors": {"payment_score": "60分", "consumption_stability": "中等"}}, "assessment_result": {"total_risk_score": "70分", "risk_level": "中等风险", "default_probability": "15%", "risk_factors": ["征信分数较低", "有逾期记录"], "mitigation_suggestions": ["要求提供抵押物", "增加共同借款人"]}, "business_decision": {"final_decision": "批准", "approved_amount": "300000元", "interest_rate": "8%", "conditions": ["提供价值不低于400000元的房产作为抵押", "增加信用良好的共同借款人"]}}, {"case_id": "RISK00007", "customer_info": {"age": 62, "monthly_income": 11315, "credit_score": 663, "occupation": "退休教师", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "20%", "employment_years": "38年"}, "credit_factors": {"overdue_records": "0次", "credit_history_length": "20年"}, "behavioral_factors": {"payment_score": "80分", "consumption_stability": "较稳定"}}, "assessment_result": {"total_risk_score": "30分", "risk_level": "低风险", "default_probability": "5%", "risk_factors": ["年龄较大，未来收入稳定性可能降低"], "mitigation_suggestions": ["要求提供子女作为共同还款人"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000元", "interest_rate": "5%", "conditions": ["需提供子女作为共同还款人", "按季还款"]}}, {"case_id": "RISK00011", "customer_info": {"age": 35, "monthly_income": 18663, "credit_score": 660, "occupation": "企业中层管理人员", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "30%", "employment_years": "8年"}, "credit_factors": {"overdue_records": "1次", "credit_history_length": "6年"}, "behavioral_factors": {"payment_score": "80分", "consumption_stability": "稳定"}}, "assessment_result": {"total_risk_score": "65分", "risk_level": "中等风险", "default_probability": "10%", "risk_factors": ["有1次逾期记录"], "mitigation_suggestions": ["要求提供额外的担保措施", "缩短贷款期限"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000元", "interest_rate": "8%", "conditions": ["需提供房产作为抵押", "贷款期限缩短至3年"]}}, {"case_id": "RISK00008", "customer_info": {"age": 43, "monthly_income": 19921, "credit_score": 372, "occupation": "企业中层管理人员", "application_amount": "300000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "40%", "employment_years": "15年"}, "credit_factors": {"overdue_records": "3次", "credit_history_length": "12年"}, "behavioral_factors": {"payment_score": "60分", "consumption_stability": "一般"}}, "assessment_result": {"total_risk_score": "70分", "risk_level": "中等风险", "default_probability": "15%", "risk_factors": ["征信分数较低", "有逾期记录", "还款行为评分不高"], "mitigation_suggestions": ["要求提供抵押物", "增加共同借款人", "缩短贷款期限"]}, "business_decision": {"final_decision": "批准贷款", "approved_amount": "200000元", "interest_rate": "8%", "conditions": ["需提供房产作为抵押物", "贷款期限缩短至3年"]}}, {"case_id": "RISK00010", "customer_info": {"age": 26, "monthly_income": 25381, "credit_score": 476, "occupation": "软件工程师", "application_amount": "200000"}, "assessment_input": {"financial_factors": {"debt_to_income": "30%", "employment_years": "3年"}, "credit_factors": {"overdue_records": "2次", "credit_history_length": "4年"}, "behavioral_factors": {"payment_score": "60分", "consumption_stability": "一般"}}, "assessment_result": {"total_risk_score": "65分", "risk_level": "中等风险", "default_probability": "15%", "risk_factors": ["征信分数较低", "有逾期记录"], "mitigation_suggestions": ["要求提供抵押物", "增加共同借款人"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000", "interest_rate": "8%", "conditions": ["提供价值200000元的房产作为抵押", "增加信用良好的共同借款人"]}}, {"case_id": "RISK00009", "customer_info": {"age": 59, "monthly_income": 19535, "credit_score": 574, "occupation": "中学教师", "application_amount": "500000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "30%", "employment_years": "35年"}, "credit_factors": {"overdue_records": "2次", "credit_history_length": "20年"}, "behavioral_factors": {"payment_score": "70分", "consumption_stability": "较稳定"}}, "assessment_result": {"total_risk_score": "65分", "risk_level": "中等风险", "default_probability": "15%", "risk_factors": ["年龄接近退休", "征信分数一般", "有逾期记录"], "mitigation_suggestions": ["要求提供额外的抵押物", "增加共同借款人"]}, "business_decision": {"final_decision": "批准", "approved_amount": "300000元", "interest_rate": "7%", "conditions": ["提供房产作为抵押", "增加一位有稳定收入的共同借款人"]}}, {"case_id": "RISK00012", "customer_info": {"age": 51, "monthly_income": 19514, "credit_score": 684, "occupation": "企业中层管理人员", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "25%", "employment_years": "20年"}, "credit_factors": {"overdue_records": "0次", "credit_history_length": "15年"}, "behavioral_factors": {"payment_score": "85分", "consumption_stability": "高"}}, "assessment_result": {"total_risk_score": "30分", "risk_level": "低风险", "default_probability": "5%", "risk_factors": [], "mitigation_suggestions": ["持续关注客户的财务状况和信用记录"]}, "business_decision": {"final_decision": "批准", "approved_amount": "200000元", "interest_rate": "5%", "conditions": ["客户需按合同约定按时还款"]}}, {"case_id": "RISK00013", "customer_info": {"age": 44, "monthly_income": 23223, "credit_score": 618, "occupation": "企业中层管理人员", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "30%", "employment_years": "15年"}, "credit_factors": {"overdue_records": "2次", "credit_history_length": "12年"}, "behavioral_factors": {"payment_score": "70分", "consumption_stability": "较稳定"}}, "assessment_result": {"total_risk_score": "65分", "risk_level": "中等风险", "default_probability": "10%", "risk_factors": ["有2次逾期记录"], "mitigation_suggestions": ["要求提供额外的担保措施", "缩短贷款期限"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000元", "interest_rate": "8%", "conditions": ["需提供房产作为抵押", "每月还款额不超过月收入的40%"]}}, {"case_id": "RISK00016", "customer_info": {"age": 43, "monthly_income": 9475, "credit_score": 724, "occupation": "企业中层管理人员", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "25%", "employment_years": "15年"}, "credit_factors": {"overdue_records": "0次", "credit_history_length": "12年"}, "behavioral_factors": {"payment_score": "85分", "consumption_stability": "高"}}, "assessment_result": {"total_risk_score": "30分", "risk_level": "低风险", "default_probability": "5%", "risk_factors": [], "mitigation_suggestions": ["持续关注客户的财务状况和信用变化"]}, "business_decision": {"final_decision": "批准", "approved_amount": "200000元", "interest_rate": "5%", "conditions": ["客户需按时还款，如有逾期需承担相应违约责任"]}}, {"case_id": "RISK00014", "customer_info": {"age": 27, "monthly_income": 25572, "credit_score": 366, "occupation": "软件工程师", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "30%", "employment_years": "3年"}, "credit_factors": {"overdue_records": "2次", "credit_history_length": "5年"}, "behavioral_factors": {"payment_score": "60分", "consumption_stability": "中等"}}, "assessment_result": {"total_risk_score": "65分", "risk_level": "中等风险", "default_probability": "15%", "risk_factors": ["征信分数较低", "有逾期记录"], "mitigation_suggestions": ["提供抵押物", "增加共同借款人"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000元", "interest_rate": "8%", "conditions": ["需提供房产作为抵押物", "增加信用良好的共同借款人"]}}, {"case_id": "RISK00017", "customer_info": {"age": 41, "monthly_income": 13108, "credit_score": 654, "occupation": "软件工程师", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "30%", "employment_years": "10年"}, "credit_factors": {"overdue_records": "1次", "credit_history_length": "8年"}, "behavioral_factors": {"payment_score": "80分", "consumption_stability": "稳定"}}, "assessment_result": {"total_risk_score": "60分", "risk_level": "中等风险", "default_probability": "10%", "risk_factors": ["有1次逾期记录"], "mitigation_suggestions": ["要求客户提供额外的担保措施", "缩短贷款期限"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000元", "interest_rate": "8%", "conditions": ["客户需提供房产作为抵押", "每月按时还款"]}}, {"case_id": "RISK00015", "customer_info": {"age": 29, "monthly_income": 15766, "credit_score": 503, "occupation": "软件工程师", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "30%", "employment_years": "3年"}, "credit_factors": {"overdue_records": "2次", "credit_history_length": "5年"}, "behavioral_factors": {"payment_score": "70分", "consumption_stability": "中等"}}, "assessment_result": {"total_risk_score": "60分", "risk_level": "中等风险", "default_probability": "15%", "risk_factors": ["征信分数较低", "有逾期记录"], "mitigation_suggestions": ["提供额外的抵押物", "增加共同借款人"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000元", "interest_rate": "8%", "conditions": ["需提供价值不低于100000元的抵押物", "共同借款人需有良好的信用记录"]}}, {"case_id": "RISK00018", "customer_info": {"age": 53, "monthly_income": 13028, "credit_score": 403, "occupation": "企业中层管理人员", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "40%", "employment_years": "20年"}, "credit_factors": {"overdue_records": "3次", "credit_history_length": "15年"}, "behavioral_factors": {"payment_score": "60分", "consumption_stability": "中等"}}, "assessment_result": {"total_risk_score": "70分", "risk_level": "中等风险", "default_probability": "15%", "risk_factors": ["征信分数较低", "有逾期记录"], "mitigation_suggestions": ["要求提供抵押物", "增加共同借款人"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000元", "interest_rate": "8%", "conditions": ["提供价值200000元以上的房产作为抵押", "增加信用良好的共同借款人"]}}, {"case_id": "RISK00019", "customer_info": {"age": 58, "monthly_income": 11752, "credit_score": 619, "occupation": "企业中层管理人员", "application_amount": "200000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "30%", "employment_years": "30年"}, "credit_factors": {"overdue_records": "2次", "credit_history_length": "20年"}, "behavioral_factors": {"payment_score": "70分", "consumption_stability": "较稳定"}}, "assessment_result": {"total_risk_score": "65分", "risk_level": "中等风险", "default_probability": "10%", "risk_factors": ["年龄较大，接近退休年龄", "有2次逾期记录"], "mitigation_suggestions": ["要求提供额外的担保措施", "缩短贷款期限"]}, "business_decision": {"final_decision": "批准", "approved_amount": "150000元", "interest_rate": "7%", "conditions": ["需提供房产作为抵押担保", "贷款期限缩短至3年"]}}, {"case_id": "RISK00020", "customer_info": {"age": 30, "monthly_income": 20055, "credit_score": 623, "occupation": "软件工程师", "application_amount": "500000元"}, "assessment_input": {"financial_factors": {"debt_to_income": "20%", "employment_years": "5年"}, "credit_factors": {"overdue_records": "1次", "credit_history_length": "8年"}, "behavioral_factors": {"payment_score": "70分", "consumption_stability": "较稳定"}}, "assessment_result": {"total_risk_score": "60分", "risk_level": "中等风险", "default_probability": "10%", "risk_factors": ["有一次逾期记录"], "mitigation_suggestions": ["要求提供额外的担保措施", "缩短贷款期限"]}, "business_decision": {"final_decision": "批准", "approved_amount": "300000元", "interest_rate": "7%", "conditions": ["提供房产作为抵押", "每月按时还款"]}}]