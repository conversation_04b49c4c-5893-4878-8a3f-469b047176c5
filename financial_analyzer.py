# simplified_financial_analyzer.py
import json
import re
from typing import Dict, List, Any, Optional
from datetime import datetime
from llm_api import call_deepseek_api, parse_llm_json

class FinancialAnalyzer:
    """基于大模型的金融分析器"""
    
    def __init__(self):
        self.system_prompt = """你是专业的金融分析助手，具备以下能力：
1. 分析个人财务健康状况
2. 评估贷款能力和风险
3. 提供专业的理财建议
4. 生成结构化的分析报告

请基于提供的数据进行专业分析，给出量化评分和具体建议。"""

    def analyze_financial_health(self, customer_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析财务健康状况"""
        try:
            prompt = f"""
请分析以下客户的财务健康状况，返回JSON格式结果：

客户信息：
- 年龄：{customer_data.get('age', 'N/A')}岁
- 月收入：{customer_data.get('monthly_income', 0)}元
- 月支出：{customer_data.get('monthly_expenses', 0)}元
- 总资产：{customer_data.get('total_assets', 0)}元
- 总负债：{customer_data.get('total_debts', 0)}元
- 征信评分：{customer_data.get('credit_score', 650)}分

请返回以下JSON格式：
{{
    "health_score": 85,
    "grade": "优秀/良好/一般/需改进",
    "metrics": {{
        "debt_to_income_ratio": 0.3,
        "savings_rate": 0.2,
        "net_worth": 400000,
        "liquidity_ratio": 0.15
    }},
    "risk_level": "低/中/高",
    "suggestions": ["建议1", "建议2", "建议3"],
    "analysis_summary": "整体分析总结"
}}
"""
            
            response = call_deepseek_api([
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ])
            
            result = parse_llm_json(response)
            result["analysis_date"] = datetime.now().strftime("%Y-%m-%d")
            return result
            
        except Exception as e:
            return {"error": f"财务健康分析失败: {e}"}

    def analyze_loan_capacity(self, customer_data: Dict[str, Any], 
                             loan_request: Dict[str, Any]) -> Dict[str, Any]:
        """分析贷款能力"""
        try:
            prompt = f"""
请分析以下客户的贷款能力，返回JSON格式结果：

客户信息：
- 月收入：{customer_data.get('monthly_income', 0)}元
- 月支出：{customer_data.get('monthly_expenses', 0)}元
- 征信评分：{customer_data.get('credit_score', 650)}分
- 现有负债：{customer_data.get('total_debts', 0)}元
- 工作年限：{customer_data.get('work_years', 3)}年

贷款需求：
- 申请金额：{loan_request.get('amount', 0)}元
- 贷款期限：{loan_request.get('term_months', 36)}个月
- 贷款用途：{loan_request.get('purpose', '消费')}

请返回以下JSON格式：
{{
    "approval_probability": 0.85,
    "recommended_amount": 200000,
    "estimated_rate": 0.08,
    "monthly_payment": 5000,
    "max_loan_capacity": 300000,
    "risk_assessment": "低风险/中风险/高风险",
    "approval_conditions": ["条件1", "条件2"],
    "rejection_reasons": [],
    "recommendation": "推荐/谨慎/不推荐"
}}
"""
            
            response = call_deepseek_api([
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ])
            
            return parse_llm_json(response)
            
        except Exception as e:
            return {"error": f"贷款能力分析失败: {e}"}

    def generate_investment_advice(self, customer_data: Dict[str, Any], 
                                 investment_profile: Dict[str, Any]) -> Dict[str, Any]:
        """生成投资建议"""
        try:
            prompt = f"""
根据客户信息和投资偏好，提供个性化投资建议：

客户信息：
- 年龄：{customer_data.get('age', 30)}岁
- 月收入：{customer_data.get('monthly_income', 0)}元
- 可投资金额：{investment_profile.get('investment_amount', 0)}元
- 风险承受能力：{investment_profile.get('risk_tolerance', '中等')}
- 投资期限：{investment_profile.get('investment_horizon', '3-5年')}
- 投资目标：{investment_profile.get('investment_goal', '资产增值')}

请返回JSON格式的投资建议：
{{
    "asset_allocation": {{
        "stocks": 40,
        "bonds": 30,
        "cash": 20,
        "alternatives": 10
    }},
    "recommended_products": [
        {{"name": "产品名", "type": "基金/股票/债券", "allocation": 30, "expected_return": 8, "risk_level": "中等"}},
    ],
    "investment_strategy": "投资策略描述",
    "risk_warnings": ["风险提示1", "风险提示2"],
    "rebalancing_frequency": "季度/半年/年度"
}}
"""
            
            response = call_deepseek_api([
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ])
            
            return parse_llm_json(response)
            
        except Exception as e:
            return {"error": f"投资建议生成失败: {e}"}

    def generate_comprehensive_report(self, customer_data: Dict[str, Any]) -> str:
        """生成综合财务报告"""
        try:
            prompt = f"""
生成详细的个人财务分析报告，包含以下内容：

客户信息：
{json.dumps(customer_data, ensure_ascii=False, indent=2)}

请生成一份专业的财务分析报告，包括：
1. 财务现状概览
2. 财务健康度评估
3. 风险分析
4. 改进建议
5. 未来规划建议

报告应该专业、详细且易于理解。
"""
            
            response = call_deepseek_api([
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ])
            
            return response
            
        except Exception as e:
            return f"报告生成失败: {e}"


class DialogEvaluator:
    """基于大模型的对话评估器"""
    
    def __init__(self):
        self.system_prompt = """你是专业的金融客服对话质量评估专家，负责评估客服回复的质量。

评估维度包括：
1. 准确性 - 信息是否准确无误
2. 相关性 - 是否针对用户问题
3. 完整性 - 信息是否全面
4. 专业性 - 是否体现专业水准
5. 合规性 - 是否符合金融监管要求

请给出客观、专业的评估结果。"""

    def evaluate_response(self, query: str, response: str) -> Dict[str, Any]:
        """评估单个回复"""
        try:
            prompt = f"""
请评估以下客服对话的质量：

用户问题：{query}

客服回复：{response}

请返回JSON格式的评估结果：
{{
    "overall_score": 85,
    "grade": "优秀/良好/一般/需改进",
    "dimension_scores": {{
        "accuracy": 90,
        "relevance": 85,
        "completeness": 80,
        "professionalism": 85,
        "compliance": 90
    }},
    "strengths": ["优点1", "优点2"],
    "weaknesses": ["不足1", "不足2"],
    "suggestions": ["改进建议1", "改进建议2"],
    "compliance_issues": ["合规问题1"],
    "risk_level": "低/中/高"
}}
"""
            
            response_eval = call_deepseek_api([
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ])
            
            result = parse_llm_json(response_eval)
            result["query"] = query
            result["response"] = response
            return result
            
        except Exception as e:
            return {"error": f"评估失败: {e}"}

    def compare_responses(self, query: str, responses: List[str]) -> Dict[str, Any]:
        """对比多个回复"""
        try:
            responses_text = "\n\n".join([f"回复{i+1}：{resp}" for i, resp in enumerate(responses)])
            
            prompt = f"""
请对比评估以下多个客服回复的质量：

用户问题：{query}

{responses_text}

请返回JSON格式的对比结果：
{{
    "rankings": [
        {{"rank": 1, "response_id": 2, "score": 85, "reason": "排名理由"}},
        {{"rank": 2, "response_id": 1, "score": 75, "reason": "排名理由"}}
    ],
    "best_response": {{
        "response_id": 2,
        "score": 85,
        "highlights": ["亮点1", "亮点2"]
    }},
    "common_issues": ["共同问题1", "共同问题2"],
    "improvement_suggestions": ["整体改进建议1", "整体改进建议2"]
}}
"""
            
            comparison = call_deepseek_api([
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ])
            
            result = parse_llm_json(comparison)
            result["query"] = query
            result["responses"] = responses
            return result
            
        except Exception as e:
            return {"error": f"对比失败: {e}"}

    def batch_evaluate(self, dialogues: List[Dict[str, str]]) -> Dict[str, Any]:
        """批量评估对话"""
        try:
            dialogue_text = "\n\n".join([
                f"对话{i+1}：\n用户：{d['query']}\n客服：{d['response']}" 
                for i, d in enumerate(dialogues)
            ])
            
            prompt = f"""
请批量评估以下客服对话质量：

{dialogue_text}

请返回JSON格式的批量评估结果：
{{
    "overall_statistics": {{
        "average_score": 78,
        "score_distribution": {{"优秀": 2, "良好": 5, "一般": 3, "需改进": 0}},
        "total_dialogues": 10
    }},
    "individual_scores": [
        {{"dialogue_id": 1, "score": 85, "grade": "优秀"}},
        {{"dialogue_id": 2, "score": 75, "grade": "良好"}}
    ],
    "common_strengths": ["共同优点1", "共同优点2"],
    "common_weaknesses": ["共同不足1", "共同不足2"],
    "training_recommendations": ["培训建议1", "培训建议2"]
}}
"""
            
            batch_result = call_deepseek_api([
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ])
            
            return parse_llm_json(batch_result)
            
        except Exception as e:
            return {"error": f"批量评估失败: {e}"}


# 测试代码
if __name__ == "__main__":
    # 测试金融分析器
    analyzer = FinancialAnalyzer()
    
    test_customer = {
        "age": 30,
        "monthly_income": 15000,
        "monthly_expenses": 8000,
        "total_assets": 500000,
        "total_debts": 100000,
        "credit_score": 720,
        "work_years": 5
    }
    
    print("=== 财务健康分析 ===")
    health_result = analyzer.analyze_financial_health(test_customer)
    if "error" not in health_result:
        print(f"健康评分: {health_result.get('health_score', 'N/A')}/100")
        print(f"评级: {health_result.get('grade', 'N/A')}")
        print("建议:", health_result.get('suggestions', [])[:2])
    
    # 测试贷款能力分析
    loan_request = {"amount": 200000, "term_months": 60, "purpose": "消费"}
    print(f"\n=== 贷款能力分析 ===")
    loan_result = analyzer.analyze_loan_capacity(test_customer, loan_request)
    if "error" not in loan_result:
        print(f"推荐额度: {loan_result.get('recommended_amount', 0):,}元")
        print(f"批准概率: {loan_result.get('approval_probability', 0):.1%}")
        print(f"推荐等级: {loan_result.get('recommendation', 'N/A')}")
    
    # 测试对话评估器
    evaluator = DialogEvaluator()
    
    query = "个人消费贷款的利率是多少？"
    response = "个人消费贷款年化利率一般在6%-18%之间，具体根据征信和收入情况确定。建议到银行详细咨询。"
    
    print(f"\n=== 对话评估 ===")
    eval_result = evaluator.evaluate_response(query, response)
    if "error" not in eval_result:
        print(f"综合得分: {eval_result.get('overall_score', 0)}/100")
        print(f"评级: {eval_result.get('grade', 'N/A')}")
        print("改进建议:", eval_result.get('suggestions', [])[:2])