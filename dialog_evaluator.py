# dialog_evaluator.py
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from llm_api import call_doubao_api_prompt, parse_llm_json

class DialogEvaluator:
    """对话评估器"""
    
    def __init__(self):
        # 评估维度和权重
        self.dimensions = {
            "准确性": 0.25, "相关性": 0.20, "完整性": 0.20, 
            "专业性": 0.15, "可理解性": 0.10, "合规性": 0.10
        }
        
        # 场景权重调整
        self.scenario_weights = {
            "产品咨询": {"准确性": 1.2, "专业性": 1.1},
            "政策解读": {"准确性": 1.3, "合规性": 1.2},
            "风险评估": {"准确性": 1.2, "专业性": 1.2},
            "申请指导": {"完整性": 1.1, "可理解性": 1.1},
            "一般咨询": {"相关性": 1.1, "可理解性": 1.1}
        }
    
    def evaluate_single_response(self, query: str, response: str, context: Dict = None) -> Dict[str, Any]:
        """评估单个回复"""
        try:
            # 确定场景
            scenario = self._determine_scenario(query, context)
            
            # 使用大模型进行综合评估
            evaluation_prompt = f"""
            请对以下对话进行专业评估：
            
            用户问题：{query}
            AI回复：{response}
            评估场景：{scenario}
            
            请从以下6个维度评分（0-100分）：
            1. 准确性：回答内容的准确性和事实正确性
            2. 相关性：回答与用户问题的相关程度
            3. 完整性：回答内容的完整性和全面性
            4. 专业性：回答的专业程度和权威性
            5. 可理解性：回答的易懂程度和表达清晰度
            6. 合规性：回答的合规性和风险控制
            
            请以JSON格式返回评估结果：
            {{
                "dimension_scores": {{
                    "准确性": 分数,
                    "相关性": 分数,
                    "完整性": 分数,
                    "专业性": 分数,
                    "可理解性": 分数,
                    "合规性": 分数
                }},
                "detailed_feedback": {{
                    "优点": ["优点1", "优点2"],
                    "不足": ["不足1", "不足2"],
                    "建议": ["建议1", "建议2"]
                }},
                "overall_assessment": "整体评价描述"
            }}
            """
            
            api_response = call_doubao_api_prompt(evaluation_prompt)
            eval_data = parse_llm_json(api_response)
            
            # 解析评估结果
            dimension_scores = eval_data.get("dimension_scores", {})
            
            # 如果API返回不完整，使用备用评分
            if not dimension_scores:
                dimension_scores = self._fallback_scoring(query, response)
            
            # 计算加权总分
            weighted_score = self._calculate_weighted_score(dimension_scores, scenario)
            
            # 确定评级
            grade = self._determine_grade(weighted_score)
            
            # 生成改进建议
            suggestions = eval_data.get("detailed_feedback", {}).get("建议", 
                                      ["建议优化回答质量"])
            
            return {
                "query": query,
                "response": response,
                "scenario": scenario,
                "dimension_scores": dimension_scores,
                "weighted_score": round(weighted_score, 2),
                "grade": grade,
                "detailed_feedback": eval_data.get("detailed_feedback", {}),
                "improvement_suggestions": suggestions[:3],
                "overall_assessment": eval_data.get("overall_assessment", ""),
                "evaluation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            print(f"评估失败: {e}")
            return self._generate_fallback_evaluation(query, response)
    
    def evaluate_conversation(self, conversation: List[Dict[str, Any]]) -> Dict[str, Any]:
        """评估完整对话"""
        try:
            # 准备对话文本
            conversation_text = "\n".join([
                f"{turn['role']}: {turn['content']}" 
                for turn in conversation
            ])
            
            conversation_prompt = f"""
            请评估以下完整对话的质量：
            
            {conversation_text}
            
            请从以下角度分析：
            1. 对话的整体质量和连贯性
            2. AI回复的一致性和逻辑性
            3. 用户问题的解决程度
            4. 对话的专业性和合规性
            
            请以JSON格式返回：
            {{
                "overall_score": 总体得分(0-100),
                "conversation_metrics": {{
                    "coherence": 连贯性得分,
                    "consistency": 一致性得分,
                    "problem_solving": 问题解决度得分,
                    "professionalism": 专业性得分
                }},
                "trend_analysis": "对话质量趋势分析",
                "conversation_summary": "对话质量总结",
                "recommendations": ["改进建议1", "改进建议2"]
            }}
            """
            
            api_response = call_doubao_api_prompt(conversation_prompt)
            result = parse_llm_json(api_response)
            
            if not result:
                result = {"overall_score": 75, "conversation_summary": "评估失败"}
            
            result["overall_grade"] = self._determine_grade(result.get("overall_score", 75))
            result["conversation_length"] = len(conversation)
            
            return result
            
        except Exception as e:
            print(f"对话评估失败: {e}")
            return {"error": f"对话评估失败: {e}"}
    
    def compare_responses(self, query: str, responses: List[str], 
                         response_labels: List[str] = None) -> Dict[str, Any]:
        """对比多个回复"""
        try:
            # 准备对比文本
            comparison_text = f"用户问题：{query}\n\n"
            for i, response in enumerate(responses):
                label = response_labels[i] if response_labels and i < len(response_labels) else f"回复{i+1}"
                comparison_text += f"{label}：{response}\n\n"
            
            comparison_prompt = f"""
            请对以下多个回复进行对比分析：
            
            {comparison_text}
            
            请从准确性、相关性、完整性、专业性、可理解性、合规性等维度进行对比，
            并给出排名和推荐。
            
            请以JSON格式返回：
            {{
                "evaluations": [
                    {{
                        "label": "回复标签",
                        "score": 得分,
                        "strengths": ["优点1", "优点2"],
                        "weaknesses": ["不足1", "不足2"]
                    }}
                ],
                "ranking": ["最佳回复标签", "第二名标签", "第三名标签"],
                "best_response": {{
                    "label": "最佳回复标签",
                    "reason": "推荐理由"
                }},
                "comparison_summary": "对比分析总结"
            }}
            """
            
            api_response = call_doubao_api_prompt(comparison_prompt)
            result = parse_llm_json(api_response)
            
            return {
                "query": query,
                "response_count": len(responses),
                "comparison_result": result,
                "evaluation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            print(f"对比失败: {e}")
            return {"error": f"对比失败: {e}"}
    
    def batch_evaluate(self, evaluation_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量评估"""
        try:
            batch_results = []
            total_score = 0
            
            for i, data in enumerate(evaluation_data):
                query = data.get("query", "")
                response = data.get("response", "")
                context = data.get("context", {})
                
                evaluation = self.evaluate_single_response(query, response, context)
                evaluation["batch_id"] = i + 1
                batch_results.append(evaluation)
                total_score += evaluation["weighted_score"]
            
            # 计算统计信息
            avg_score = total_score / len(batch_results) if batch_results else 0
            
            # 生成批量报告
            batch_prompt = f"""
            请为以下批量评估结果生成总结报告：
            
            总评估数量：{len(batch_results)}
            平均得分：{avg_score:.1f}
            
            评估结果详情：
            {json.dumps([{
                'query': r['query'][:50] + '...' if len(r['query']) > 50 else r['query'],
                'score': r['weighted_score'],
                'grade': r['grade']
            } for r in batch_results], ensure_ascii=False, indent=2)}
            
            请生成包含统计分析、趋势分析和改进建议的报告。
            """
            
            report_response = call_doubao_api_prompt(batch_prompt)
            
            return {
                "total_evaluations": len(batch_results),
                "average_score": round(avg_score, 2),
                "batch_results": batch_results,
                "batch_report": report_response,
                "evaluation_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            print(f"批量评估失败: {e}")
            return {"error": f"批量评估失败: {e}"}
    
    def _determine_scenario(self, query: str, context: Dict = None) -> str:
        """确定评估场景"""
        if context and "intent" in context:
            intent_mapping = {
                "产品咨询": "产品咨询", "申请条件": "申请指导",
                "利率查询": "产品咨询", "额度评估": "风险评估",
                "政策咨询": "政策解读", "合规咨询": "政策解读"
            }
            return intent_mapping.get(context["intent"], "一般咨询")
        
        # 基于关键词判断
        query_lower = query.lower()
        if any(kw in query_lower for kw in ["政策", "法规", "监管", "合规"]):
            return "政策解读"
        elif any(kw in query_lower for kw in ["风险", "评估", "评分", "征信"]):
            return "风险评估"
        elif any(kw in query_lower for kw in ["申请", "条件", "流程", "材料"]):
            return "申请指导"
        elif any(kw in query_lower for kw in ["产品", "利率", "额度", "期限"]):
            return "产品咨询"
        else:
            return "一般咨询"
    
    def _calculate_weighted_score(self, dimension_scores: Dict[str, float], scenario: str) -> float:
        """计算加权总分"""
        total_score = 0
        total_weight = 0
        
        scenario_weights = self.scenario_weights.get(scenario, {})
        
        for dimension, score in dimension_scores.items():
            if dimension in self.dimensions:
                base_weight = self.dimensions[dimension]
                adjusted_weight = base_weight * scenario_weights.get(dimension, 1.0)
                total_score += score * adjusted_weight
                total_weight += adjusted_weight
        
        return total_score / total_weight if total_weight > 0 else 75.0
    
    def _determine_grade(self, score: float) -> str:
        """确定评级"""
        if score >= 90:
            return "优秀"
        elif score >= 80:
            return "良好"
        elif score >= 70:
            return "一般"
        elif score >= 60:
            return "需改进"
        else:
            return "较差"
    
    def _fallback_scoring(self, query: str, response: str) -> Dict[str, float]:
        """备用评分机制"""
        base_score = 75.0
        scores = {}
        
        for dimension in self.dimensions.keys():
            score = base_score
            
            # 简单的规则评分
            if dimension == "相关性":
                query_words = set(re.findall(r'\w+', query.lower()))
                response_words = set(re.findall(r'\w+', response.lower()))
                overlap = len(query_words & response_words)
                score = min(95, 60 + overlap * 5)
            
            elif dimension == "完整性":
                score = min(95, 50 + len(response) / 10)
            
            elif dimension == "可理解性":
                avg_sentence_len = len(response) / max(1, response.count('。'))
                if 10 <= avg_sentence_len <= 30:
                    score += 10
                elif avg_sentence_len > 50:
                    score -= 10
            
            scores[dimension] = max(0, min(100, score))
        
        return scores
    
    def _generate_fallback_evaluation(self, query: str, response: str) -> Dict[str, Any]:
        """生成备用评估结果"""
        dimension_scores = self._fallback_scoring(query, response)
        weighted_score = sum(dimension_scores.values()) / len(dimension_scores)
        
        return {
            "query": query,
            "response": response,
            "scenario": "一般咨询",
            "dimension_scores": dimension_scores,
            "weighted_score": round(weighted_score, 2),
            "grade": self._determine_grade(weighted_score),
            "detailed_feedback": {"评估": "使用备用评估机制"},
            "improvement_suggestions": ["建议重新评估", "优化回答质量"],
            "overall_assessment": "评估过程出现异常，使用备用评分",
            "evaluation_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

# 使用示例
if __name__ == "__main__":
    evaluator = DialogEvaluator()
    
    # 测试单个回复评估
    query = "个人消费贷款的利率是多少？"
    response = "个人消费贷款的年化利率通常在4.35%-18%之间，具体利率会根据您的征信状况、收入水平、负债情况等因素综合确定。"
    
    print("=== 单个回复评估 ===")
    evaluation = evaluator.evaluate_single_response(query, response)
    print(f"综合得分: {evaluation['weighted_score']}/100")
    print(f"评估等级: {evaluation['grade']}")
    print(f"场景: {evaluation['scenario']}")
    
    # 测试对比评估
    print("\n=== 对比评估 ===")
    responses = [
        response,
        "利率大概在5%-15%左右。",
        "个人消费贷款利率4.35%-18%年化，需要提供收入证明等材料申请。"
    ]
    
    comparison = evaluator.compare_responses(query, responses, ["详细回复", "简短回复", "标准回复"])
    if "comparison_result" in comparison:
        ranking = comparison["comparison_result"].get("ranking", [])
        print(f"排名: {ranking}")