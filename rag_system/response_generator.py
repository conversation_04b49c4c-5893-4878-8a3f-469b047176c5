# rag_system/response_generator.py
import json
from typing import Dict, List, Any, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, call_deepseek_api_prompt, parse_llm_json

class ResponseGenerator:
    """基于大模型的回复生成器"""
    
    def __init__(self):
        # 系统提示词
        self.system_prompt = """你是专业的金融贷款咨询助手，具备以下特点：
1. 基于提供的信息准确回答，不编造信息
2. 根据不同的查询意图采用相应的回复策略
3. 语言专业但通俗易懂
4. 突出关键信息（利率、额度、条件等）
5. 提供实用的建议和指导
6. 避免绝对性承诺，注意合规表达
7. 如信息不足，建议咨询专业人士

请确保回答专业、准确、有用。"""
        
        # 意图特定的回复策略
        self.intent_strategies = {
            '产品咨询': self._generate_product_response,
            '产品对比': self._generate_comparison_response,
            '资质评估': self._generate_assessment_response,
            '风险评估': self._generate_risk_response,
            '合规咨询': self._generate_compliance_response,
            '申请流程': self._generate_process_response,
            '服务投诉': self._generate_conflict_response
        }
        
        # 合规检查关键词
        self.compliance_keywords = [
            '保证', '承诺', '确保', '一定', '肯定',
            '无风险', '零风险', '稳赚', '必赚'
        ]
    
    def generate_response(self, query_info: Dict[str, Any], 
                         retrieved_chunks: List[Dict[str, Any]],
                         conversation_context: Optional[List[Dict[str, str]]] = None,
                         strategy_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """根据意图使用不同策略生成回复"""
        try:
            intent = query_info.get('intent', '一般咨询')
            query = query_info.get('original_query', '')
            
            # 构建上下文信息
            context_info = self._build_context_info(retrieved_chunks)
            history_text = self._build_conversation_history(conversation_context)
            
            # 根据意图选择策略
            strategy_func = self.intent_strategies.get(intent, self._generate_general_response)
            
            # 生成回复
            response = strategy_func(query, intent, context_info, history_text, strategy_context)
            
            # 合规检查和优化
            response = self._compliance_check(response)
            
            # 生成追问建议
            follow_up_questions = self._generate_follow_up_questions(intent, query_info)
            
            return {
                'response': response,
                'intent': intent,
                'confidence': self._calculate_confidence(response, retrieved_chunks),
                'source_chunks': len(retrieved_chunks),
                'follow_up_questions': follow_up_questions,
                'compliance_checked': True,
                'strategy_used': strategy_func.__name__
            }
            
        except Exception as e:
            print(f"回复生成失败: {e}")
            return self._generate_fallback_response(query_info)
    
    def generate_with_llm(self, query_info: Dict[str, Any], 
                         retrieved_chunks: List[Dict[str, Any]],
                         conversation_context: Optional[List[Dict[str, str]]] = None) -> str:
        """直接使用大模型生成回复"""
        try:
            # 构建完整的提示词
            prompt = self._build_comprehensive_prompt(query_info, retrieved_chunks, conversation_context)
            
            # 调用大模型
            response = call_deepseek_api_prompt(prompt, max_tokens=2000)
            
            # 后处理
            response = self._post_process_llm_response(response)
            
            return response
            
        except Exception as e:
            print(f"LLM生成回复失败: {e}")
            return "抱歉，我暂时无法为您提供准确的信息。建议您咨询专业的金融顾问。"
    
    def _generate_product_response(self, query: str, intent: str, context_info: str, 
                                  history_text: str, strategy_context: Dict[str, Any]) -> str:
        """生成产品相关回复"""
        prompt = f"""
{self.system_prompt}

用户问题：{query}
查询意图：{intent}

相关产品信息：
{context_info}

对话历史：
{history_text}

请针对用户的{intent}需求，基于提供的产品信息生成专业的回复。回复应该：
1. 直接回答用户关心的问题
2. 突出关键信息（利率、额度、条件等）
3. 提供实用的申请建议
4. 包含必要的风险提示

请生成回复：
"""
        
        return call_deepseek_api_prompt(prompt, max_tokens=1500)
    
    def _generate_comparison_response(self, query: str, intent: str, context_info: str, 
                                     history_text: str, strategy_context: Dict[str, Any]) -> str:
        """生成产品对比回复"""
        prompt = f"""
{self.system_prompt}

用户问题：{query}
查询意图：{intent}

产品对比信息：
{context_info}

对话历史：
{history_text}

请生成专业的产品对比分析，包括：
1. 清晰的对比维度
2. 各产品的优势和劣势
3. 针对不同客户群体的推荐
4. 选择建议和注意事项

请生成对比分析：
"""
        
        return call_deepseek_api_prompt(prompt, max_tokens=2000)
    
    def _generate_assessment_response(self, query: str, intent: str, context_info: str, 
                                     history_text: str, strategy_context: Dict[str, Any]) -> str:
        """生成资质评估回复"""
        assessment_info = strategy_context.get('assessment_info', {}) if strategy_context else {}
        
        prompt = f"""
{self.system_prompt}

用户问题：{query}
查询意图：{intent}

评估信息：
{json.dumps(assessment_info, ensure_ascii=False, indent=2)}

相关案例和信息：
{context_info}

对话历史：
{history_text}

请基于评估信息进行资质分析，包括：
1. 客户基本情况分析
2. 申请成功概率评估
3. 风险因素识别
4. 改进建议
5. 合适的产品推荐

请生成评估分析：
"""
        
        return call_deepseek_api_prompt(prompt, max_tokens=1500)
    
    def _generate_risk_response(self, query: str, intent: str, context_info: str, 
                               history_text: str, strategy_context: Dict[str, Any]) -> str:
        """生成风险评估专用回复"""
        risk_info = strategy_context.get('risk_assessment', {}) if strategy_context else {}
        
        prompt = f"""
{self.system_prompt}

作为风险评估专家，请基于以下信息为用户进行风险分析：

用户问题：{query}
查询意图：{intent}

风险评估信息：
{json.dumps(risk_info, ensure_ascii=False, indent=2)}

相关信息：
{context_info}

对话历史：
{history_text}

请生成专业的风险评估回复，包括：
1. 风险等级明确说明
2. 主要风险因素分析
3. 风险缓解建议
4. 必要的风险提示

回复应该客观、专业、有建设性。
"""
        
        return call_deepseek_api_prompt(prompt, max_tokens=1500)
    
    def _generate_compliance_response(self, query: str, intent: str, context_info: str, 
                                     history_text: str, strategy_context: Dict[str, Any]) -> str:
        """生成合规咨询回复"""
        prompt = f"""
{self.system_prompt}

作为合规专家，请基于以下信息为用户提供合规咨询：

用户问题：{query}
查询意图：{intent}

相关政策信息：
{context_info}

对话历史：
{history_text}

请生成专业的合规咨询回复，包括：
1. 相关法规政策解读
2. 合规要求说明
3. 操作建议
4. 风险提示

回复应该准确、专业、有指导性。
"""
        
        return call_deepseek_api_prompt(prompt, max_tokens=1500)
    
    def _generate_process_response(self, query: str, intent: str, context_info: str, 
                                  history_text: str, strategy_context: Dict[str, Any]) -> str:
        """生成流程相关回复"""
        prompt = f"""
{self.system_prompt}

用户问题：{query}
查询意图：{intent}

相关流程信息：
{context_info}

对话历史：
{history_text}

请详细说明申请流程，包括：
1. 准备阶段的要求
2. 具体申请步骤
3. 所需材料清单
4. 时间安排
5. 注意事项和建议

请生成详细的流程指南：
"""
        
        return call_deepseek_api_prompt(prompt, max_tokens=1500)
    
    def _generate_conflict_response(self, query: str, intent: str, context_info: str, 
                                   history_text: str, strategy_context: Dict[str, Any]) -> str:
        """生成服务冲突专用回复"""
        conflict_info = strategy_context.get('conflict_info', {}) if strategy_context else {}
        
        prompt = f"""
{self.system_prompt}

作为客服专家，请为以下服务冲突生成合适的回应：

用户问题：{query}
冲突信息：{json.dumps(conflict_info, ensure_ascii=False, indent=2)}

对话历史：
{history_text}

回复原则：
1. 首先真诚道歉，承认问题
2. 针对具体问题给出解决方案
3. 展现专业和耐心
4. 提供后续跟进方式
5. 语气温和、理解用户感受

请生成温和、专业、解决问题导向的回复。
"""
        
        return call_deepseek_api_prompt(prompt, max_tokens=1000)
    
    def _generate_general_response(self, query: str, intent: str, context_info: str, 
                                  history_text: str, strategy_context: Dict[str, Any]) -> str:
        """生成通用回复"""
        prompt = f"""
{self.system_prompt}

用户问题：{query}
查询意图：{intent}

相关信息：
{context_info}

对话历史：
{history_text}

请基于提供的信息回答用户问题。如果信息不足，请诚实说明，并建议用户咨询专业人士。

请生成回复：
"""
        
        return call_deepseek_api_prompt(prompt, max_tokens=1500)
    
    def _build_comprehensive_prompt(self, query_info: Dict[str, Any], 
                                  retrieved_chunks: List[Dict[str, Any]],
                                  conversation_context: Optional[List[Dict[str, str]]]) -> str:
        """构建综合提示词"""
        query = query_info.get('original_query', '')
        intent = query_info.get('intent', '')
        entities = query_info.get('entities', {})
        
        context_info = self._build_context_info(retrieved_chunks)
        history_text = self._build_conversation_history(conversation_context)
        
        prompt = f"""
{self.system_prompt}

用户问题：{query}
识别意图：{intent}
提取实体：{json.dumps(entities, ensure_ascii=False)}

相关信息：
{context_info}

对话历史：
{history_text}

请基于以上信息为用户提供专业、准确、有用的回答。确保：
1. 回答针对性强，直接解决用户问题
2. 信息准确，基于提供的资料
3. 语言专业但易懂
4. 包含实用的建议
5. 遵守金融合规要求

请生成回复：
"""
        return prompt
    
    def _build_context_info(self, chunks: List[Dict[str, Any]]) -> str:
        """构建上下文信息"""
        if not chunks:
            return "暂无相关信息"
        
        context_parts = []
        for i, chunk in enumerate(chunks[:5], 1):  # 最多使用前5个chunk
            content = chunk.get('content', '')
            content_type = chunk.get('content_type', '')
            
            context_parts.append(f"信息{i} ({content_type})：{content}")
        
        return "\n\n".join(context_parts)
    
    def _build_conversation_history(self, conversation_context: Optional[List[Dict[str, str]]]) -> str:
        """构建对话历史"""
        if not conversation_context:
            return "无对话历史"
        
        history_lines = []
        for turn in conversation_context[-3:]:  # 最近3轮对话
            role = "用户" if turn['role'] == 'user' else "助手"
            content = turn['content'][:200]  # 限制长度
            history_lines.append(f"{role}: {content}")
        
        return "\n".join(history_lines)
    
    def _calculate_confidence(self, response: str, chunks: List[Dict[str, Any]]) -> float:
        """计算回复置信度"""
        try:
            confidence = 0.5  # 基础置信度
            
            # 基于信息源数量调整
            if len(chunks) >= 3:
                confidence += 0.2
            elif len(chunks) >= 1:
                confidence += 0.1
            
            # 基于回复长度调整
            if len(response) > 200:
                confidence += 0.1
            
            # 基于关键信息完整性调整
            key_indicators = ['利率', '额度', '条件', '流程', '建议']
            present_indicators = sum(1 for indicator in key_indicators if indicator in response)
            confidence += (present_indicators / len(key_indicators)) * 0.2
            
            return min(1.0, confidence)
            
        except Exception:
            return 0.5
    
    def _generate_follow_up_questions(self, intent: str, query_info: Dict[str, Any]) -> List[str]:
        """生成追问建议"""
        try:
            entities = query_info.get('entities', {})
            
            prompt = f"""
基于用户的查询意图和已提取的信息，生成3个相关的追问建议：

用户意图：{intent}
已提取信息：{json.dumps(entities, ensure_ascii=False)}

请返回JSON格式的追问建议：
{{
    "follow_up_questions": [
        "您想了解具体的申请条件吗？",
        "需要我为您推荐合适的产品吗？",
        "您还有其他问题需要咨询吗？"
    ]
}}
"""
            
            response = call_doubao_api_prompt(prompt)
            result = parse_llm_json(response)
            
            return result.get('follow_up_questions', []) if result else self._default_follow_up_questions(intent)
            
        except Exception as e:
            print(f"生成追问建议失败: {e}")
            return self._default_follow_up_questions(intent)
    
    def _default_follow_up_questions(self, intent: str) -> List[str]:
        """默认追问建议"""
        default_questions = {
            '产品咨询': [
                "您想了解申请条件吗？",
                "需要我为您介绍申请流程吗？",
                "您想对比其他产品吗？"
            ],
            '利率查询': [
                "您想了解如何获得更优惠的利率吗？",
                "需要我推荐低利率产品吗？",
                "您想了解利率的计算方式吗？"
            ],
            '申请条件': [
                "需要我评估您的申请成功率吗？",
                "您想了解如何提高通过率吗？",
                "需要我推荐适合的产品吗？"
            ],
            '风险评估': [
                "需要我为您分析具体的风险因素吗？",
                "您想了解如何降低风险吗？",
                "需要我推荐风险较低的产品吗？"
            ],
            '服务投诉': [
                "还有什么我可以为您改进的吗？",
                "需要我为您转接专人服务吗？",
                "您希望我们如何跟进处理？"
            ]
        }
        
        return default_questions.get(intent, [
            "还有其他问题吗？",
            "需要我详细解释吗？",
            "您想了解相关产品吗？"
        ])
    
    def _compliance_check(self, response: str) -> str:
        """合规检查"""
        try:
            # 检查禁用词汇
            modified_response = response
            for keyword in self.compliance_keywords:
                if keyword in modified_response:
                    modified_response = modified_response.replace(keyword, f"通常{keyword}")
            
            # 确保包含风险提示
            if not any(warning in modified_response for warning in ['建议', '咨询', '仅供参考', '风险提示']):
                modified_response += "\n\n风险提示：以上信息仅供参考，具体以金融机构实际政策为准。借贷有风险，申请需谨慎。"
            
            return modified_response
            
        except Exception as e:
            print(f"合规检查失败: {e}")
            return response
    
    def _post_process_llm_response(self, response: str) -> str:
        """后处理LLM回复"""
        try:
            # 移除可能的模型输出标记
            response = response.replace("回答：", "").replace("助手：", "").strip()
            
            # 确保适当的格式
            if not response.endswith(('。', '！', '？', '.')):
                response += "。"
            
            return response
            
        except Exception as e:
            print(f"LLM回复后处理失败: {e}")
            return response
    
    def _generate_fallback_response(self, query_info: Dict[str, Any]) -> Dict[str, Any]:
        """生成备用回复"""
        return {
            'response': "抱歉，我暂时无法为您提供准确的信息。建议您咨询专业的金融顾问或直接联系相关金融机构。",
            'intent': query_info.get('intent', '一般咨询'),
            'confidence': 0.0,
            'source_chunks': 0,
            'follow_up_questions': ["需要我为您转接人工客服吗？", "您还有其他问题吗？"],
            'compliance_checked': True,
            'strategy_used': 'fallback'
        }

if __name__ == "__main__":
    # 测试响应生成器
    generator = ResponseGenerator()
    
    # 模拟查询信息
    test_query_info = {
        'original_query': '个人消费贷款利率是多少',
        'intent': '利率查询',
        'entities': {'产品类型': ['消费贷款']},
        'slots': {'intent': '利率查询', 'constraints': {'product_type': '消费贷款'}}
    }
    
    # 模拟检索结果
    test_chunks = [
        {
            'chunk_id': 'CHUNK001',
            'content': '个人消费贷款年化利率4.35%-18%，额度1-50万元，期限6个月-5年。申请条件：征信良好，月收入5000元以上。',
            'content_type': 'product_basic_info',
            'metadata': {
                'name': '个人消费贷款',
                'interest_rate': {'min': 4.35, 'max': 18.0},
                'features': {'amount_range': '1-50万元'}
            }
        }
    ]
    
    print("=== 测试策略化回复生成 ===")
    response_result = generator.generate_response(test_query_info, test_chunks)
    print(f"回复: {response_result['response']}")
    print(f"置信度: {response_result['confidence']:.3f}")
    print(f"使用策略: {response_result['strategy_used']}")
    print(f"追问建议: {response_result['follow_up_questions']}")
    
    print("\n=== 测试风险评估回复 ===")
    risk_query = {
        'original_query': '我想评估贷款风险',
        'intent': '风险评估',
        'entities': {}
    }
    
    risk_context = {
        'risk_assessment': {
            'risk_level': '中',
            'risk_score': 75,
            'risk_factors': ['收入不稳定'],
            'mitigation_suggestions': ['提供收入证明', '增加担保']
        }
    }
    
    risk_response = generator.generate_response(risk_query, test_chunks, None, risk_context)
    print(f"风险评估回复: {risk_response['response']}")