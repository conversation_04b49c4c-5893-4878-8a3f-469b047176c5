# rag_system/compliance_strategy.py
import re
import json
from typing import Dict, List, Any, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, parse_llm_json, call_doubao_api

class ComplianceStrategy:
    """基于大模型的合规处理策略"""
    
    def __init__(self):
        # 基础配置
        self.prohibited_words = [
            "保证", "承诺", "确保", "一定能", "100%", "无风险", "零风险", 
            "稳赚", "必赚", "绝对", "包通过", "秒批", "免审核"
        ]
        
        self.risk_keywords = [
            "投资", "理财", "收益", "回报", "利润", "贷款", "借款", "信贷"
        ]
        
        # 大模型提示模板
        self.compliance_check_prompt = """
你是一个金融合规专家，请检查以下内容的合规性：

内容：{content}

请从以下维度评估：
1. 是否包含禁用词汇（如"保证"、"100%"、"无风险"等绝对化表述）
2. 是否需要风险提示
3. 是否存在夸大宣传
4. 是否缺少必要信息披露
5. 是否符合客户保护要求

请以JSON格式返回结果：
{{
    "is_compliant": true/false,
    "compliance_score": 0-100,
    "risk_level": "低/中/高",
    "violations": ["违规问题列表"],
    "warnings": ["警告列表"],
    "suggestions": ["改进建议"],
    "analysis": "详细分析"
}}
"""
        
        self.compliance_enhance_prompt = """
请将以下金融内容修改为合规版本：

原内容：{content}

修改要求：
1. 移除或替换绝对化表述（保证→通常，100%→大概率等）
2. 添加必要的风险提示
3. 使用相对化、谦逊的表达方式
4. 添加免责声明
5. 确保信息披露完整

请返回修改后的内容，保持原意但符合金融合规要求。
"""

    def check_compliance(self, content: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """使用大模型检查内容合规性"""
        try:
            # 先进行基础规则检查
            rule_check = self._basic_rule_check(content)
            
            # 构建大模型检查提示
            prompt = self.compliance_check_prompt.format(content=content)
            
            # 调用大模型API - 修复：使用call_doubao_api而不是call_doubao_api_prompt
            messages = [{"role": "user", "content": prompt}]
            llm_response = call_doubao_api(messages, max_tokens=1000)
            llm_result = parse_llm_json(llm_response)
            
            # 合并规则检查和大模型检查结果
            final_result = self._merge_compliance_results(rule_check, llm_result)
            
            # 添加修改后的内容
            if not final_result.get("is_compliant", True):
                final_result["modified_content"] = self._quick_fix_content(content)
            else:
                final_result["modified_content"] = content
                
            return final_result
            
        except Exception as e:
            print(f"合规检查失败: {e}")
            return self._generate_fallback_result(content)

    def enhance_compliance(self, content: str, context: Dict[str, Any] = None) -> str:
        """使用大模型增强内容合规性"""
        try:
            # 构建增强提示
            prompt = self.compliance_enhance_prompt.format(content=content)
            
            # 调用大模型API - 修复：使用call_doubao_api而不是call_doubao_api_prompt
            messages = [{"role": "user", "content": prompt}]
            enhanced_content = call_doubao_api(messages, max_tokens=1500)
            
            # 清理响应内容
            enhanced_content = enhanced_content.strip()
            
            # 确保包含基本风险提示
            if not self._has_risk_warning(enhanced_content):
                enhanced_content = self._add_basic_risk_warning(enhanced_content, content)
                
            return enhanced_content
            
        except Exception as e:
            print(f"合规增强失败: {e}")
            return self._fallback_enhance(content)

    def validate_financial_advice(self, advice: str, advice_type: str = "general") -> Dict[str, Any]:
        """验证金融建议的合规性"""
        try:
            validation_prompt = f"""
请验证以下{advice_type}类型的金融建议是否合规：

建议内容：{advice}

验证要点：
- 是否避免了绝对化承诺
- 是否包含适当的风险提示
- 是否存在误导性表述
- 是否符合监管要求

请以JSON格式返回：
{{
    "is_valid": true/false,
    "risk_assessment": "低/中/高",
    "compliance_issues": ["问题列表"],
    "recommendations": ["建议列表"]
}}
"""
            
            # 修复：使用call_doubao_api
            messages = [{"role": "user", "content": validation_prompt}]
            response = call_doubao_api(messages, max_tokens=800)
            result = parse_llm_json(response)
            
            # 添加增强后的建议
            if not result.get("is_valid", True):
                result["enhanced_advice"] = self.enhance_compliance(advice)
            else:
                result["enhanced_advice"] = advice
                
            return result
            
        except Exception as e:
            print(f"建议验证失败: {e}")
            return {
                "is_valid": False,
                "risk_assessment": "中",
                "compliance_issues": ["验证系统异常"],
                "enhanced_advice": advice
            }

    def generate_compliance_report(self, content: str, context: Dict[str, Any] = None) -> str:
        """生成合规检查报告"""
        try:
            compliance_result = self.check_compliance(content, context)
            
            report_prompt = f"""
基于以下合规检查结果，生成一份专业的合规检查报告：

检查结果：{json.dumps(compliance_result, ensure_ascii=False, indent=2)}

请生成包含以下部分的报告：
1. 执行摘要
2. 详细分析
3. 风险评估
4. 改进建议
5. 合规要求说明

使用专业、客观的语调，格式清晰易读。
"""
            
            # 修复：使用call_doubao_api
            messages = [{"role": "user", "content": report_prompt}]
            report = call_doubao_api(messages, max_tokens=2000)
            return report.strip()
            
        except Exception as e:
            print(f"报告生成失败: {e}")
            return self._generate_simple_report(content)

    def _basic_rule_check(self, content: str) -> Dict[str, Any]:
        """基础规则检查"""
        violations = []
        warnings = []
        score = 100
        
        # 检查禁用词汇
        for word in self.prohibited_words:
            if word in content:
                violations.append(f"包含禁用词汇：'{word}'")
                score -= 15
        
        # 检查风险关键词但缺少风险提示
        has_risk_keywords = any(keyword in content for keyword in self.risk_keywords)
        has_risk_warning = self._has_risk_warning(content)
        
        if has_risk_keywords and not has_risk_warning:
            warnings.append("涉及风险相关内容但缺少风险提示")
            score -= 10
        
        return {
            "violations": violations,
            "warnings": warnings,
            "score": max(score, 0),
            "risk_level": "高" if violations else ("中" if warnings else "低")
        }

    def _merge_compliance_results(self, rule_result: Dict, llm_result: Dict) -> Dict[str, Any]:
        """合并规则检查和大模型检查结果"""
        merged = {
            "is_compliant": rule_result.get("score", 100) >= 80 and llm_result.get("is_compliant", True),
            "compliance_score": min(rule_result.get("score", 100), llm_result.get("compliance_score", 100)),
            "risk_level": "高" if (rule_result.get("risk_level") == "高" or llm_result.get("risk_level") == "高") 
                         else "中" if (rule_result.get("risk_level") == "中" or llm_result.get("risk_level") == "中") 
                         else "低",
            "violations": rule_result.get("violations", []) + llm_result.get("violations", []),
            "warnings": rule_result.get("warnings", []) + llm_result.get("warnings", []),
            "suggestions": llm_result.get("suggestions", []),
            "analysis": llm_result.get("analysis", "")
        }
        
        return merged

    def _quick_fix_content(self, content: str) -> str:
        """快速修复内容"""
        fixed = content
        
        # 替换常见问题词汇
        replacements = {
            "保证": "通常", "确保": "一般情况下", "一定": "可能",
            "100%": "大概率", "无风险": "风险相对较低", "绝对": "通常"
        }
        
        for original, replacement in replacements.items():
            fixed = fixed.replace(original, replacement)
        
        return fixed

    def _has_risk_warning(self, content: str) -> bool:
        """检查是否包含风险提示"""
        warning_indicators = ["风险", "谨慎", "仅供参考", "实际政策为准", "投资有风险"]
        return any(indicator in content for indicator in warning_indicators)

    def _add_basic_risk_warning(self, content: str, original_content: str) -> str:
        """添加基本风险提示"""
        if any(keyword in original_content for keyword in ["投资", "理财"]):
            return content + "\n\n风险提示：投资有风险，理财需谨慎。"
        elif any(keyword in original_content for keyword in ["贷款", "借款"]):
            return content + "\n\n风险提示：借贷有风险，申请需谨慎。"
        else:
            return content + "\n\n提示：以上信息仅供参考，具体以相关机构政策为准。"

    def _fallback_enhance(self, content: str) -> str:
        """备用增强方法"""
        enhanced = self._quick_fix_content(content)
        return self._add_basic_risk_warning(enhanced, content)

    def _generate_fallback_result(self, content: str) -> Dict[str, Any]:
        """生成备用检查结果"""
        return {
            "is_compliant": False,
            "compliance_score": 60,
            "risk_level": "中",
            "violations": ["系统异常，建议人工审核"],
            "warnings": ["合规检查系统暂时不可用"],
            "suggestions": ["请联系合规部门确认"],
            "modified_content": self._fallback_enhance(content),
            "analysis": "系统异常，采用备用检查方法"
        }

    def _generate_simple_report(self, content: str) -> str:
        """生成简单报告"""
        result = self.check_compliance(content)
        
        report = f"""## 合规检查报告

### 总体评估
- **合规状态**: {'合规' if result['is_compliant'] else '不合规'}
- **风险等级**: {result['risk_level']}
- **合规评分**: {result['compliance_score']}/100

### 主要问题
{chr(10).join(f"- {v}" for v in result.get('violations', []))}

### 改进建议
{chr(10).join(f"- {s}" for s in result.get('suggestions', []))}

### 修改建议
{result.get('modified_content', content)}

ini

复制
"""
        return report


if __name__ == "__main__":
    # 测试示例
    strategy = ComplianceStrategy()
    
    test_cases = [
        "我们保证您一定能通过贷款申请，100%无风险！",
        "个人消费贷款年化利率4.35%-18%，请根据自身情况申请。",
        "投资理财产品，预期收益率8-12%，存在市场风险。"
    ]
    
    for i, content in enumerate(test_cases, 1):
        print(f"\n=== 测试 {i} ===")
        print(f"原内容: {content}")
        
        # 合规检查
        result = strategy.check_compliance(content)
        print(f"合规状态: {'合规' if result['is_compliant'] else '不合规'}")
        print(f"评分: {result['compliance_score']}")
        
        # 合规增强
        enhanced = strategy.enhance_compliance(content)
        print(f"增强后: {enhanced}")