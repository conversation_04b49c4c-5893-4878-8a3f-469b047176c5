# rag_system/risk_strategy.py
import json
from typing import Dict, List, Any, Optional, Tuple
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, call_deepseek_api_prompt, parse_llm_json

class RiskStrategy:
    """基于大模型的风险评估策略"""
    
    def __init__(self):
        # 风险等级定义
        self.risk_levels = {
            "很低": {"threshold": 80, "description": "风险极低，优质客户"},
            "低": {"threshold": 65, "description": "风险较低，可信客户"},
            "中": {"threshold": 45, "description": "风险中等，需评估"},
            "高": {"threshold": 25, "description": "风险较高，谨慎处理"},
            "很高": {"threshold": 0, "description": "风险很高，建议拒绝"}
        }
        
        # 系统提示词
        self.system_prompt = """你是专业的金融风险评估专家，具备以下能力：
1. 客户信用风险评估
2. 贷款违约概率预测
3. 风险因子识别分析
4. 风险缓释建议制定

请基于客户信息进行专业、客观的风险评估。"""
    
    def assess_customer_risk(self, customer_profile: Dict[str, Any], 
                           application_info: Dict[str, Any]) -> Dict[str, Any]:
        """使用大模型评估客户风险"""
        try:
            prompt = f"""
请对以下客户进行全面的信用风险评估：

客户信息：
{json.dumps(customer_profile, ensure_ascii=False, indent=2)}

申请信息：
{json.dumps(application_info, ensure_ascii=False, indent=2)}

请返回JSON格式的风险评估结果：
{{
    "risk_score": 75.5,
    "risk_level": "低/中/高",
    "default_probability": 0.05,
    "risk_factors": ["风险因子1", "风险因子2"],
    "protective_factors": ["保护因子1", "保护因子2"],
    "mitigation_suggestions": ["建议1", "建议2"],
    "assessment_details": "详细风险分析",
    "score_breakdown": {{
        "credit_score_impact": 25,
        "income_stability": 20,
        "debt_ratio": 15,
        "employment_stability": 10,
        "other_factors": 5
    }},
    "confidence_level": "高/中/低",
    "recommended_actions": ["行动建议1", "行动建议2"]
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=2000)
            result = parse_llm_json(response)
            
            # 如果解析失败，使用备用方法
            if not result:
                return self._fallback_risk_assessment(customer_profile, application_info)
            
            return result
            
        except Exception as e:
            print(f"风险评估失败: {e}")
            return self._fallback_risk_assessment(customer_profile, application_info)
    
    def compare_risk_profiles(self, profiles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """使用大模型比较多个客户的风险状况"""
        try:
            prompt = f"""
请对以下多个客户的风险状况进行对比分析：

客户信息：
{json.dumps(profiles, ensure_ascii=False, indent=2)}

请返回JSON格式的对比分析结果：
{{
    "risk_rankings": [
        {{"profile_id": "Profile_1", "rank": 1, "risk_score": 85, "risk_level": "低"}},
        {{"profile_id": "Profile_2", "rank": 2, "risk_score": 70, "risk_level": "中"}}
    ],
    "risk_distribution": {{"低": 2, "中": 3, "高": 1}},
    "comparative_analysis": "详细对比分析",
    "portfolio_recommendation": "组合建议",
    "key_differentiators": ["主要区别因素1", "主要区别因素2"]
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=2000)
            result = parse_llm_json(response)
            
            if not result:
                return self._fallback_comparison(profiles)
            
            return result
            
        except Exception as e:
            print(f"风险对比失败: {e}")
            return self._fallback_comparison(profiles)
    
    def generate_risk_report(self, customer_profile: Dict[str, Any], 
                           application_info: Dict[str, Any]) -> str:
        """使用大模型生成风险评估报告"""
        try:
            # 先进行风险评估
            assessment = self.assess_customer_risk(customer_profile, application_info)
            
            prompt = f"""
请基于以下风险评估结果生成专业的风险评估报告：

客户信息：
{json.dumps(customer_profile, ensure_ascii=False, indent=2)}

申请信息：
{json.dumps(application_info, ensure_ascii=False, indent=2)}

风险评估结果：
{json.dumps(assessment, ensure_ascii=False, indent=2)}

请生成完整的风险评估报告，包括：
1. 执行摘要
2. 客户基本情况
3. 风险评估结果
4. 详细风险分析
5. 风险缓释建议
6. 结论和建议

报告应该专业、详细且结构清晰。
"""
            
            report = call_deepseek_api_prompt(prompt, max_tokens=3000)
            return report
            
        except Exception as e:
            print(f"生成风险报告失败: {e}")
            return self._fallback_risk_report(customer_profile, application_info)
    
    def predict_default_probability(self, customer_profile: Dict[str, Any], 
                                  loan_amount: float, loan_term: int) -> Dict[str, Any]:
        """使用大模型预测违约概率"""
        try:
            prompt = f"""
请基于客户信息预测贷款违约概率：

客户信息：
{json.dumps(customer_profile, ensure_ascii=False, indent=2)}

贷款信息：
- 贷款金额：{loan_amount:,}元
- 贷款期限：{loan_term}个月

请返回JSON格式的预测结果：
{{
    "default_probability": 0.05,
    "probability_range": {{"min": 0.03, "max": 0.07}},
    "confidence_level": 0.85,
    "key_risk_indicators": ["指标1", "指标2"],
    "scenario_analysis": {{
        "best_case": 0.02,
        "base_case": 0.05,
        "worst_case": 0.12
    }},
    "time_horizon_analysis": {{
        "6_months": 0.01,
        "12_months": 0.03,
        "24_months": 0.05
    }},
    "prediction_reasoning": "预测依据和逻辑"
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=1500)
            result = parse_llm_json(response)
            
            if not result:
                return self._fallback_default_prediction(customer_profile, loan_amount, loan_term)
            
            return result
            
        except Exception as e:
            print(f"违约概率预测失败: {e}")
            return self._fallback_default_prediction(customer_profile, loan_amount, loan_term)
    
    def analyze_portfolio_risk(self, customer_portfolio: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析客户组合风险"""
        try:
            prompt = f"""
请对以下客户组合进行风险分析：

客户组合：
{json.dumps(customer_portfolio, ensure_ascii=False, indent=2)}

请返回JSON格式的组合风险分析：
{{
    "portfolio_risk_score": 65.5,
    "risk_distribution": {{"低风险": 30, "中风险": 50, "高风险": 20}},
    "correlation_analysis": "客户间风险相关性分析",
    "concentration_risk": "集中度风险分析",
    "diversification_score": 0.75,
    "portfolio_recommendations": ["建议1", "建议2"],
    "risk_limits": {{
        "high_risk_limit": 25,
        "current_high_risk": 20,
        "recommendation": "可以接受"
    }},
    "stress_test_results": {{
        "mild_stress": {{"loss_rate": 0.02}},
        "severe_stress": {{"loss_rate": 0.08}}
    }}
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=2000)
            result = parse_llm_json(response)
            
            if not result:
                return self._fallback_portfolio_analysis(customer_portfolio)
            
            return result
            
        except Exception as e:
            print(f"组合风险分析失败: {e}")
            return self._fallback_portfolio_analysis(customer_portfolio)
    
    def generate_risk_monitoring_alerts(self, customer_profile: Dict[str, Any], 
                                      monitoring_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成风险监控预警"""
        try:
            prompt = f"""
请基于客户信息和监控数据生成风险预警：

客户信息：
{json.dumps(customer_profile, ensure_ascii=False, indent=2)}

监控数据：
{json.dumps(monitoring_data, ensure_ascii=False, indent=2)}

请返回JSON格式的预警列表：
{{
    "alerts": [
        {{
            "alert_id": "ALERT_001",
            "alert_level": "高/中/低",
            "alert_type": "征信恶化/收入下降/逾期风险",
            "description": "预警描述",
            "trigger_indicators": ["触发指标1", "触发指标2"],
            "recommended_actions": ["建议行动1", "建议行动2"],
            "urgency": "紧急/一般/关注",
            "impact_assessment": "影响评估"
        }}
    ]
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=1500)
            result = parse_llm_json(response)
            
            return result.get("alerts", []) if result else []
            
        except Exception as e:
            print(f"风险监控预警生成失败: {e}")
            return []
    
    def _fallback_risk_assessment(self, customer_profile: Dict[str, Any], 
                                 application_info: Dict[str, Any]) -> Dict[str, Any]:
        """备用风险评估方法"""
        # 简化的风险评估逻辑
        credit_score = customer_profile.get('credit_score', 650)
        monthly_income = customer_profile.get('monthly_income', 5000)
        
        if credit_score >= 750 and monthly_income >= 15000:
            risk_level = "低"
            risk_score = 80
        elif credit_score >= 650 and monthly_income >= 8000:
            risk_level = "中"
            risk_score = 60
        else:
            risk_level = "高"
            risk_score = 40
        
        return {
            "risk_score": risk_score,
            "risk_level": risk_level,
            "default_probability": 0.05 if risk_level == "低" else 0.10 if risk_level == "中" else 0.20,
            "risk_factors": ["数据不足，无法详细分析"],
            "mitigation_suggestions": ["建议补充完整信息后重新评估"],
            "assessment_details": "基于基础信息的简化评估",
            "confidence_level": "低"
        }
    
    def _fallback_comparison(self, profiles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """备用风险对比方法"""
        return {
            "risk_rankings": [{"profile_id": f"Profile_{i+1}", "rank": i+1, "risk_score": 50, "risk_level": "中"} 
                            for i in range(len(profiles))],
            "comparative_analysis": "数据不足，无法进行详细对比",
            "portfolio_recommendation": "建议补充完整信息后重新分析"
        }
    
    def _fallback_risk_report(self, customer_profile: Dict[str, Any], 
                             application_info: Dict[str, Any]) -> str:
        """备用风险报告生成"""
        return f"""
## 风险评估报告

### 客户基本信息
- 征信评分：{customer_profile.get('credit_score', 'N/A')}
- 月收入：{customer_profile.get('monthly_income', 'N/A')}元

### 风险评估结果
由于信息不完整，无法进行详细的风险评估。建议补充完整的客户信息后重新评估。

### 建议
1. 收集完整的客户财务信息
2. 获取详细的征信报告
3. 评估客户的还款能力和意愿
"""
    
    def _fallback_default_prediction(self, customer_profile: Dict[str, Any], 
                                   loan_amount: float, loan_term: int) -> Dict[str, Any]:
        """备用违约概率预测"""
        return {
            "default_probability": 0.10,
            "confidence_level": 0.50,
            "prediction_reasoning": "基于简化模型的预测，准确性有限"
        }
    
    def _fallback_portfolio_analysis(self, customer_portfolio: List[Dict[str, Any]]) -> Dict[str, Any]:
        """备用组合分析"""
        return {
            "portfolio_risk_score": 60.0,
            "risk_distribution": {"中风险": 100},
            "portfolio_recommendations": ["建议详细分析各客户风险状况"]
        }

if __name__ == "__main__":
    # 测试调整后的风险策略
    strategy = RiskStrategy()
    
    # 测试客户数据
    test_customer = {
        "age": 32,
        "monthly_income": 15000,
        "credit_score": 720,
        "debt_to_income": 0.3,
        "employment_years": 5,
        "education": "本科",
        "city_tier": "一线城市"
    }
    
    test_application = {
        "amount": 200000,
        "purpose": "装修",
        "term_months": 36
    }
    
    print("=== 大模型风险评估 ===")
    assessment = strategy.assess_customer_risk(test_customer, test_application)
    print(f"风险评分: {assessment.get('risk_score', 'N/A')}")
    print(f"风险等级: {assessment.get('risk_level', 'N/A')}")
    print(f"违约概率: {assessment.get('default_probability', 'N/A')}")
    
    print("\n=== 违约概率预测 ===")
    default_pred = strategy.predict_default_probability(test_customer, 200000, 36)
    print(f"预测违约概率: {default_pred.get('default_probability', 'N/A')}")
    print(f"置信水平: {default_pred.get('confidence_level', 'N/A')}")
    
    print("\n=== 风险报告生成 ===")
    report = strategy.generate_risk_report(test_customer, test_application)
    print(f"报告长度: {len(report)} 字符")
    print(f"报告摘要: {report[:200]}...")