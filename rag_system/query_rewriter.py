# rag_system/query_rewriter.py
import re
import json
from typing import Dict, List, Any, Optional, Tuple
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, call_deepseek_api_prompt, parse_llm_json

class QueryRewriter:
    """基于大模型的查询重写器和意图识别器"""
    
    def __init__(self):
        # 意图分类系统提示词
        self.intent_system_prompt = """你是专业的金融领域意图识别专家，能够准确识别用户在金融贷款咨询中的真实意图。

支持的意图类别：
- 产品咨询：了解具体产品信息、特性、优势等
- 申请条件：询问申请要求和门槛条件  
- 利率查询：关心利率水平和计算方式
- 额度评估：想知道能申请多少额度
- 申请流程：了解申请步骤和办理流程
- 资质评估：评估自己是否符合条件，成功概率
- 产品对比：比较不同产品优劣势
- 风险评估：了解投资风险和注意事项
- 合规咨询：咨询政策法规、合规要求相关
- 还款方式：了解还款方式和选择
- 服务投诉：对服务不满、要求改进或投诉
- 一般咨询：其他金融相关咨询

特别注意：
- 如果用户表达不满、投诉、服务态度问题，应识别为"服务投诉"
- 如果询问具体的个人情况评估，应识别为"资质评估"
- 如果要求分析风险状况，应识别为"风险评估"
- 如果对比多个产品或机构，应识别为"产品对比"

请准确识别用户的主要意图。"""
        
        # 实体提取系统提示词
        self.entity_system_prompt = """你是专业的金融实体提取专家，能够从用户查询中准确提取关键的金融相关信息。

需要提取的实体类型：
- 金额：贷款金额、收入金额等数值
- 利率：利率百分比
- 期限：贷款期限、时间范围
- 机构：银行或金融机构名称
- 产品类型：具体的贷款产品类型
- 征信分数：信用评分数值
- 收入：月收入或年收入
- 年龄：客户年龄
- 城市：所在城市或地区

请准确提取并分类这些实体。"""
    
    def process_query(self, query: str, conversation_context: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """使用大模型处理查询：重写 + 意图识别 + 实体提取"""
        try:
            print(f"处理查询: {query}")
            
            # 1. 查询预处理
            cleaned_query = self._clean_query(query)
            
            # 2. 上下文相关的查询扩写
            expanded_query = self._expand_with_context(cleaned_query, conversation_context)
            
            # 3. 并行处理：意图识别和实体提取
            intent_result = self._identify_intent_with_llm(expanded_query)
            entities_result = self._extract_entities_with_llm(expanded_query)
            
            # 4. 查询改写
            rewritten_queries = self._rewrite_query_with_llm(
                expanded_query, 
                intent_result.get('intent', '一般咨询'), 
                entities_result
            )
            
            # 5. 槽位填充
            slots = self._fill_slots_with_llm(expanded_query, intent_result, entities_result)
            
            result = {
                'original_query': query,
                'cleaned_query': cleaned_query,
                'expanded_query': expanded_query,
                'rewritten_queries': rewritten_queries,
                'intent': intent_result.get('intent', '一般咨询'),
                'intent_confidence': intent_result.get('confidence', 0.5),
                'entities': entities_result,
                'slots': slots,
                'query_type': self._classify_query_type(expanded_query, intent_result.get('intent', '一般咨询'))
            }
            
            return result
            
        except Exception as e:
            print(f"查询处理失败: {e}")
            return self._generate_fallback_result(query)
    
    def rewrite_for_retrieval(self, query_result: Dict[str, Any]) -> List[str]:
        """为检索优化的查询重写"""
        try:
            original_query = query_result['expanded_query']
            intent = query_result['intent']
            entities = query_result['entities']
            
            prompt = f"""
请为以下查询生成多个适合信息检索的重写版本：

原始查询：{original_query}
用户意图：{intent}
提取实体：{json.dumps(entities, ensure_ascii=False)}

要求：
1. 生成3-5个不同角度的重写版本
2. 保持原始语义
3. 增加相关的金融术语
4. 提高检索精确度
5. 考虑同义词和相关词汇

请返回JSON格式：
{{
    "retrieval_queries": [
        "重写版本1",
        "重写版本2", 
        "重写版本3"
    ]
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=800)
            result = parse_llm_json(response)
            
            queries = result.get('retrieval_queries', [original_query]) if result else [original_query]
            
            # 确保原查询在列表中
            if original_query not in queries:
                queries.insert(0, original_query)
            
            return queries[:5]  # 最多返回5个查询
            
        except Exception as e:
            print(f"检索查询重写失败: {e}")
            return [query_result.get('expanded_query', query_result.get('original_query', ''))]
    
    def _identify_intent_with_llm(self, query: str) -> Dict[str, Any]:
        """使用大模型识别意图"""
        try:
            prompt = f"""
{self.intent_system_prompt}

用户查询：{query}

请分析用户的真实意图，返回JSON格式：
{{
    "intent": "最匹配的意图类别",
    "confidence": 0.85,
    "reasoning": "分类理由和依据",
    "secondary_intents": ["可能的次要意图"]
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=500)
            result = parse_llm_json(response)
            
            if result:
                return result
            else:
                return self._fallback_intent_identification(query)
                
        except Exception as e:
            print(f"意图识别失败: {e}")
            return self._fallback_intent_identification(query)
    
    def _extract_entities_with_llm(self, query: str) -> Dict[str, List[str]]:
        """使用大模型提取实体"""
        try:
            prompt = f"""
{self.entity_system_prompt}

用户查询：{query}

请提取查询中的金融相关实体，返回JSON格式：
{{
    "金额": ["提取的金额数值"],
    "利率": ["提取的利率"],
    "期限": ["提取的期限"],
    "机构": ["银行或机构名称"],
    "产品类型": ["产品类型"],
    "征信分数": ["信用评分"],
    "收入": ["收入金额"],
    "年龄": ["年龄"],
    "城市": ["城市名称"]
}}

注意：只返回确实存在的实体，不存在的实体类型不要包含在结果中。
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=600)
            result = parse_llm_json(response)
            
            if result and isinstance(result, dict):
                # 过滤空值
                filtered_result = {k: v for k, v in result.items() if v and isinstance(v, list) and len(v) > 0}
                return filtered_result
            else:
                return self._fallback_entity_extraction(query)
                
        except Exception as e:
            print(f"实体提取失败: {e}")
            return self._fallback_entity_extraction(query)
    
    def _rewrite_query_with_llm(self, query: str, intent: str, entities: Dict[str, List[str]]) -> List[str]:
        """使用大模型重写查询"""
        try:
            prompt = f"""
请为以下查询生成多个重写版本以提高信息检索效果：

原始查询：{query}
用户意图：{intent}
提取实体：{json.dumps(entities, ensure_ascii=False)}

重写要求：
1. 保持原始语义
2. 增加相关的专业术语
3. 考虑不同的表达方式
4. 提高检索匹配度
5. 生成2-4个版本

请返回JSON格式：
{{
    "rewritten_queries": [
        "重写版本1",
        "重写版本2",
        "重写版本3"
    ]
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=600)
            result = parse_llm_json(response)
            
            if result and 'rewritten_queries' in result:
                queries = result['rewritten_queries']
                # 确保原查询在列表中
                if query not in queries:
                    queries.insert(0, query)
                return queries
            else:
                return [query]
                
        except Exception as e:
            print(f"查询重写失败: {e}")
            return [query]
    
    def _fill_slots_with_llm(self, query: str, intent_result: Dict[str, Any], entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """使用大模型填充槽位"""
        try:
            intent = intent_result.get('intent', '一般咨询')
            
            prompt = f"""
请基于用户查询、意图和实体信息填充结构化槽位：

查询：{query}
意图：{intent}
实体：{json.dumps(entities, ensure_ascii=False)}

请返回JSON格式的槽位信息：
{{
    "intent": "{intent}",
    "entities": {json.dumps(entities, ensure_ascii=False)},
    "constraints": {{
        "product_type": "产品类型约束",
        "institution": "机构约束",
        "amount_range": "金额范围",
        "rate_preference": "利率偏好"
    }},
    "preferences": {{
        "priority": "用户优先考虑的因素",
        "urgency": "紧急程度",
        "risk_tolerance": "风险承受度"
    }},
    "context": {{
        "customer_segment": "客户类型",
        "consultation_stage": "咨询阶段"
    }}
}}

注意：只填充能从查询中确定的信息，不确定的字段设为null。
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=800)
            result = parse_llm_json(response)
            
            if result:
                return result
            else:
                return self._fallback_slot_filling(intent, entities)
                
        except Exception as e:
            print(f"槽位填充失败: {e}")
            return self._fallback_slot_filling(intent_result.get('intent', '一般咨询'), entities)
    
    def _clean_query(self, query: str) -> str:
        """查询预处理"""
        try:
            # 去除多余空格
            cleaned = re.sub(r'\s+', ' ', query.strip())
            
            # 标准化标点符号
            cleaned = cleaned.replace('？', '?').replace('！', '!')
            
            # 去除无意义的词语
            noise_words = ['请问', '想问一下', '能告诉我', '我想知道', '麻烦', '谢谢']
            for noise in noise_words:
                cleaned = cleaned.replace(noise, '')
            
            return cleaned.strip()
            
        except Exception as e:
            print(f"查询清理失败: {e}")
            return query
    
    def _expand_with_context(self, query: str, context: Optional[List[Dict[str, str]]]) -> str:
        """基于上下文扩展查询"""
        try:
            if not context or len(context) == 0:
                return query
            
            # 使用大模型进行上下文理解和查询扩展
            context_text = "\n".join([
                f"{turn['role']}: {turn['content']}" 
                for turn in context[-3:]  # 最近3轮对话
            ])
            
            prompt = f"""
基于对话上下文，判断当前查询是否需要补充信息：

对话历史：
{context_text}

当前查询：{query}

如果当前查询过于简短或存在指代不明，请结合上下文补充完整。如果查询已经完整，直接返回原查询。

请返回JSON格式：
{{
    "expanded_query": "扩展后的查询",
    "expansion_reason": "扩展理由"
}}
"""
            
            response = call_doubao_api_prompt(prompt)
            result = parse_llm_json(response)
            
            if result and 'expanded_query' in result:
                return result['expanded_query']
            else:
                return query
                
        except Exception as e:
            print(f"上下文扩展失败: {e}")
            return query
    
    def _classify_query_type(self, query: str, intent: str) -> str:
        """分类查询类型"""
        try:
            # 简单查询
            if len(query.split()) <= 3:
                return 'simple'
            
            # 复杂查询
            if any(word in query for word in ['同时', '并且', '还有', '以及', '另外']):
                return 'complex'
            
            # 对比查询
            if intent == '产品对比' or any(word in query for word in ['对比', '比较', '哪个好', '区别']):
                return 'comparison'
            
            # 条件查询
            if any(word in query for word in ['如果', '假如', '在什么情况下', '当']):
                return 'conditional'
            
            return 'standard'
            
        except Exception as e:
            print(f"查询类型分类失败: {e}")
            return 'standard'
    
    def _fallback_intent_identification(self, query: str) -> Dict[str, Any]:
        """备用意图识别 - 增强关键词匹配"""
        query_lower = query.lower()
        
        # 服务冲突相关
        if any(word in query_lower for word in ['投诉', '不满', '态度', '服务差', '不专业', '要求退款', '举报']):
            return {'intent': '服务投诉', 'confidence': 0.8, 'reasoning': '冲突关键词匹配'}
        
        # 风险评估相关
        elif any(word in query_lower for word in ['风险', '评估风险', '安全吗', '有什么风险', '风险大吗']):
            return {'intent': '风险评估', 'confidence': 0.7, 'reasoning': '风险关键词匹配'}
        
        # 资质评估相关
        elif any(word in query_lower for word in ['我能', '我的情况', '适合我', '能申请', '资质', '我可以']):
            return {'intent': '资质评估', 'confidence': 0.7, 'reasoning': '资质关键词匹配'}
        
        # 产品对比相关
        elif any(word in query_lower for word in ['对比', '比较', '哪个好', '区别', '哪家', '选择哪个']):
            return {'intent': '产品对比', 'confidence': 0.7, 'reasoning': '对比关键词匹配'}
        
        # 合规咨询相关
        elif any(word in query_lower for word in ['法规', '政策', '合规', '规定', '监管', '合法']):
            return {'intent': '合规咨询', 'confidence': 0.7, 'reasoning': '合规关键词匹配'}
        
        # 原有的分类逻辑
        elif any(word in query_lower for word in ['产品', '种类', '有什么', '推荐']):
            return {'intent': '产品咨询', 'confidence': 0.6, 'reasoning': '产品关键词匹配'}
        elif any(word in query_lower for word in ['条件', '要求', '门槛', '需要什么']):
            return {'intent': '申请条件', 'confidence': 0.6, 'reasoning': '条件关键词匹配'}
        elif any(word in query_lower for word in ['利率', '费率', '利息', '年化']):
            return {'intent': '利率查询', 'confidence': 0.6, 'reasoning': '利率关键词匹配'}
        elif any(word in query_lower for word in ['额度', '多少钱', '能贷', '最多']):
            return {'intent': '额度评估', 'confidence': 0.6, 'reasoning': '额度关键词匹配'}
        elif any(word in query_lower for word in ['流程', '怎么申请', '如何办理', '步骤']):
            return {'intent': '申请流程', 'confidence': 0.6, 'reasoning': '流程关键词匹配'}
        else:
            return {'intent': '一般咨询', 'confidence': 0.5, 'reasoning': '默认分类'}
    
    def _fallback_entity_extraction(self, query: str) -> Dict[str, List[str]]:
        """备用实体提取"""
        entities = {}
        
        # 简单的正则匹配
        patterns = {
            '金额': r'(\d+\.?\d*)(万|千|元)',
            '利率': r'(\d+\.?\d*)%',
            '期限': r'(\d+)(年|个月|月)',
            '机构': r'(工商银行|建设银行|农业银行|中国银行|招商银行|平安银行|交通银行)',
            '产品类型': r'(消费|抵押|经营|信用|房|车)贷款?'
        }
        
        for entity_type, pattern in patterns.items():
            matches = re.findall(pattern, query)
            if matches:
                if entity_type in ['金额', '利率']:
                    entities[entity_type] = [match[0] if isinstance(match, tuple) else match for match in matches]
                else:
                    entities[entity_type] = [match if isinstance(match, str) else ''.join(match) for match in matches]
        
        return entities
    
    def _fallback_slot_filling(self, intent: str, entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """备用槽位填充"""
        return {
            'intent': intent,
            'entities': entities,
            'constraints': {},
            'preferences': {},
            'context': {'consultation_stage': 'initial'}
        }
    
    def _generate_fallback_result(self, query: str) -> Dict[str, Any]:
        """生成备用处理结果"""
        return {
            'original_query': query,
            'cleaned_query': query,
            'expanded_query': query,
            'rewritten_queries': [query],
            'intent': '一般咨询',
            'intent_confidence': 0.5,
            'entities': {},
            'slots': {'intent': '一般咨询', 'entities': {}},
            'query_type': 'simple'
        }

if __name__ == "__main__":
    # 测试查询重写器
    rewriter = QueryRewriter()
    
    # 测试查询列表
    test_queries = [
        "个人消费贷款利率是多少",
        "我月收入8000能申请多少额度", 
        "征信不好可以贷款吗",
        "工商银行和建设银行的房贷哪个好",
        "申请车贷需要什么条件",
        "贷款有什么风险",
        "你们的服务态度太差了，我要投诉"
    ]
    
    for query in test_queries:
        print(f"\n=== 测试查询: {query} ===")
        
        # 处理查询
        result = rewriter.process_query(query)
        
        print(f"意图: {result['intent']} (置信度: {result['intent_confidence']:.3f})")
        print(f"实体: {result['entities']}")
        print(f"查询类型: {result['query_type']}")
        
        # 生成检索查询
        retrieval_queries = rewriter.rewrite_for_retrieval(result)
        print(f"检索查询: {retrieval_queries}")