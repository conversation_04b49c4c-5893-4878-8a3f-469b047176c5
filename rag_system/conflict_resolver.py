# conflict_resolver.py
import json
from typing import Dict, List, Any, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, call_deepseek_api_prompt, parse_llm_json

class ConflictResolver:
    """贷款咨询服务冲突处理器"""
    
    def __init__(self):
        # 冲突类型定义
        self.conflict_types = {
            "服务态度": {
                "keywords": ["态度", "不耐烦", "敷衍", "粗鲁", "不专业", "语气"],
                "severity": "高"
            },
            "平台不满": {
                "keywords": ["平台", "系统", "体验差", "难用", "复杂", "不方便"],
                "severity": "中"
            },
            "需求未满足": {
                "keywords": ["没解决", "不明白", "不清楚", "还是不懂", "没回答"],
                "severity": "高"
            },
            "信息质疑": {
                "keywords": ["不对", "错误", "不准确", "假的", "骗人", "不信"],
                "severity": "中"
            },
            "流程问题": {
                "keywords": ["太复杂", "手续多", "时间长", "麻烦", "流程"],
                "severity": "中"
            },
            "售后投诉": {
                "keywords": ["投诉", "举报", "不满意", "要退款", "售后"],
                "severity": "高"
            },
            "重复咨询": {
                "keywords": ["问了很多次", "重复", "一直问", "反复"],
                "severity": "低"
            }
        }
        
        # 情绪等级
        self.emotion_levels = {
            "愤怒": 0.9,
            "非常不满": 0.8,
            "不满意": 0.6,
            "疑惑": 0.4,
            "中性": 0.2,
            "满意": 0.1
        }
    
    def detect_service_conflicts(self, dialog_history: List[Dict[str, Any]], 
                                current_query: str) -> Dict[str, Any]:
        """检测服务冲突"""
        try:
            # 准备对话历史
            dialog_content = []
            for i, turn in enumerate(dialog_history[-10:]):  # 只看最近10轮对话
                role = turn.get("role", "user")
                content = turn.get("content", "")
                timestamp = turn.get("timestamp", "")
                dialog_content.append(f"【{role}】{timestamp}: {content}")
            
            detection_prompt = f"""
作为客服质量分析专家，请分析用户与贷款咨询助手的对话中是否存在服务冲突：

对话历史：
{chr(10).join(dialog_content)}

当前用户消息：{current_query}

请重点识别以下类型的冲突：
1. 服务态度问题：助手态度不好、不耐烦、敷衍等
2. 平台体验不满：对系统、平台使用体验的抱怨
3. 需求未得到满足：问题没被解决、回答不到位
4. 信息准确性质疑：用户质疑提供信息的准确性
5. 流程复杂度抱怨：认为办理流程太复杂麻烦
6. 售后服务投诉：对服务结果不满，要求投诉退款等
7. 重复咨询问题：用户反复询问同样问题

分析要求：
- 识别用户的情绪状态和不满程度
- 判断冲突的根本原因
- 评估冲突的严重程度
- 提供处理建议

返回JSON格式：
{{
    "has_conflict": true/false,
    "conflict_types": ["冲突类型1", "冲突类型2"],
    "emotion_level": "愤怒/非常不满/不满意/疑惑/中性",
    "severity": "高/中/低",
    "root_cause": "冲突根本原因分析",
    "user_complaints": ["具体抱怨内容"],
    "previous_issues": "历史问题总结",
    "urgency": "紧急/一般/低",
    "confidence": 0.0-1.0
}}
"""
            
            response = call_doubao_api_prompt(detection_prompt)
            result = parse_llm_json(response)
            
            if not result:
                return {"has_conflict": False, "error": "检测失败"}
            
            # 添加对话引用
            result["dialog_turns"] = len(dialog_history)
            result["current_query"] = current_query
            
            return result
            
        except Exception as e:
            print(f"服务冲突检测失败: {e}")
            return {"has_conflict": False, "error": str(e)}
    
    def generate_conflict_response(self, conflict_info: Dict[str, Any], 
                                  user_query: str, 
                                  dialog_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成冲突处理响应"""
        try:
            if not conflict_info.get("has_conflict"):
                return {
                    "response_type": "normal",
                    "message": "感谢您的咨询，我会尽力为您提供准确的信息。",
                    "actions": []
                }
            
            # 准备冲突信息
            conflict_types = conflict_info.get("conflict_types", [])
            emotion_level = conflict_info.get("emotion_level", "中性")
            severity = conflict_info.get("severity", "中")
            root_cause = conflict_info.get("root_cause", "")
            complaints = conflict_info.get("user_complaints", [])
            
            response_prompt = f"""
作为专业的客服响应专家，请为以下服务冲突生成合适的回应：

用户当前消息：{user_query}

冲突分析：
- 冲突类型：{', '.join(conflict_types)}
- 情绪状态：{emotion_level}
- 严重程度：{severity}
- 根本原因：{root_cause}
- 具体抱怨：{', '.join(complaints)}

响应原则：
1. 首先真诚道歉，承认问题
2. 针对具体问题给出解决方案
3. 展现专业和耐心
4. 提供后续跟进方式
5. 语气温和、理解用户感受

请返回JSON格式：
{{
    "response_type": "conflict_resolution",
    "primary_message": "主要回应内容",
    "apology": "道歉内容",
    "solution": "具体解决方案", 
    "compensation": "补偿措施（如果需要）",
    "follow_up": "后续跟进安排",
    "escalation": "是否需要升级处理",
    "tone": "温和/正式/亲切",
    "priority": "高/中/低"
}}
"""
            
            response = call_doubao_api_prompt(response_prompt)
            result = parse_llm_json(response)
            
            if not result:
                return {
                    "response_type": "conflict_resolution",
                    "primary_message": "非常抱歉给您带来不便，我会立即为您重新核实相关信息，请您稍等。"
                }
            
            return result
            
        except Exception as e:
            print(f"生成冲突响应失败: {e}")
            return {"response_type": "error", "message": "系统处理异常，请稍后再试"}
    
    def analyze_user_satisfaction(self, dialog_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析用户满意度趋势"""
        try:
            # 提取用户消息
            user_messages = []
            for turn in dialog_history:
                if turn.get("role") == "user":
                    content = turn.get("content", "")
                    timestamp = turn.get("timestamp", "")
                    user_messages.append(f"{timestamp}: {content}")
            
            satisfaction_prompt = f"""
请分析用户在整个咨询过程中的满意度变化：

用户消息历史：
{chr(10).join(user_messages[-15:])}  # 最近15条用户消息

分析维度：
1. 满意度趋势：从开始到现在的满意度变化
2. 关键转折点：满意度发生明显变化的时刻
3. 主要不满原因：导致满意度下降的具体因素
4. 积极反馈：用户表达满意的地方
5. 改进建议：如何提升用户满意度

返回JSON格式：
{{
    "overall_satisfaction": 0.0-1.0,
    "satisfaction_trend": "上升/下降/稳定/波动",
    "key_turning_points": ["转折点描述"],
    "dissatisfaction_reasons": ["不满原因"],
    "positive_feedback": ["积极反馈"],
    "improvement_suggestions": ["改进建议"],
    "risk_level": "高/中/低"
}}
"""
            
            response = call_doubao_api_prompt(satisfaction_prompt)
            return parse_llm_json(response) or {"overall_satisfaction": 0.5}
            
        except Exception as e:
            print(f"满意度分析失败: {e}")
            return {"overall_satisfaction": 0.5}
    
    def generate_service_recovery_plan(self, conflict_info: Dict[str, Any], 
                                     satisfaction_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成服务恢复计划"""
        try:
            conflict_types = conflict_info.get("conflict_types", [])
            severity = conflict_info.get("severity", "中")
            emotion_level = conflict_info.get("emotion_level", "中性")
            satisfaction = satisfaction_analysis.get("overall_satisfaction", 0.5)
            risk_level = satisfaction_analysis.get("risk_level", "中")
            
            recovery_prompt = f"""
请制定服务恢复计划：

冲突情况：
- 冲突类型：{', '.join(conflict_types)}
- 严重程度：{severity}
- 用户情绪：{emotion_level}

满意度分析：
- 当前满意度：{satisfaction}
- 风险等级：{risk_level}
- 改进建议：{satisfaction_analysis.get('improvement_suggestions', [])}

请制定恢复计划：
{{
    "immediate_actions": ["立即执行的措施"],
    "short_term_plan": ["短期改进计划"],
    "long_term_strategy": ["长期策略"],
    "compensation_plan": "补偿方案",
    "quality_assurance": ["质量保证措施"],
    "monitoring_plan": "后续监控方案",
    "success_metrics": ["成功指标"],
    "timeline": "执行时间表"
}}
"""
            
            response = call_doubao_api_prompt(recovery_prompt)
            return parse_llm_json(response) or {}
            
        except Exception as e:
            print(f"生成恢复计划失败: {e}")
            return {}
    
    def format_user_friendly_response(self, conflict_response: Dict[str, Any]) -> str:
        """格式化用户友好的回复"""
        try:
            response_type = conflict_response.get("response_type", "normal")
            
            if response_type == "normal":
                return conflict_response.get("message", "感谢您的咨询，有什么可以帮助您的吗？")
            
            # 冲突处理回复
            parts = []
            
            # 道歉
            apology = conflict_response.get("apology", "")
            if apology:
                parts.append(apology)
            
            # 主要回应
            primary_message = conflict_response.get("primary_message", "")
            if primary_message:
                parts.append(primary_message)
            
            # 解决方案
            solution = conflict_response.get("solution", "")
            if solution:
                parts.append(f"解决方案：{solution}")
            
            # 补偿措施
            compensation = conflict_response.get("compensation", "")
            if compensation:
                parts.append(f"补偿措施：{compensation}")
            
            # 后续跟进
            follow_up = conflict_response.get("follow_up", "")
            if follow_up:
                parts.append(f"后续安排：{follow_up}")
            
            return "\n\n".join(parts)
            
        except Exception as e:
            print(f"格式化回复失败: {e}")
            return "非常抱歉，我会立即为您核实相关信息，请您稍等。"
    
    def log_conflict_incident(self, conflict_info: Dict[str, Any], 
                             resolution_actions: Dict[str, Any]) -> Dict[str, Any]:
        """记录冲突事件"""
        try:
            log_prompt = f"""
请生成冲突事件记录：

冲突信息：{json.dumps(conflict_info, ensure_ascii=False, indent=2)}
处理措施：{json.dumps(resolution_actions, ensure_ascii=False, indent=2)}

生成记录：
{{
    "incident_id": "事件ID",
    "incident_summary": "事件摘要",
    "severity_level": "严重等级",
    "resolution_status": "处理状态",
    "lessons_learned": ["经验教训"],
    "prevention_measures": ["预防措施"],
    "follow_up_required": true/false
}}
"""
            
            response = call_doubao_api_prompt(log_prompt)
            result = parse_llm_json(response)
            
            # 添加时间戳
            import datetime
            if result:
                result["timestamp"] = datetime.datetime.now().isoformat()
                result["status"] = "logged"
            
            return result or {}
            
        except Exception as e:
            print(f"记录冲突事件失败: {e}")
            return {}

if __name__ == "__main__":
    # 测试服务冲突处理器
    resolver = ConflictResolver()
    
    # 模拟对话历史
    test_dialog = [
        {"role": "user", "content": "我想了解个人消费贷款", "timestamp": "2025-01-20 10:00:00"},
        {"role": "assistant", "content": "好的，个人消费贷款利率是4.35%-18%", "timestamp": "2025-01-20 10:00:10"},
        {"role": "user", "content": "这个利率范围太大了，能具体点吗？", "timestamp": "2025-01-20 10:01:00"},
        {"role": "assistant", "content": "具体利率需要根据您的资质评估", "timestamp": "2025-01-20 10:01:15"},
        {"role": "user", "content": "你们总是这样说，能不能给个准确的？我问了好几次了", "timestamp": "2025-01-20 10:02:00"},
        {"role": "assistant", "content": "抱歉，确实需要评估后才能确定", "timestamp": "2025-01-20 10:02:10"},
        {"role": "user", "content": "你们的服务态度真的很差，一点都不专业！我要投诉！", "timestamp": "2025-01-20 10:03:00"}
    ]
    
    current_query = "你们的服务态度真的很差，一点都不专业！我要投诉！"
    
    print("=== 服务冲突检测 ===")
    conflict_info = resolver.detect_service_conflicts(test_dialog, current_query)
    print(f"发现冲突: {conflict_info.get('has_conflict')}")
    print(f"冲突类型: {conflict_info.get('conflict_types', [])}")
    print(f"情绪状态: {conflict_info.get('emotion_level', '')}")
    print(f"严重程度: {conflict_info.get('severity', '')}")
    print(f"根本原因: {conflict_info.get('root_cause', '')}")
    
    print("\n=== 生成处理响应 ===")
    response_plan = resolver.generate_conflict_response(conflict_info, current_query, test_dialog)
    print(f"响应类型: {response_plan.get('response_type')}")
    print(f"优先级: {response_plan.get('priority', '')}")
    
    print("\n=== 用户友好回复 ===")
    user_response = resolver.format_user_friendly_response(response_plan)
    print(user_response)
    
    print("\n=== 满意度分析 ===")
    satisfaction = resolver.analyze_user_satisfaction(test_dialog)
    print(f"整体满意度: {satisfaction.get('overall_satisfaction', 0):.2f}")
    print(f"满意度趋势: {satisfaction.get('satisfaction_trend', '')}")
    print(f"风险等级: {satisfaction.get('risk_level', '')}")
    
    print("\n=== 服务恢复计划 ===")
    recovery_plan = resolver.generate_service_recovery_plan(conflict_info, satisfaction)
    print(f"立即措施: {recovery_plan.get('immediate_actions', [])}")
    print(f"补偿方案: {recovery_plan.get('compensation_plan', '')}")
    
    print("\n=== 事件记录 ===")
    incident_log = resolver.log_conflict_incident(conflict_info, response_plan)
    print(f"事件ID: {incident_log.get('incident_id', '')}")
    print(f"事件摘要: {incident_log.get('incident_summary', '')}")