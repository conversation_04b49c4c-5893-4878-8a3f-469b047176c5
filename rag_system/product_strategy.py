# rag_system/product_strategy.py
import json
from typing import Dict, List, Any, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, call_deepseek_api_prompt, parse_llm_json

class ProductStrategy:
    """基于大模型的产品推荐策略"""
    
    def __init__(self):
        self.system_prompt = """你是专业的金融产品顾问，具备以下能力：
1. 深度分析客户需求和风险状况
2. 精准匹配最适合的金融产品
3. 提供专业的产品推荐理由
4. 给出实用的申请建议和注意事项

请基于客户信息提供专业、客观、有价值的产品推荐服务。"""

    def analyze_customer(self, customer_profile: Dict[str, Any]) -> Dict[str, Any]:
        """使用大模型分析客户画像"""
        try:
            prompt = f"""
{self.system_prompt}

请深度分析以下客户的基本情况和需求特征：

客户信息：
{json.dumps(customer_profile, ensure_ascii=False, indent=2)}

请返回JSON格式的客户分析结果：
{{
    "customer_segment": "优质客户/标准客户/潜力客户/谨慎客户",
    "risk_profile": {{
        "risk_level": "低/中/高",
        "risk_score": 75,
        "key_risk_factors": ["风险因子1", "风险因子2"],
        "protective_factors": ["保护因子1", "保护因子2"]
    }},
    "financial_capacity": {{
        "income_stability": "稳定/一般/不稳定", 
        "debt_capacity": "强/中等/弱",
        "repayment_ability": "强/中等/弱"
    }},
    "customer_needs": {{
        "primary_needs": ["主要需求1", "主要需求2"],
        "urgency_level": "高/中/低",
        "amount_preference": "大额/中等/小额"
    }},
    "recommendation_strategy": "推荐策略概述",
    "analysis_summary": "客户分析总结"
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=1500)
            result = parse_llm_json(response)
            
            if result:
                return result
            else:
                return self._fallback_customer_analysis(customer_profile)
                
        except Exception as e:
            print(f"客户分析失败: {e}")
            return self._fallback_customer_analysis(customer_profile)

    def recommend_products(self, customer_profile: Dict[str, Any], 
                          application_info: Dict[str, Any],
                          available_products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """使用大模型推荐产品"""
        try:
            # 简化产品信息以适应token限制
            simplified_products = []
            for product in available_products:
                simplified = {
                    'name': product.get('name', ''),
                    'type': product.get('type', ''),
                    'institution': product.get('institution', ''),
                    'interest_rate': product.get('interest_rate', {}),
                    'amount_range': product.get('features', {}).get('amount_range', ''),
                    'main_requirements': product.get('requirements', {})
                }
                simplified_products.append(simplified)
            
            prompt = f"""
{self.system_prompt}

请为客户推荐最适合的金融产品：

客户信息：
{json.dumps(customer_profile, ensure_ascii=False, indent=2)}

申请需求：
{json.dumps(application_info, ensure_ascii=False, indent=2)}

可选产品：
{json.dumps(simplified_products, ensure_ascii=False, indent=2)}

请返回JSON格式的产品推荐结果（最多推荐3个产品）：
{{
    "recommendations": [
        {{
            "product_name": "产品名称",
            "product_type": "产品类型",
            "institution": "金融机构",
            "match_score": 0.85,
            "suitability_level": "强烈推荐/推荐/可考虑",
            "estimated_rate": "预估利率范围",
            "estimated_amount": "预估批准额度",
            "approval_probability": "预估批准概率",
            "recommendation_reasons": [
                "推荐理由1",
                "推荐理由2",
                "推荐理由3"
            ],
            "key_advantages": [
                "优势1",
                "优势2", 
                "优势3"
            ],
            "potential_challenges": [
                "可能的挑战1",
                "可能的挑战2"
            ],
            "application_tips": [
                "申请建议1",
                "申请建议2"
            ]
        }}
    ],
    "overall_strategy": "整体推荐策略",
    "alternative_suggestions": "备选建议"
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=2500)
            result = parse_llm_json(response)
            
            if result and 'recommendations' in result:
                return result['recommendations']
            else:
                return self._fallback_product_recommendation(customer_profile, available_products)
                
        except Exception as e:
            print(f"产品推荐失败: {e}")
            return self._fallback_product_recommendation(customer_profile, available_products)

    def compare_products(self, products: List[Dict[str, Any]], 
                        customer_profile: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """使用大模型进行产品对比分析"""
        try:
            prompt = f"""
{self.system_prompt}

请对以下金融产品进行详细对比分析：

产品信息：
{json.dumps(products, ensure_ascii=False, indent=2)}

客户信息（如提供）：
{json.dumps(customer_profile, ensure_ascii=False, indent=2) if customer_profile else "未提供"}

请返回JSON格式的对比分析：
{{
    "comparison_summary": "对比总结",
    "comparison_dimensions": {{
        "interest_rate": {{
            "best_product": "利率最优产品",
            "analysis": "利率对比分析"
        }},
        "loan_amount": {{
            "best_product": "额度最优产品", 
            "analysis": "额度对比分析"
        }},
        "application_requirements": {{
            "easiest_product": "门槛最低产品",
            "analysis": "申请条件对比"
        }},
        "overall_value": {{
            "best_product": "综合价值最优产品",
            "analysis": "综合价值分析"
        }}
    }},
    "detailed_comparison": [
        {{
            "product_name": "产品名称",
            "strengths": ["优势1", "优势2"],
            "weaknesses": ["劣势1", "劣势2"],
            "best_for": "最适合的客户群体"
        }}
    ],
    "recommendation": {{
        "top_choice": "首选产品",
        "reasoning": "选择理由",
        "conditions": "选择条件"
    }},
    "decision_framework": "决策建议框架"
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=2000)
            result = parse_llm_json(response)
            
            if result:
                return result
            else:
                return self._fallback_product_comparison(products)
                
        except Exception as e:
            print(f"产品对比失败: {e}")
            return self._fallback_product_comparison(products)

    def generate_product_guide(self, customer_profile: Dict[str, Any],
                              application_info: Dict[str, Any]) -> str:
        """使用大模型生成个性化产品选择指南"""
        try:
            prompt = f"""
{self.system_prompt}

请为客户生成详细的个性化金融产品选择指南：

客户信息：
{json.dumps(customer_profile, ensure_ascii=False, indent=2)}

申请需求：
{json.dumps(application_info, ensure_ascii=False, indent=2)}

请生成完整的选择指南，包括：
1. 客户情况分析
2. 资金需求评估
3. 产品类型推荐
4. 选择要点和策略
5. 申请准备建议
6. 注意事项和风险提示

指南应该实用、专业，帮助客户做出明智的金融决策。
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=2500)
            return response
            
        except Exception as e:
            print(f"产品指南生成失败: {e}")
            return self._fallback_product_guide(customer_profile, application_info)

    def assess_product_fit(self, customer_profile: Dict[str, Any],
                          product_info: Dict[str, Any]) -> Dict[str, Any]:
        """评估特定产品与客户的匹配度"""
        try:
            prompt = f"""
{self.system_prompt}

请评估以下产品与客户的匹配度：

客户信息：
{json.dumps(customer_profile, ensure_ascii=False, indent=2)}

产品信息：
{json.dumps(product_info, ensure_ascii=False, indent=2)}

请返回JSON格式的匹配度评估：
{{
    "overall_fit_score": 0.85,
    "fit_level": "高度匹配/较好匹配/一般匹配/不太匹配/不匹配",
    "dimension_scores": {{
        "eligibility_match": 0.9,
        "needs_alignment": 0.8,
        "risk_compatibility": 0.7,
        "cost_effectiveness": 0.8
    }},
    "approval_likelihood": {{
        "probability": 0.75,
        "confidence_level": "高/中/低",
        "key_factors": ["关键因素1", "关键因素2"]
    }},
    "benefits_for_customer": [
        "客户获益1",
        "客户获益2",
        "客户获益3"
    ],
    "potential_concerns": [
        "潜在关注点1",
        "潜在关注点2"
    ],
    "improvement_suggestions": [
        "改进建议1",
        "改进建议2"
    ],
    "final_recommendation": "推荐/谨慎考虑/不推荐",
    "reasoning": "详细评估理由"
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=1500)
            result = parse_llm_json(response)
            
            if result:
                return result
            else:
                return self._fallback_fit_assessment(customer_profile, product_info)
                
        except Exception as e:
            print(f"产品匹配度评估失败: {e}")
            return self._fallback_fit_assessment(customer_profile, product_info)

    def optimize_application_strategy(self, customer_profile: Dict[str, Any],
                                    target_products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """优化申请策略"""
        try:
            prompt = f"""
{self.system_prompt}

请为客户制定最优的申请策略：

客户信息：
{json.dumps(customer_profile, ensure_ascii=False, indent=2)}

目标产品：
{json.dumps(target_products, ensure_ascii=False, indent=2)}

请返回JSON格式的申请策略：
{{
    "application_sequence": [
        {{
            "order": 1,
            "product_name": "产品名称",
            "timing": "申请时机",
            "rationale": "选择理由"
        }}
    ],
    "preparation_checklist": [
        {{
            "category": "材料准备",
            "items": ["所需材料1", "所需材料2"]
        }},
        {{
            "category": "资质提升",
            "items": ["提升建议1", "提升建议2"]
        }}
    ],
    "risk_management": {{
        "max_applications": 2,
        "time_spacing": "申请间隔建议",
        "backup_options": ["备选方案1", "备选方案2"]
    }},
    "success_factors": [
        "成功关键因素1",
        "成功关键因素2"
    ],
    "timeline": "预计申请时间线",
    "strategy_summary": "策略总结"
}}
"""
            
            response = call_deepseek_api_prompt(prompt, max_tokens=1800)
            result = parse_llm_json(response)
            
            if result:
                return result
            else:
                return self._fallback_application_strategy(customer_profile, target_products)
                
        except Exception as e:
            print(f"申请策略优化失败: {e}")
            return self._fallback_application_strategy(customer_profile, target_products)

    def _fallback_customer_analysis(self, customer_profile: Dict[str, Any]) -> Dict[str, Any]:
        """备用客户分析"""
        credit_score = customer_profile.get('credit_score', 650)
        monthly_income = customer_profile.get('monthly_income', 5000)
        
        if credit_score >= 750 and monthly_income >= 15000:
            segment = "优质客户"
            risk_level = "低"
        elif credit_score >= 650 and monthly_income >= 8000:
            segment = "标准客户"
            risk_level = "中"
        else:
            segment = "谨慎客户"
            risk_level = "高"
        
        return {
            "customer_segment": segment,
            "risk_profile": {"risk_level": risk_level, "risk_score": 60},
            "analysis_summary": "基于基础信息的简化分析"
        }

    def _fallback_product_recommendation(self, customer_profile: Dict[str, Any], 
                                       available_products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """备用产品推荐"""
        recommendations = []
        
        for i, product in enumerate(available_products[:3]):
            recommendations.append({
                "product_name": product.get('name', f'产品{i+1}'),
                "match_score": 0.6,
                "suitability_level": "可考虑",
                "recommendation_reasons": ["基于基础匹配逻辑"],
                "key_advantages": ["需要详细分析"],
                "application_tips": ["准备完整材料"]
            })
        
        return recommendations

    def _fallback_product_comparison(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """备用产品对比"""
        return {
            "comparison_summary": "产品各有特色，需要根据具体需求选择",
            "recommendation": {
                "top_choice": products[0].get('name', '产品1') if products else "暂无",
                "reasoning": "基于基础信息的简单对比"
            }
        }

    def _fallback_product_guide(self, customer_profile: Dict[str, Any], 
                               application_info: Dict[str, Any]) -> str:
        """备用产品指南"""
        return f"""
## 金融产品选择指南

### 客户基本情况
根据您提供的信息，我们为您制定以下选择建议。

### 产品推荐策略
1. 根据您的收入和征信情况选择合适的产品
2. 优先考虑利率较低的产品
3. 确保额度满足您的资金需求

### 申请建议
1. 准备完整的申请材料
2. 确保个人信息真实准确
3. 选择合适的申请时机

如需更详细的指导，建议咨询专业的金融顾问。
"""

    def _fallback_fit_assessment(self, customer_profile: Dict[str, Any],
                                product_info: Dict[str, Any]) -> Dict[str, Any]:
        """备用匹配度评估"""
        return {
            "overall_fit_score": 0.6,
            "fit_level": "一般匹配",
            "approval_likelihood": {"probability": 0.5, "confidence_level": "中"},
            "final_recommendation": "谨慎考虑",
            "reasoning": "基于基础信息的简化评估"
        }

    def _fallback_application_strategy(self, customer_profile: Dict[str, Any],
                                     target_products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """备用申请策略"""
        return {
            "application_sequence": [
                {
                    "order": 1,
                    "product_name": target_products[0].get('name', '首选产品') if target_products else "待选择",
                    "timing": "资质准备充分后",
                    "rationale": "基础策略建议"
                }
            ],
            "preparation_checklist": [
                {
                    "category": "基础准备",
                    "items": ["身份证明", "收入证明", "征信报告"]
                }
            ],
            "strategy_summary": "建议先完善个人资质，再选择合适的产品申请"
        }

if __name__ == "__main__":
    # 测试调整后的产品策略
    strategy = ProductStrategy()
    
    # 测试客户信息
    test_customer = {
        "age": 30,
        "monthly_income": 15000,
        "credit_score": 720,
        "employment_years": 5,
        "debt_to_income": 0.3,
        "education": "本科",
        "occupation": "软件工程师"
    }
    
    # 测试申请信息
    test_application = {
        "amount": 200000,
        "purpose": "装修",
        "term_months": 36,
        "urgency": "中等"
    }
    
    # 测试产品信息
    test_products = [
        {
            "name": "个人信用消费贷",
            "type": "消费贷",
            "institution": "工商银行",
            "interest_rate": {"min": 4.35, "max": 15.8},
            "features": {"amount_range": "1-50万", "term_range": "1-5年"},
            "requirements": {"credit_score": "≥650", "monthly_income": "≥5000"}
        },
        {
            "name": "房屋抵押贷款",
            "type": "抵押贷",
            "institution": "建设银行",
            "interest_rate": {"min": 3.85, "max": 8.5},
            "features": {"amount_range": "10-500万", "term_range": "1-30年"},
            "requirements": {"房产价值": "≥50万", "credit_score": "≥600"}
        }
    ]
    
    print("=== 客户分析 ===")
    customer_analysis = strategy.analyze_customer(test_customer)
    print(f"客户类型: {customer_analysis.get('customer_segment', 'N/A')}")
    print(f"风险等级: {customer_analysis.get('risk_profile', {}).get('risk_level', 'N/A')}")
    
    print("\n=== 产品推荐 ===")
    recommendations = strategy.recommend_products(test_customer, test_application, test_products)
    for i, rec in enumerate(recommendations, 1):
        print(f"推荐{i}: {rec.get('product_name', 'N/A')}")
        print(f"  匹配度: {rec.get('match_score', 0)}")
        print(f"  推荐等级: {rec.get('suitability_level', 'N/A')}")
    
    print("\n=== 产品对比 ===")
    comparison = strategy.compare_products(test_products, test_customer)
    print(f"对比总结: {comparison.get('comparison_summary', 'N/A')[:100]}...")
    
    print("\n=== 产品选择指南 ===")
    guide = strategy.generate_product_guide(test_customer, test_application)
    print(f"指南长度: {len(guide)} 字符")
    print(f"指南摘要: {guide[:200]}...")