# rag_system/template_manager.py
import json
from typing import Dict, List, Any, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_doubao_api_prompt, call_deepseek_api_prompt, parse_llm_json

class TemplateManager:
    """基于大模型的模板管理器"""
    
    def __init__(self):
        # 基础模板
        self.response_templates = self._load_response_templates()
        
        # 针对不同策略的模板
        self.strategy_templates = {
            'conflict_resolution': """
非常抱歉给您带来不便。我理解您的感受，您的反馈对我们很重要。

{specific_issue_response}

为了更好地为您服务：
{solution_steps}

如果您仍不满意，我可以为您：
• 转接专人客服
• 记录您的意见建议  
• 提供其他解决方案

请告诉我，我还能为您做些什么？
""",
            
            'risk_assessment': """
基于您提供的信息，风险评估结果如下：

🔍 **风险等级**: {risk_level}
📊 **风险评分**: {risk_score}/100
⚠️ **主要风险因素**: {risk_factors}
✅ **保护因素**: {protective_factors}

📋 **详细分析**:
{detailed_analysis}

💡 **建议措施**:
{suggestions}

{risk_disclaimer}
""",
            
            'product_recommendation': """
根据您的情况，为您推荐以下产品：

{product_list}

🎯 **推荐理由**:
{recommendation_reasons}

📝 **申请建议**:
{application_tips}

{risk_warning}
""",
            
            'qualification_assessment': """
基于您的资质情况，评估结果如下：

👤 **客户类型**: {customer_segment}
💯 **资质评分**: {qualification_score}/100
📈 **批准概率**: {approval_probability}

📊 **优势分析**:
{strengths}

⚠️ **待改进项**:
{improvement_areas}

💼 **推荐产品**:
{recommended_products}

{assessment_disclaimer}
"""
        }
        
        # 动态模板生成提示词
        self.template_generation_prompts = {
            "product_template": """请为金融产品咨询生成回复模板，要求包含：
- 产品基本信息（名称、类型、机构）
- 核心指标（利率、额度、期限）
- 申请条件
- 产品特色
- 风险提示

模板应该专业、结构清晰，支持变量替换。""",
            
            "comparison_template": """请为金融产品对比生成回复模板，要求包含：
- 对比维度说明
- 表格式对比展示
- 优劣势分析
- 个性化建议

模板应该客观、易于理解。""",
            
            "assessment_template": """请为客户资质评估生成回复模板，要求包含：
- 客户基本画像
- 风险等级评估
- 通过概率预测
- 改进建议
- 产品推荐

模板应该专业、有针对性。"""
        }
    
    def get_response_template(self, intent: str, scenario: str = "default") -> str:
        """获取回答模板，支持动态生成"""
        try:
            template_key = f"{intent}_{scenario}"
            
            # 先从预设模板中查找
            if template_key in self.response_templates:
                return self.response_templates[template_key]
            elif intent in self.response_templates:
                return self.response_templates[intent]
            
            # 如果没有预设模板，使用大模型动态生成
            return self._generate_template_with_llm(intent, scenario)
            
        except Exception as e:
            print(f"获取回答模板失败: {e}")
            return self.response_templates["default"]
    
    def get_strategy_template(self, strategy: str, context: Dict[str, Any]) -> str:
        """获取策略专用模板"""
        try:
            template = self.strategy_templates.get(strategy, self.response_templates.get("default", ""))
            
            # 格式化模板
            if template and context:
                try:
                    return template.format(**context)
                except KeyError as e:
                    print(f"模板格式化失败，缺少参数: {e}")
                    # 返回未格式化的模板
                    return template
            
            return template
            
        except Exception as e:
            print(f"获取策略模板失败: {e}")
            return self.response_templates.get("default", "")
    
    def format_product_response(self, product_info: Dict[str, Any], 
                               intent: str = "产品咨询") -> str:
        """使用大模型格式化产品回复"""
        try:
            prompt = f"""
请根据以下产品信息生成专业的金融产品介绍回复：

产品信息：
{json.dumps(product_info, ensure_ascii=False, indent=2)}

用户意图：{intent}

要求：
1. 内容准确专业
2. 结构清晰易懂
3. 突出关键信息（利率、额度、条件等）
4. 包含适当的风险提示
5. 语言通俗但专业

请生成完整的回复内容：
"""
            
            response = call_deepseek_api_prompt(prompt)
            return self._add_compliance_footer(response)
            
        except Exception as e:
            print(f"格式化产品回复失败: {e}")
            return self._fallback_product_response(product_info)
    
    def format_case_response(self, case_info: Dict[str, Any], 
                            intent: str = "资质评估") -> str:
        """使用大模型格式化案例回复"""
        try:
            prompt = f"""
请根据以下案例信息生成专业的客户资质评估回复：

案例信息：
{json.dumps(case_info, ensure_ascii=False, indent=2)}

用户意图：{intent}

要求：
1. 客观分析客户情况
2. 基于案例给出建议
3. 提供成功要素分析
4. 包含风险提示
5. 给出实用的行动建议

请生成完整的评估回复：
"""
            
            response = call_deepseek_api_prompt(prompt)
            return self._add_compliance_footer(response)
            
        except Exception as e:
            print(f"格式化案例回复失败: {e}")
            return self._fallback_case_response(case_info)
    
    def format_comparison_response(self, products: List[Dict[str, Any]]) -> str:
        """使用大模型格式化对比回复"""
        try:
            prompt = f"""
请根据以下产品信息生成专业的产品对比分析：

产品信息：
{json.dumps(products, ensure_ascii=False, indent=2)}

要求：
1. 生成清晰的对比表格
2. 从多个维度进行比较（利率、额度、条件、特色等）
3. 客观分析各产品优劣势
4. 给出选择建议
5. 包含风险提示

请生成完整的对比分析：
"""
            
            response = call_deepseek_api_prompt(prompt)
            return self._add_compliance_footer(response)
            
        except Exception as e:
            print(f"格式化对比回复失败: {e}")
            return self._fallback_comparison_response(products)
    
    def format_policy_response(self, policy_info: Dict[str, Any]) -> str:
        """使用大模型格式化政策回复"""
        try:
            prompt = f"""
请根据以下政策信息生成专业的政策解读回复：

政策信息：
{json.dumps(policy_info, ensure_ascii=False, indent=2)}

要求：
1. 解释政策要点
2. 分析对客户的影响
3. 提供合规建议
4. 语言通俗易懂
5. 突出重要变化

请生成完整的政策解读：
"""
            
            response = call_deepseek_api_prompt(prompt)
            return self._add_compliance_footer(response)
            
        except Exception as e:
            print(f"格式化政策回复失败: {e}")
            return self._fallback_policy_response(policy_info)
    
    def build_llm_prompt(self, task: str, context: Dict[str, Any]) -> str:
        """构建优化的LLM提示词"""
        try:
            base_prompt = self._get_base_prompt_for_task(task)
            
            if task == "response_generation":
                return self._build_response_generation_prompt(context)
            elif task == "intent_classification":
                return self._build_intent_classification_prompt(context)
            elif task == "entity_extraction":
                return self._build_entity_extraction_prompt(context)
            elif task == "query_rewrite":
                return self._build_query_rewrite_prompt(context)
            else:
                return self._build_general_prompt(task, context)
                
        except Exception as e:
            print(f"构建LLM提示词失败: {e}")
            return context.get('query', '')
    
    def generate_dynamic_template(self, intent: str, example_data: Dict[str, Any]) -> str:
        """动态生成新的回复模板"""
        try:
            prompt = f"""
请为意图"{intent}"设计一个专业的回复模板。

参考数据示例：
{json.dumps(example_data, ensure_ascii=False, indent=2)}

要求：
1. 模板应该支持变量替换（使用{{variable_name}}格式）
2. 结构清晰，逻辑合理
3. 符合金融行业规范
4. 包含必要的风险提示
5. 语言专业但易懂

请生成模板代码：
"""
            
            template = call_deepseek_api_prompt(prompt)
            
            # 保存到模板库
            self.response_templates[intent] = template
            
            return template
            
        except Exception as e:
            print(f"动态生成模板失败: {e}")
            return self.response_templates["default"]
    
    def optimize_template_with_feedback(self, template: str, feedback: str) -> str:
        """基于反馈优化模板"""
        try:
            prompt = f"""
请根据反馈优化以下回复模板：

原模板：
{template}

反馈意见：
{feedback}

要求：
1. 保持模板的核心结构
2. 根据反馈改进不足之处
3. 提高模板的实用性和准确性
4. 确保符合合规要求

请提供优化后的模板：
"""
            
            optimized_template = call_deepseek_api_prompt(prompt)
            return optimized_template
            
        except Exception as e:
            print(f"优化模板失败: {e}")
            return template
    
    def _generate_template_with_llm(self, intent: str, scenario: str) -> str:
        """使用大模型生成模板"""
        try:
            base_prompt = self.template_generation_prompts.get(
                f"{intent}_template", 
                self.template_generation_prompts.get("product_template", "")
            )
            
            prompt = f"""
{base_prompt}

意图：{intent}
场景：{scenario}

请生成一个支持变量替换的回复模板（使用{{variable_name}}格式）：
"""
            
            template = call_deepseek_api_prompt(prompt)
            
            # 缓存生成的模板
            self.response_templates[f"{intent}_{scenario}"] = template
            
            return template
            
        except Exception as e:
            print(f"LLM生成模板失败: {e}")
            return self.response_templates["default"]
    
    def _build_response_generation_prompt(self, context: Dict[str, Any]) -> str:
        """构建回复生成提示词"""
        prompt = f"""
你是专业的金融贷款咨询助手，请根据以下信息为用户提供准确、有用的回答。

用户问题：{context.get('query', '')}
用户意图：{context.get('intent', '')}

相关信息：
{context.get('context_info', '')}

对话历史：
{context.get('conversation_history', '')}

请遵循以下要求：
1. 基于提供的信息准确回答，不编造信息
2. 语言专业但通俗易懂
3. 突出关键信息（利率、额度、条件等）
4. 提供实用的建议和指导
5. 避免绝对性承诺，注意合规表达
6. 如信息不足，建议咨询专业人士
7. 包含必要的风险提示

请提供专业的回答：
"""
        return prompt
    
    def _build_intent_classification_prompt(self, context: Dict[str, Any]) -> str:
        """构建意图分类提示词"""
        intent_categories = [
            "产品咨询", "申请条件", "利率查询", "额度评估", 
            "申请流程", "资质评估", "产品对比", "风险评估", 
            "合规咨询", "还款方式", "服务投诉", "一般咨询"
        ]
        
        prompt = f"""
请识别以下用户查询的意图类别。

查询内容：{context.get('query', '')}

可选意图类别：
{', '.join(intent_categories)}

请返回JSON格式的结果：
{{
    "intent": "最匹配的意图类别",
    "confidence": 0.85,
    "reasoning": "分类理由"
}}
"""
        return prompt
    
    def _build_entity_extraction_prompt(self, context: Dict[str, Any]) -> str:
        """构建实体提取提示词"""
        entity_types = [
            "金额", "利率", "期限", "机构", "产品类型", 
            "征信分数", "收入", "年龄", "城市"
        ]
        
        prompt = f"""
请从以下文本中提取相关的金融实体。

文本内容：{context.get('query', '')}

实体类型：
{', '.join(entity_types)}

请返回JSON格式的结果：
{{
    "entities": {{
        "金额": ["提取的金额"],
        "利率": ["提取的利率"],
        "机构": ["提取的机构名称"]
    }}
}}
"""
        return prompt
    
    def _build_query_rewrite_prompt(self, context: Dict[str, Any]) -> str:
        """构建查询重写提示词"""
        prompt = f"""
请将以下用户查询重写为更适合检索的形式。

原始查询：{context.get('query', '')}
用户意图：{context.get('intent', '')}
已识别实体：{context.get('entities', {})}

重写要求：
1. 保持原意不变
2. 补充必要的金融术语
3. 提高检索精确度
4. 返回2-3个重写版本

请返回JSON格式的结果：
{{
    "rewritten_queries": [
        "重写版本1",
        "重写版本2", 
        "重写版本3"
    ]
}}
"""
        return prompt
    
    def _build_general_prompt(self, task: str, context: Dict[str, Any]) -> str:
        """构建通用提示词"""
        prompt = f"""
任务：{task}

输入信息：
{json.dumps(context, ensure_ascii=False, indent=2)}

请根据任务要求处理输入信息并返回结果。
"""
        return prompt
    
    def _get_base_prompt_for_task(self, task: str) -> str:
        """获取任务的基础提示词"""
        base_prompts = {
            "response_generation": "生成专业的金融咨询回复",
            "intent_classification": "识别用户查询意图",
            "entity_extraction": "提取金融相关实体",
            "query_rewrite": "优化查询以提高检索效果",
            "summarization": "总结金融信息"
        }
        return base_prompts.get(task, "处理金融相关任务")
    
    def _add_compliance_footer(self, response: str) -> str:
        """添加合规声明"""
        if "风险提示" not in response and "仅供参考" not in response:
            return response + "\n\n风险提示：以上信息仅供参考，具体以金融机构实际政策为准。借贷有风险，申请需谨慎。"
        return response
    
    def _fallback_product_response(self, product_info: Dict[str, Any]) -> str:
        """备用产品回复"""
        name = product_info.get('name', '该产品')
        return f"关于{name}的详细信息，建议您直接咨询相关金融机构获取最新准确信息。"
    
    def _fallback_case_response(self, case_info: Dict[str, Any]) -> str:
        """备用案例回复"""
        return "根据类似案例分析，建议您准备完整的申请材料，并确保个人资质符合要求。具体情况建议咨询专业顾问。"
    
    def _fallback_comparison_response(self, products: List[Dict[str, Any]]) -> str:
        """备用对比回复"""
        return "产品对比信息正在整理中，建议您根据个人需求和资质情况选择合适的产品。如需详细对比，请咨询专业顾问。"
    
    def _fallback_policy_response(self, policy_info: Dict[str, Any]) -> str:
        """备用政策回复"""
        return "相关政策信息建议您查阅官方文件或咨询专业人士，以确保获得最新准确的政策解读。"
    
    def _load_response_templates(self) -> Dict[str, str]:
        """加载基础回答模板"""
        return {
            "default": "根据您的咨询，相关信息如下：{content}。如需更详细信息，建议咨询专业顾问。",
            
            "产品咨询": "关于{product_name}，主要特点如下：利率范围{interest_rate}，额度范围{amount_range}，贷款期限{term_range}。主要特征：{features}。",
            
            "申请条件": "{product_name}的申请条件包括：{requirements}。建议您准备相关材料并确保符合基本要求。",
            
            "利率查询": "{product_name}的年化利率为{interest_rate}，具体利率会根据您的征信状况、收入水平等因素综合确定。",
            
            "额度评估": "根据{product_name}，贷款额度范围为{amount_range}。实际额度主要根据月收入水平、征信记录、负债情况等因素确定。",
            
            "申请流程": "贷款申请流程：1) 准备材料 2) 提交申请 3) 审核评估 4) 签署合同 5) 放款到账。具体流程可能因机构而异。",
            
            "风险评估": "根据风险评估，您的风险等级为{risk_level}，主要风险因素包括{risk_factors}。建议{risk_suggestions}。",
            
            "一般咨询": "根据您的问题，{response_content}。如需更多帮助，建议咨询专业的金融顾问。"
        }

if __name__ == "__main__":
    # 测试模板管理器
    template_manager = TemplateManager()
    
    # 测试策略模板
    print("=== 测试策略模板 ===")
    context = {
        'risk_level': '中',
        'risk_score': 75,
        'risk_factors': '收入不稳定, 负债率偏高',
        'protective_factors': '征信良好, 有抵押物',
        'detailed_analysis': '客户整体风险可控，但需要关注收入稳定性',
        'suggestions': '• 提供收入证明\n• 降低负债率',
        'risk_disclaimer': '\n风险提示：评估仅供参考，实际风险可能变化。'
    }
    
    risk_template = template_manager.get_strategy_template('risk_assessment', context)
    print(f"风险评估模板: {risk_template}")
    
    # 测试产品回复格式化
    print("\n=== 测试产品回复格式化 ===")
    test_product = {
        'name': '个人消费贷款',
        'institution': '工商银行',
        'interest_rate': {'min': 4.35, 'max': 15.8},
        'features': {'amount_range': '1-50万', 'term_range': '1-5年'}
    }
    
    product_response = template_manager.format_product_response(test_product)
    print(f"产品回复: {product_response}")