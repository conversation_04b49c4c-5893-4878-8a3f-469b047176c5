# llm_api.py
import os
import base64
from io import BytesIO
from PIL import Image
import re 
from typing import Dict, Any
import json
from openai import OpenAI
import time
from volcenginesdkarkruntime import Ark

# 配置Deepseek API
# Deepseek API配置
deepseek_api_key = "***********************************"
deepseek_base_url = "https://api.deepseek.com/v1"
deepseek_client = OpenAI(api_key=deepseek_api_key, base_url=deepseek_base_url)

# 配置豆包API
doubao_api_key = "1bca94f1-768d-4546-9ef1-2b1d99bfa30c"
doubao_base_url = "https://ark.cn-beijing.volces.com/api/v3"
doubao_client = OpenAI(api_key=doubao_api_key, base_url=doubao_base_url)

def parse_llm_json(response: str):
    """解析大模型生成的JSON响应，支持对象和数组"""
    if not response:
        return {}
    
    # 移除markdown代码块标记
    text = response.strip()
    text = re.sub(r'```json\s*', '', text)
    text = re.sub(r'```\s*', '', text)
    
    # 查找JSON对象或数组
    # 首先尝试找到完整的JSON结构
    json_candidates = []
    
    # 查找对象 {}
    obj_start = text.find('{')
    obj_end = text.rfind('}')
    if obj_start != -1 and obj_end != -1 and obj_start < obj_end:
        json_candidates.append((obj_start, obj_end + 1, text[obj_start:obj_end + 1]))
    
    # 查找数组 []
    arr_start = text.find('[')
    arr_end = text.rfind(']')
    if arr_start != -1 and arr_end != -1 and arr_start < arr_end:
        json_candidates.append((arr_start, arr_end + 1, text[arr_start:arr_end + 1]))
    
    # 如果没有找到任何JSON结构
    if not json_candidates:
        return {}
    
    # 选择最早开始的JSON结构
    json_candidates.sort(key=lambda x: x[0])
    
    # 尝试解析每个候选JSON
    for start_pos, end_pos, json_text in json_candidates:
        try:
            result = json.loads(json_text)
            if isinstance(result, (dict, list)):
                return result
        except json.JSONDecodeError:
            continue
    
    # 如果所有候选都解析失败，尝试更智能的查找
    # 寻找嵌套平衡的JSON结构
    for start_char, end_char in [('{', '}'), ('[', ']')]:
        start_idx = text.find(start_char)
        if start_idx == -1:
            continue
            
        bracket_count = 0
        for i, char in enumerate(text[start_idx:], start_idx):
            if char == start_char:
                bracket_count += 1
            elif char == end_char:
                bracket_count -= 1
                if bracket_count == 0:
                    json_text = text[start_idx:i + 1]
                    try:
                        result = json.loads(json_text)
                        if isinstance(result, (dict, list)):
                            return result
                    except json.JSONDecodeError:
                        break
    
    return {}

def image_to_base64_url(image_path):
    """将图片转换为base64 URL格式"""
    try:
        with Image.open(image_path) as img:
            # 转换为RGB模式
            if img.mode != 'RGB':
                img = img.convert('RGB')
            # 创建内存缓冲区
            buffered = BytesIO()
            # 保存为JPEG格式
            img.save(buffered, format="JPEG")
            # 获取base64编码
            img_str = base64.b64encode(buffered.getvalue()).decode('utf-8')
            # 返回base64 URL格式
            return f"data:image/jpeg;base64,{img_str}"
    except Exception as e:
        print(f"图片转换base64失败: {str(e)}")
        return None

def call_doubao_api(messages, max_tokens=4000, model="doubao-1-5-pro-32k-250115", temperature=0.1, stream=False):
    """
    调用豆包模型API
    
    """
    if stream:
        # 流式调用
        stream_response = doubao_client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
            stream=True,
        )
        return stream_response
    else:
        # 非流式调用
        completion = doubao_client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens,
        )
        return completion.choices[0].message.content

def call_deepseek_api(messages, max_tokens=4000, model="deepseek-chat", temperature=0.1):
    completion = deepseek_client.chat.completions.create(
        model=model,
        messages=messages,
        temperature=temperature,
        max_tokens=max_tokens,
    )
    return completion.choices[0].message.content

def call_doubao_api_prompt(prompt):
    """使用单个提示调用大模型API"""
    return call_doubao_api([{"role": "user", "content": prompt}])

def call_deepseek_api_prompt(prompt, max_tokens=4000, model="deepseek-chat"):
    """使用单个提示调用Deepseek API"""
    return call_deepseek_api([{"role": "user", "content": prompt}], max_tokens, model)

def call_embedding_api(text):
    """获取文本的向量表示"""
    try:
        response = doubao_client.embeddings.create(
            model="doubao-embedding-text-240715",  # 替换为实际使用的embedding模型
            input=text,
            encoding_format="float"
        )
        return response.data[0].embedding
    except Exception as e:
        print(f"Embedding API调用失败: {str(e)}")
        # 发生异常时返回零向量
        return [0.0] * 1536

def call_vision_api(image_path, question="请描述这张医学图像的内容"):
    """调用多模态大模型API处理图像"""
    try:
        if "http" in image_path:
            image_url = image_path
        else:
            # 将图片转换为base64
            image_url = image_to_base64_url(image_path)
            if not image_url:
                return {"error": "图片转换失败"}

        # 调用多模态API
        response = doubao_client.chat.completions.create(
            model="doubao-vision-pro",  # 替换为实际的多模态模型
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": question},
                        {
                            "type": "image_url",
                            "image_url": {"url": image_url}
                        },
                    ],
                }
            ],
        )
        
        return response.choices[0].message.content
    except Exception as e:
        return f"无法处理图像: {str(e)}"
    

def call_doubao_generate_image_api(prompt, size="1024x1024", response_format="url"):
    """
    调用方舟推理接口生成图片。

    参数:
        prompt (str): 图片描述文本。
        size (str): 图片大小，默认为 "1024x1024"。
        response_format (str): 返回格式，默认为 "url"。

    返回:
        str: 生成图片的 URL。
    """
    # 调用图片生成接口
    response = doubao_client.images.generate(
        model="ep-20250623211534-xjwdh",
        prompt=prompt,
        size=size,
        response_format=response_format
    )

    # 返回生成图片的 URL
    return response.data[0].url


def call_doubao_generate_video_api(prompt, image_url, model_ep="ep-20250623211334-8fdmf", resolution="720p", duration=5):
    """
    调用方舟推理接口生成视频。

    参数:
        prompt (str): 视频描述文本。
        image_url (str): 首帧图片的 URL。
        model_ep (str): 模型的 Endpoint ID，默认为 "ep-20250623211334-8fdmf"。
        resolution (str): 视频分辨率，默认为 "720p"。
        duration (int): 视频时长，默认为 5 秒。

    返回:
        dict: 包含任务状态和视频 URL 的字典。
    """
    # 初始化方舟客户端
    client = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=doubao_api_key,
    )

    # 创建任务请求
    create_result = client.content_generation.tasks.create(
        model=model_ep,
        content=[
            {
                "type": "text",
                "text": f"{prompt} --resolution {resolution} --duration {duration}"
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": image_url
                }
            }
        ]
    )

    # 检查任务是否创建成功
    if not create_result:
        raise Exception("Failed to create content generation task.")

    task_id = create_result.id
    print(f"创建的Task ID: {task_id}")

    # 轮询查询任务状态
    while True:
        get_result = client.content_generation.tasks.get(task_id=task_id)
        status = get_result.status

        if status == "succeeded":
            print("----- task succeeded -----")
            
            # 修复这里：正确处理Content对象
            video_url = None
            if hasattr(get_result, 'content') and get_result.content:
                # 如果content是字典类型
                if isinstance(get_result.content, dict):
                    video_url = get_result.content.get("video_url")
                # 如果content是对象类型，尝试直接访问属性
                elif hasattr(get_result.content, 'video_url'):
                    video_url = get_result.content.video_url
                # 如果content有其他结构，尝试转换为字典
                else:
                    try:
                        content_dict = dict(get_result.content)
                        video_url = content_dict.get("video_url")
                    except:
                        # 最后的备用方案，直接从结果中查找
                        video_url = getattr(get_result, 'video_url', None)
            
            return {
                "task_id": task_id,
                "model": model_ep,
                "video_url": video_url,
                "status": status,
                "created_at": get_result.created_at,
                "updated_at": get_result.updated_at,
                "raw_content": get_result.content  # 添加原始内容用于调试
            }
        elif status == "failed":
            print("----- task failed -----")
            error_message = None
            if hasattr(get_result, 'error') and get_result.error:
                if isinstance(get_result.error, dict):
                    error_message = get_result.error.get("message", "Unknown error")
                elif hasattr(get_result.error, 'message'):
                    error_message = get_result.error.message
                else:
                    error_message = str(get_result.error)
            else:
                error_message = "Unknown error"
            
            print(f"Error: {error_message}")
            return {
                "task_id": task_id,
                "status": status,
                "error": error_message
            }
        else:
            print(f"Current status: {status}, Retrying after 5 seconds...")
            time.sleep(5)



if __name__ == "__main__":
    # print(call_llm_api_prompt("你好"))
#     messages = [
#     {"role": "system", "content": "你是一个专业的医生问诊助手AI，能够分析患者症状、协助诊断和提供治疗建议。基于循证医学和临床诊疗指南，为医生提供专业的医疗决策支持。请注意，你的建议仅供参考，最终诊断和治疗方案需要执业医师确认。"},
#     {"role": "user", "content": "患者主诉：最近一周出现持续性胸痛，疼痛位于胸骨后，有压迫感，活动时加重，休息后可缓解。患者既往有高血压病史，目前正在服用降压药。请分析可能的诊断和下一步处理建议。"},
#     {"role": "assistant", "content": "根据患者的症状描述，需要重点考虑以下几种可能：\n\n1. **冠心病/心绞痛**（最需要警惕）\n   - 典型的胸骨后压迫性疼痛\n   - 活动诱发，休息缓解\n   - 有高血压危险因素\n\n2. **其他可能诊断**\n   - 不稳定型心绞痛\n   - 主动脉夹层（需紧急排除）\n   - 肺栓塞\n   - 胃食管反流病\n\n**建议的检查和处理**：\n- 立即行心电图检查\n- 查心肌酶谱（肌钙蛋白I/T）\n- 胸片排除其他病因\n- 评估TIMI危险分层\n- 如疑似急性冠脉综合征，建议急诊科处理\n- 口服阿司匹林100mg（如无禁忌症）\n\n**注意事项**：患者症状提示可能存在急性冠脉事件，建议尽快完善相关检查，必要时急诊冠脉造影。"},
#     {"role": "user", "content": "我该怎么办"},
# ]
    # response = call_deepseek_api(messages)
    # print("医生问诊助手响应:", response)
    # print(call_deepseek_api_prompt("你好"))
    # response = call_doubao_api(messages)
    # print("医生问诊助手响应:", response)
    # print(call_doubao_api_prompt("你好"))

    # text = "患者主诉：最近一周出现持续性胸痛，疼痛位于胸骨后，有压迫感，活动时加重，休息后可缓解。患者既往有高血压病史，目前正在服用降压药。请分析可能的诊断和下一步处理建议。"
    # print(call_embedding_api(text))

    # image_url = call_doubao_generate_image_api(prompt="一只可爱的大熊猫在欢乐地跳舞，背景是一个充满自然气息的竹林，阳光透过树叶洒下温暖的光芒。这张图片适合用于儿童玩具、亲子活动或自然主题的电商广告，营造出温馨、欢乐的氛围。")
    # print(image_url)

    prompt = "女孩抱着狐狸，女孩睁开眼，温柔地看向镜头，狐狸友善地抱着，镜头缓缓拉出，女孩的头发被风吹动"
    image_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png"

    result = call_doubao_generate_video_api(prompt, image_url)
    print(result)
