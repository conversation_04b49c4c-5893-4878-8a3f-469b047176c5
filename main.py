# main.py
import os
import json
import re
from typing import Dict, List, Any, Optional
from config.financial_config import FinancialConfig
from retrieval.hybrid_retriever import HybridRetriever
from rag_system.query_rewriter import QueryRewriter
from rag_system.response_generator import ResponseGenerator
from rag_system.template_manager import TemplateManager
from rag_system.compliance_strategy import ComplianceStrategy
from rag_system.risk_strategy import RiskStrategy
from rag_system.product_strategy import ProductStrategy
from rag_system.conflict_resolver import ConflictResolver
from retrieval.rerank import Reranker
from financial_analyzer import FinancialAnalyzer

class FinancialRAGSystem:
    """金融RAG问答系统主类 - 集成各种策略"""
    
    def __init__(self, config_path: Optional[str] = None):
        # 加载配置
        self.config = FinancialConfig()
        
        # 初始化核心组件
        self.query_rewriter = QueryRewriter()
        self.retriever = HybridRetriever(
            "./data/knowledge_base/knowledge.db",
            "./data/knowledge_base/index"
        )
        self.reranker = Reranker()
        self.response_generator = ResponseGenerator()
        self.template_manager = TemplateManager()
        
        # 初始化各种策略
        self.compliance_strategy = ComplianceStrategy()
        self.risk_strategy = RiskStrategy()
        self.product_strategy = ProductStrategy()
        self.conflict_resolver = ConflictResolver()
        self.financial_analyzer = FinancialAnalyzer()
        
        # 对话历史
        self.conversation_history = []
        
        print("金融RAG系统初始化完成")
    
    def _safe_extract_number(self, value: Any) -> float:
        """安全提取数字值"""
        if value is None:
            return 0.0
        
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            # 去除非数字字符
            value = value.strip()
            if not value:
                return 0.0
            
            # 处理中文数字单位
            if '万' in value:
                match = re.search(r'(\d+(?:\.\d+)?)', value.replace('万', ''))
                if match:
                    return float(match.group(1)) * 10000
            elif '千' in value:
                match = re.search(r'(\d+(?:\.\d+)?)', value.replace('千', ''))
                if match:
                    return float(match.group(1)) * 1000
            
            # 提取纯数字
            match = re.search(r'(\d+(?:\.\d+)?)', value)
            if match:
                return float(match.group(1))
        
        return 0.0
    
    def query(self, user_query: str, customer_profile: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """处理用户查询 - 根据意图分发到不同策略"""
        try:
            print(f"\n用户查询: {user_query}")
            
            # 1. 冲突检测
            conflict_info = self.conflict_resolver.detect_service_conflicts(
                self.conversation_history, user_query
            )
            
            if conflict_info.get("has_conflict"):
                return self._handle_service_conflict(user_query, conflict_info)
            
            # 2. 查询重写和意图识别
            query_info = self.query_rewriter.process_query(
                user_query, 
                self.conversation_history
            )
            
            intent = query_info['intent']
            print(f"识别意图: {intent} (置信度: {query_info['intent_confidence']:.3f})")
            
            # 3. 根据意图分发到不同处理策略
            if intent == "风险评估":
                result = self._handle_risk_assessment(query_info, customer_profile)
            elif intent == "产品咨询" or intent == "产品对比":
                result = self._handle_product_consultation(query_info, customer_profile)
            elif intent == "资质评估":
                result = self._handle_qualification_assessment(query_info, customer_profile)
            elif intent == "合规咨询":
                result = self._handle_compliance_consultation(query_info)
            elif intent in ["申请条件", "申请流程", "利率查询", "额度评估"]:
                result = self._handle_standard_query(query_info, customer_profile)
            elif intent == "服务投诉":
                return self._handle_service_conflict(user_query, {"has_conflict": True, "conflict_types": ["服务投诉"]})
            else:
                result = self._handle_general_consultation(query_info, customer_profile)
            
            # 4. 合规检查
            result = self._apply_compliance_check(result)
            
            # 5. 更新对话历史
            self._update_conversation_history(user_query, result['response'])
            
            return result
            
        except Exception as e:
            print(f"查询处理失败: {e}")
            return self._generate_error_response(user_query, str(e))
    
    def _handle_service_conflict(self, user_query: str, conflict_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理服务冲突"""
        try:
            print(f"检测到服务冲突: {conflict_info.get('conflict_types', [])}")
            
            # 生成冲突处理响应
            conflict_response = self.conflict_resolver.generate_conflict_response(
                conflict_info, user_query, self.conversation_history
            )
            
            # 格式化用户友好回复
            formatted_response = self.conflict_resolver.format_user_friendly_response(
                conflict_response
            )
            
            # 分析满意度
            satisfaction = self.conflict_resolver.analyze_user_satisfaction(
                self.conversation_history
            )
            
            # 生成恢复计划
            recovery_plan = self.conflict_resolver.generate_service_recovery_plan(
                conflict_info, satisfaction
            )
            
            return {
                'query': user_query,
                'intent': '服务冲突',
                'response': formatted_response,
                'confidence': 0.9,
                'conflict_info': conflict_info,
                'satisfaction_analysis': satisfaction,
                'recovery_plan': recovery_plan,
                'sources': [],
                'follow_up_questions': ["还有什么我可以为您改进的吗？", "需要我为您转接专人服务吗？"],
                'processing_info': {
                    'strategy': 'conflict_resolution',
                    'severity': conflict_info.get('severity', '中')
                }
            }
            
        except Exception as e:
            print(f"冲突处理失败: {e}")
            return self._generate_error_response(user_query, f"冲突处理异常: {e}")
    
    def _handle_risk_assessment(self, query_info: Dict[str, Any], 
                               customer_profile: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """处理风险评估查询"""
        try:
            print("使用风险策略处理查询")
            
            if not customer_profile:
                # 从查询中提取客户信息
                entities = query_info.get('entities', {})
                customer_profile = self._extract_customer_profile_from_entities(entities)
            
            if customer_profile:
                # 获取申请信息
                application_info = self._extract_application_info_from_entities(
                    query_info.get('entities', {})
                )
                
                # 风险评估
                risk_assessment = self.risk_strategy.assess_customer_risk(
                    customer_profile, application_info
                )
                
                # 违约概率预测
                if application_info.get('amount') and application_info.get('term_months'):
                    default_prediction = self.risk_strategy.predict_default_probability(
                        customer_profile, 
                        application_info['amount'], 
                        application_info['term_months']
                    )
                    risk_assessment['default_prediction'] = default_prediction
                
                # 生成风险报告
                risk_report = self.risk_strategy.generate_risk_report(
                    customer_profile, application_info
                )
                
                # 使用策略专用模板格式化响应
                template_context = {
                    'risk_level': risk_assessment.get('risk_level', '中'),
                    'risk_score': risk_assessment.get('risk_score', 60),
                    'risk_factors': ', '.join(risk_assessment.get('risk_factors', [])),
                    'protective_factors': ', '.join(risk_assessment.get('protective_factors', [])),
                    'detailed_analysis': risk_assessment.get('assessment_details', ''),
                    'suggestions': '\n'.join(f"• {s}" for s in risk_assessment.get('mitigation_suggestions', [])),
                    'risk_disclaimer': "\n风险提示：以上评估仅供参考，实际风险可能因市场变化而调整。"
                }
                
                response = self.template_manager.get_strategy_template('risk_assessment', template_context)
                
                return {
                    'query': query_info['original_query'],
                    'intent': '风险评估',
                    'response': response,
                    'confidence': 0.9,
                    'risk_assessment': risk_assessment,
                    'risk_report': risk_report,
                    'sources': [],
                    'follow_up_questions': [
                        "需要我为您分析具体的风险因素吗？",
                        "您想了解如何降低风险吗？",
                        "需要我推荐适合的产品吗？"
                    ],
                    'processing_info': {
                        'strategy': 'risk_assessment',
                        'risk_level': risk_assessment.get('risk_level', '中')
                    }
                }
            else:
                # 缺少客户信息，请求补充
                return {
                    'query': query_info['original_query'],
                    'intent': '风险评估',
                    'response': "为了给您准确的风险评估，请提供您的基本信息：年龄、月收入、征信分数、工作年限等。",
                    'confidence': 0.7,
                    'sources': [],
                    'follow_up_questions': [
                        "请告诉我您的年龄和月收入",
                        "您的征信分数大概是多少？",
                        "您的工作年限是多久？"
                    ],
                    'processing_info': {'strategy': 'risk_assessment', 'status': 'need_more_info'}
                }
                
        except Exception as e:
            print(f"风险评估处理失败: {e}")
            return self._generate_error_response(query_info['original_query'], f"风险评估异常: {e}")
    
    def _handle_product_consultation(self, query_info: Dict[str, Any], 
                                   customer_profile: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """处理产品咨询和对比"""
        try:
            print("使用产品策略处理查询")
            
            intent = query_info['intent']
            
            # 检索相关产品信息
            retrieval_queries = self.query_rewriter.rewrite_for_retrieval(query_info)
            all_chunks = []
            
            for r_query in retrieval_queries[:3]:
                chunks = self.retriever.search_with_intent(
                    r_query, intent, content_types=['product_basic_info', 'product_features'], top_k=10
                )
                all_chunks.extend(chunks)
            
            # 去重和重排序
            unique_chunks = self._deduplicate_chunks(all_chunks)
            if customer_profile:
                reranked_chunks = self.reranker.rerank_financial_results(
                    query_info['original_query'], unique_chunks, customer_profile, top_k=5
                )
            else:
                reranked_chunks = self.reranker.rerank_by_intent(
                    query_info['original_query'], intent, unique_chunks, top_k=5
                )
            
            # 提取产品信息
            products = self._extract_products_from_chunks(reranked_chunks)
            
            if intent == "产品对比" and len(products) >= 2:
                # 产品对比处理
                comparison_result = self.product_strategy.compare_products(
                    products, customer_profile
                )
                
                response = self.template_manager.format_comparison_response(products)
                
                return {
                    'query': query_info['original_query'],
                    'intent': '产品对比',
                    'response': response,
                    'confidence': 0.85,
                    'comparison_result': comparison_result,
                    'products_compared': products,
                    'sources': self._format_sources(reranked_chunks),
                    'follow_up_questions': [
                        "您想了解哪个产品的详细申请条件？",
                        "需要我为您推荐最适合的产品吗？",
                        "您还想对比其他产品吗？"
                    ],
                    'processing_info': {
                        'strategy': 'product_comparison',
                        'products_count': len(products)
                    }
                }
            else:
                # 单产品咨询或推荐
                if customer_profile:
                    # 客户画像分析
                    customer_analysis = self.product_strategy.analyze_customer(customer_profile)
                    
                    # 产品推荐
                    application_info = self._extract_application_info_from_entities(
                        query_info.get('entities', {})
                    )
                    
                    recommendations = self.product_strategy.recommend_products(
                        customer_profile, application_info, products
                    )
                    
                    # 使用策略模板
                    product_list = ""
                    for i, rec in enumerate(recommendations[:3], 1):
                        product_list += f"{i}. **{rec['product_name']}**\n"
                        product_list += f"   匹配度：{rec['match_score']:.0%} | {rec['suitability_level']}\n"
                        product_list += f"   推荐理由：{', '.join(rec['recommendation_reasons'][:2])}\n\n"
                    
                    template_context = {
                        'product_list': product_list,
                        'recommendation_reasons': '\n'.join(f"• {r}" for r in recommendations[0]['recommendation_reasons'][:3]) if recommendations else "",
                        'application_tips': '\n'.join(f"• {t}" for t in recommendations[0]['application_tips'][:3]) if recommendations else "",
                        'risk_warning': "\n风险提示：产品选择应结合个人实际情况，建议详细了解条款。"
                    }
                    
                    response = self.template_manager.get_strategy_template('product_recommendation', template_context)
                    
                    return {
                        'query': query_info['original_query'],
                        'intent': '产品咨询',
                        'response': response,
                        'confidence': 0.9,
                        'customer_analysis': customer_analysis,
                        'recommendations': recommendations,
                        'sources': self._format_sources(reranked_chunks),
                        'follow_up_questions': [
                            "您想了解推荐产品的申请条件吗？",
                            "需要我详细介绍某个产品吗？",
                            "您还有其他产品需求吗？"
                        ],
                        'processing_info': {
                            'strategy': 'personalized_recommendation',
                            'recommendations_count': len(recommendations)
                        }
                    }
                else:
                    # 通用产品介绍
                    if products:
                        response = self.template_manager.format_product_response(
                            products[0], intent
                        )
                    else:
                        response = "根据您的查询，我正在为您查找相关产品信息..."
                    
                    return {
                        'query': query_info['original_query'],
                        'intent': '产品咨询',
                        'response': response,
                        'confidence': 0.8,
                        'products': products,
                        'sources': self._format_sources(reranked_chunks),
                        'follow_up_questions': [
                            "您想了解申请条件吗？",
                            "需要我为您推荐合适的产品吗？",
                            "您想对比不同产品吗？"
                        ],
                        'processing_info': {
                            'strategy': 'general_product_info',
                            'products_found': len(products)
                        }
                    }
                    
        except Exception as e:
            print(f"产品咨询处理失败: {e}")
            return self._generate_error_response(query_info['original_query'], f"产品咨询异常: {e}")
    
    def _handle_qualification_assessment(self, query_info: Dict[str, Any], 
                                       customer_profile: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """处理资质评估查询"""
        try:
            print("使用资质评估策略处理查询")
            
            if not customer_profile:
                entities = query_info.get('entities', {})
                customer_profile = self._extract_customer_profile_from_entities(entities)
            
            if customer_profile:
                # 财务健康分析
                health_analysis = self.financial_analyzer.analyze_financial_health(customer_profile)
                
                # 贷款能力分析
                application_info = self._extract_application_info_from_entities(
                    query_info.get('entities', {})
                )
                
                if application_info.get('amount'):
                    loan_capacity = self.financial_analyzer.analyze_loan_capacity(
                        customer_profile, application_info
                    )
                else:
                    loan_capacity = {"message": "需要提供申请金额以进行详细分析"}
                
                # 搜索相似案例
                similar_cases = self.retriever.search_similar_cases(customer_profile, top_k=5)
                
                # 客户分析
                customer_analysis = self.product_strategy.analyze_customer(customer_profile)
                
                # 产品推荐
                recommended_products = ""
                if application_info:
                    recommendations = self.product_strategy.recommend_products(
                        customer_profile, application_info, []
                    )
                    recommended_products = '\n'.join(f"• {r['product']}" for r in recommendations[:3])
                
                # 使用策略模板
                template_context = {
                    'customer_segment': customer_analysis.get('customer_segment', 'N/A'),
                    'qualification_score': health_analysis.get('health_score', 0),
                    'approval_probability': f"{loan_capacity.get('approval_probability', 0):.0%}",
                    'strengths': '\n'.join(f"• {s}" for s in health_analysis.get('suggestions', [])[:3]),
                    'improvement_areas': '\n'.join(f"• {f}" for f in customer_analysis.get('risk_profile', {}).get('key_risk_factors', [])[:3]),
                    'recommended_products': recommended_products,
                    'assessment_disclaimer': "\n提示：评估结果仅供参考，实际审批以金融机构为准。"
                }
                
                response = self.template_manager.get_strategy_template('qualification_assessment', template_context)
                
                return {
                    'query': query_info['original_query'],
                    'intent': '资质评估',
                    'response': response,
                    'confidence': 0.9,
                    'health_analysis': health_analysis,
                    'loan_capacity': loan_capacity,
                    'customer_analysis': customer_analysis,
                    'similar_cases': similar_cases,
                    'sources': self._format_sources(similar_cases),
                    'follow_up_questions': [
                        "您想了解如何提升资质吗？",
                        "需要我推荐适合的产品吗？",
                        "您想看看相似案例的详细情况吗？"
                    ],
                    'processing_info': {
                        'strategy': 'qualification_assessment',
                        'health_score': health_analysis.get('health_score', 0)
                    }
                }
            else:
                return {
                    'query': query_info['original_query'],
                    'intent': '资质评估',
                    'response': "为了准确评估您的资质，请提供：年龄、月收入、征信分数、工作年限、现有负债等信息。",
                    'confidence': 0.7,
                    'sources': [],
                    'follow_up_questions': [
                        "请告诉我您的基本信息",
                        "您想了解评估需要哪些资料吗？",
                        "需要我提供资质提升建议吗？"
                    ],
                    'processing_info': {'strategy': 'qualification_assessment', 'status': 'need_info'}
                }
                
        except Exception as e:
            print(f"资质评估处理失败: {e}")
            return self._generate_error_response(query_info['original_query'], f"资质评估异常: {e}")
    
    def _handle_compliance_consultation(self, query_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理合规咨询查询"""
        try:
            print("使用合规策略处理查询")
            
            query = query_info['original_query']
            
            # 合规检查
            compliance_result = self.compliance_strategy.check_compliance(query)
            
            # 如果查询本身存在合规问题，进行增强
            if not compliance_result.get("is_compliant", True):
                enhanced_query = self.compliance_strategy.enhance_compliance(query)
                print(f"查询已优化为合规版本: {enhanced_query}")
            
            # 检索政策相关信息
            retrieval_queries = self.query_rewriter.rewrite_for_retrieval(query_info)
            all_chunks = []
            
            for r_query in retrieval_queries[:2]:
                chunks = self.retriever.search_with_intent(
                    r_query, "合规咨询", content_types=['policy_paragraph'], top_k=8
                )
                all_chunks.extend(chunks)
            
            unique_chunks = self._deduplicate_chunks(all_chunks)
            
            # 生成合规回复
            if unique_chunks:
                policy_info = self._extract_policy_info_from_chunks(unique_chunks)
                response = self.template_manager.format_policy_response(policy_info)
            else:
                response = "关于您的合规咨询，建议您查阅最新的监管政策文件或咨询专业合规人员。"
            
            # 确保回复合规
            compliant_response = self.compliance_strategy.enhance_compliance(response)
            
            # 生成合规报告
            compliance_report = self.compliance_strategy.generate_compliance_report(
                query_info['original_query']
            )
            
            return {
                'query': query_info['original_query'],
                'intent': '合规咨询',
                'response': compliant_response,
                'confidence': 0.85,
                'compliance_result': compliance_result,
                'compliance_report': compliance_report,
                'sources': self._format_sources(unique_chunks),
                'follow_up_questions': [
                    "您还有其他合规方面的问题吗？",
                    "需要我为您详细解读相关政策吗？",
                    "您想了解合规操作的具体要求吗？"
                ],
                'processing_info': {
                    'strategy': 'compliance_consultation',
                    'compliance_score': compliance_result.get('compliance_score', 0)
                }
            }
            
        except Exception as e:
            print(f"合规咨询处理失败: {e}")
            return self._generate_error_response(query_info['original_query'], f"合规咨询异常: {e}")
    
    def _handle_standard_query(self, query_info: Dict[str, Any], 
                              customer_profile: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """处理标准查询（利率、条件、流程等）"""
        try:
            print("使用标准策略处理查询")
            
            intent = query_info['intent']
            
            # 检索相关信息
            retrieval_queries = self.query_rewriter.rewrite_for_retrieval(query_info)
            all_chunks = []
            
            for r_query in retrieval_queries[:3]:
                chunks = self.retriever.search_with_intent(r_query, intent, top_k=10)
                all_chunks.extend(chunks)
            
            unique_chunks = self._deduplicate_chunks(all_chunks)
            
            if customer_profile:
                reranked_chunks = self.reranker.rerank_financial_results(
                    query_info['original_query'], unique_chunks, customer_profile, top_k=5
                )
            else:
                reranked_chunks = self.reranker.rerank_by_intent(
                    query_info['original_query'], intent, unique_chunks, top_k=5
                )
            
            # 使用响应生成器生成回复
            response_result = self.response_generator.generate_response(
                query_info, reranked_chunks, self.conversation_history
            )
            
            return {
                'query': query_info['original_query'],
                'intent': intent,
                'entities': query_info.get('entities', {}),
                'response': response_result['response'],
                'confidence': response_result['confidence'],
                'sources': self._format_sources(reranked_chunks),
                'follow_up_questions': response_result.get('follow_up_questions', []),
                'processing_info': {
                    'strategy': 'standard_retrieval',
                    'chunks_found': len(unique_chunks),
                    'chunks_used': len(reranked_chunks)
                }
            }
            
        except Exception as e:
            print(f"标准查询处理失败: {e}")
            return self._generate_error_response(query_info['original_query'], f"标准查询异常: {e}")
    
    def _handle_general_consultation(self, query_info: Dict[str, Any], 
                                   customer_profile: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """处理一般咨询"""
        try:
            print("使用通用策略处理查询")
            
            # 检索相关信息
            retrieval_queries = self.query_rewriter.rewrite_for_retrieval(query_info)
            all_chunks = []
            
            for r_query in retrieval_queries[:2]:
                chunks = self.retriever.search(r_query, top_k=8)
                all_chunks.extend(chunks)
            
            unique_chunks = self._deduplicate_chunks(all_chunks)
            
            # 使用LLM直接生成回复
            llm_response = self.response_generator.generate_with_llm(
                query_info, unique_chunks, self.conversation_history
            )
            
            return {
                'query': query_info['original_query'],
                'intent': '一般咨询',
                'response': llm_response,
                'confidence': 0.7,
                'sources': self._format_sources(unique_chunks),
                'follow_up_questions': [
                    "您还有其他问题吗？",
                    "需要我为您详细解释吗？",
                    "您想了解相关产品吗？"
                ],
                'processing_info': {
                    'strategy': 'general_llm',
                    'chunks_found': len(unique_chunks)
                }
            }
            
        except Exception as e:
            print(f"一般咨询处理失败: {e}")
            return self._generate_error_response(query_info['original_query'], f"一般咨询异常: {e}")
    
    def _apply_compliance_check(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """对回复结果进行合规检查"""
        try:
            response = result.get('response', '')
            
            # 合规检查
            compliance_check = self.compliance_strategy.check_compliance(response)
            
            if not compliance_check.get('is_compliant', True):
                # 如果不合规，使用合规增强后的内容
                enhanced_response = self.compliance_strategy.enhance_compliance(response)
                result['response'] = enhanced_response
                result['compliance_enhanced'] = True
                result['original_response'] = response
            
            result['compliance_info'] = {
                'is_compliant': compliance_check.get('is_compliant', True),
                'compliance_score': compliance_check.get('compliance_score', 100),
                'violations': compliance_check.get('violations', []),
                'suggestions': compliance_check.get('suggestions', [])
            }
            
            return result
            
        except Exception as e:
            print(f"合规检查失败: {e}")
            return result
    
    def _extract_customer_profile_from_entities(self, entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """从实体中提取客户画像 - 修复类型问题"""
        profile = {}
        
        try:
            if '年龄' in entities and entities['年龄']:
                age_str = entities['年龄'][0]
                age_num = self._safe_extract_number(age_str)
                if age_num > 0:
                    profile['age'] = int(age_num)
            
            if '收入' in entities and entities['收入']:
                income_str = entities['收入'][0]
                income_num = self._safe_extract_number(income_str)
                if income_num > 0:
                    profile['monthly_income'] = income_num
            
            if '征信分数' in entities and entities['征信分数']:
                credit_str = entities['征信分数'][0]
                credit_num = self._safe_extract_number(credit_str)
                if credit_num > 0:
                    profile['credit_score'] = int(credit_num)
            
        except Exception as e:
            print(f"提取客户画像失败: {e}")
        
        return profile if profile else None
    
    def _extract_application_info_from_entities(self, entities: Dict[str, List[str]]) -> Dict[str, Any]:
        """从实体中提取申请信息"""
        application = {}
        
        try:
            if '金额' in entities and entities['金额']:
                amount_str = entities['金额'][0]
                amount = self._safe_extract_number(amount_str)
                if amount > 0:
                    application['amount'] = amount
            
            if '期限' in entities and entities['期限']:
                term_str = entities['期限'][0]
                if '年' in term_str:
                    months = int(self._safe_extract_number(term_str)) * 12
                elif '月' in term_str:
                    months = int(self._safe_extract_number(term_str))
                else:
                    months = 36  # 默认3年
                application['term_months'] = months
            
        except Exception as e:
            print(f"提取申请信息失败: {e}")
        
        return application
    
    def _extract_products_from_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """从检索块中提取产品信息"""
        products = []
        seen_products = set()
        
        for chunk in chunks:
            metadata = chunk.get('metadata', {})
            product_name = metadata.get('name', metadata.get('product_id', ''))
            
            if product_name and product_name not in seen_products:
                products.append({
                    'name': product_name,
                    'type': metadata.get('type', ''),
                    'institution': metadata.get('institution', ''),
                    'interest_rate': metadata.get('interest_rate', {}),
                    'features': metadata.get('features', {}),
                    'requirements': metadata.get('requirements', {}),
                    'content': chunk.get('content', '')
                })
                seen_products.add(product_name)
        
        return products
    
    def _extract_policy_info_from_chunks(self, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """从检索块中提取政策信息"""
        policy_info = {
            'title': '相关政策规定',
            'content': [],
            'issuer': '监管机构'
        }
        
        for chunk in chunks:
            content = chunk.get('content', '')
            if content:
                policy_info['content'].append(content)
        
        return policy_info
    
    def _format_sources(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """格式化信息来源"""
        sources = []
        for chunk in chunks:
            sources.append({
                'chunk_id': chunk.get('chunk_id'),
                'content': chunk.get('content', '')[:100] + "...",
                'score': chunk.get('rerank_score', chunk.get('final_score', 0)),
                'type': chunk.get('content_type')
            })
        return sources
    
    def _deduplicate_chunks(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重文档块"""
        seen_ids = set()
        unique_chunks = []
        
        for chunk in chunks:
            chunk_id = chunk.get('chunk_id')
            if chunk_id and chunk_id not in seen_ids:
                seen_ids.add(chunk_id)
                unique_chunks.append(chunk)
        
        return unique_chunks
    
    def _update_conversation_history(self, user_query: str, assistant_response: str):
        """更新对话历史"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        self.conversation_history.append({
            "role": "user",
            "content": user_query,
            "timestamp": timestamp
        })
        self.conversation_history.append({
            "role": "assistant", 
            "content": assistant_response,
            "timestamp": timestamp
        })
        
        # 保持历史长度限制
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
    
    def _generate_error_response(self, query: str, error_msg: str) -> Dict[str, Any]:
        """生成错误响应"""
        return {
            'query': query,
            'intent': '系统异常',
            'response': "抱歉，我暂时无法处理您的问题。请稍后再试或联系人工客服。",
            'confidence': 0.0,
            'sources': [],
            'follow_up_questions': ["需要我为您转接人工客服吗？"],
            'processing_info': {'error': error_msg, 'strategy': 'error_handling'}
        }
    
    def chat(self, user_input: str, customer_profile: Optional[Dict[str, Any]] = None) -> str:
        """简化的对话接口"""
        result = self.query(user_input, customer_profile)
        return result['response']

def main():
    """主函数 - 演示各种策略的使用"""
    print("=== 金融RAG问答系统演示 ===")
    
    # 初始化系统
    rag_system = FinancialRAGSystem()
    
    # 测试各种意图的查询
    # test_scenarios = [
    #     # 产品咨询
    #     {
    #         "query": "个人消费贷款利率是多少？",
    #         "customer_profile": None,
    #         "description": "产品咨询测试"
    #     },
        
    #     # 资质评估
    #     {
    #         "query": "我月收入8000元，能申请多少额度？",
    #         "customer_profile": {
    #             "age": 30,
    #             "monthly_income": 8000,
    #             "credit_score": 720,
    #             "employment_years": 3
    #         },
    #         "description": "资质评估测试"
    #     },
        
    #     # 风险评估
    #     {
    #         "query": "我想评估一下贷款风险",
    #         "customer_profile": {
    #             "age": 35,
    #             "monthly_income": 15000,
    #             "credit_score": 650,
    #             "debt_to_income": 0.4,
    #             "employment_years": 5
    #         },
    #         "description": "风险评估测试"
    #     },
        
    #     # 产品对比
    #     {
    #         "query": "工商银行和建设银行的消费贷哪个好？",
    #         "customer_profile": None,
    #         "description": "产品对比测试"
    #     },
        
    #     # 合规咨询
    #     {
    #         "query": "银行贷款有什么法律法规要求？",
    #         "customer_profile": None,
    #         "description": "合规咨询测试"
    #     },
        
    #     # 服务冲突
    #     {
    #         "query": "你们的回答总是不准确，我要投诉！",
    #         "customer_profile": None,
    #         "description": "服务冲突测试"
    #     }
    # ]
    
    # for i, scenario in enumerate(test_scenarios, 1):
    #     print(f"\n{'='*60}")
    #     print(f"测试场景 {i}: {scenario['description']}")
    #     print(f"查询: {scenario['query']}")
    #     if scenario['customer_profile']:
    #         print(f"客户信息: {scenario['customer_profile']}")
        
    #     print("-" * 60)
        
    #     # 执行查询
    #     result = rag_system.query(scenario['query'], scenario['customer_profile'])
        
    #     print(f"识别意图: {result['intent']}")
    #     print(f"置信度: {result['confidence']:.3f}")
    #     print(f"处理策略: {result.get('processing_info', {}).get('strategy', 'unknown')}")
    #     print(f"回复: {result['response']}")
        
    #     if result.get('follow_up_questions'):
    #         print(f"建议追问: {result['follow_up_questions'][:2]}")
        
    #     if result.get('compliance_info'):
    #         compliance = result['compliance_info']
    #         print(f"合规检查: {'通过' if compliance['is_compliant'] else '需要改进'}")
        
    #     print("-" * 60)
    
    # 演示对话历史功能
    print(f"\n{'='*60}")
    print("对话历史测试")
    print("-" * 60)
    
    # 连续对话
    queries = [
        "我想了解个人消费贷",
        "利率多少？",
        "我月收入1万能申请多少？"
    ]
    
    customer = {
        "age": 28,
        "monthly_income": 10000,
        "credit_score": 730,
        "employment_years": 4
    }
    
    for query in queries:
        print(f"\n用户: {query}")
        response = rag_system.chat(query, customer)
        print(f"助手: {response}")
    
    print(f"\n对话轮次: {len(rag_system.conversation_history)//2}")

if __name__ == "__main__":
    main()