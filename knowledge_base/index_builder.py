# knowledge_base/index_builder.py
import os
import json
import sqlite3
import pickle
from typing import Dict, List, Any, Optional
import numpy as np
from collections import defaultdict
import faiss

class IndexBuilder:
    """索引构建器"""
    
    def __init__(self, index_dir: str = "./data/knowledge_base/index"):
        self.index_dir = index_dir
        os.makedirs(index_dir, exist_ok=True)
        
        # 索引文件路径
        self.vector_index_path = os.path.join(index_dir, "vector_index.faiss")
        self.keyword_index_path = os.path.join(index_dir, "keyword_index.pkl")
        self.attribute_index_path = os.path.join(index_dir, "attribute_index.pkl")
        self.category_index_path = os.path.join(index_dir, "category_index.pkl")
    
    def build_vector_index(self, db_path: str) -> bool:
        """构建向量索引"""
        try:
            print("开始构建向量索引...")
            
            # 从数据库读取向量数据
            embeddings, chunk_ids = self._load_embeddings(db_path)
            
            if len(embeddings) == 0:
                print("没有找到向量数据")
                return False
            
            # 转换为numpy数组
            embedding_matrix = np.array(embeddings, dtype=np.float32)
            
            # 创建FAISS索引
            dimension = embedding_matrix.shape[1]
            index = faiss.IndexFlatIP(dimension)  # 使用内积相似度
            
            # 归一化向量
            faiss.normalize_L2(embedding_matrix)
            
            # 添加向量到索引
            index.add(embedding_matrix)
            
            # 保存索引
            faiss.write_index(index, self.vector_index_path)
            
            # 保存chunk_id映射
            mapping_path = os.path.join(self.index_dir, "chunk_mapping.pkl")
            with open(mapping_path, 'wb') as f:
                pickle.dump(chunk_ids, f)
            
            print(f"向量索引构建完成，包含 {len(embeddings)} 个向量")
            return True
            
        except Exception as e:
            print(f"构建向量索引失败: {e}")
            return False
    
    def build_keyword_index(self, db_path: str) -> bool:
        """构建关键词倒排索引"""
        try:
            print("开始构建关键词索引...")
            
            keyword_index = defaultdict(set)
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT chunk_id, content, keywords FROM chunks")
                
                for row in cursor.fetchall():
                    chunk_id, content, keywords_json = row
                    
                    # 处理关键词
                    try:
                        keywords = json.loads(keywords_json) if keywords_json else []
                    except:
                        keywords = []
                    
                    # 添加关键词到索引
                    for keyword in keywords:
                        keyword_index[keyword.lower()].add(chunk_id)
                    
                    # 添加内容中的词语到索引
                    words = self._extract_words(content)
                    for word in words:
                        keyword_index[word.lower()].add(chunk_id)
            
            # 转换为字典格式保存
            keyword_index_dict = {k: list(v) for k, v in keyword_index.items()}
            
            with open(self.keyword_index_path, 'wb') as f:
                pickle.dump(keyword_index_dict, f)
            
            print(f"关键词索引构建完成，包含 {len(keyword_index_dict)} 个词条")
            return True
            
        except Exception as e:
            print(f"构建关键词索引失败: {e}")
            return False
    
    def build_attribute_index(self, db_path: str) -> bool:
        """构建属性索引"""
        try:
            print("开始构建属性索引...")
            
            attribute_index = {
                'product_type': defaultdict(list),
                'institution': defaultdict(list),
                'content_type': defaultdict(list),
                'risk_level': defaultdict(list),
                'interest_rate': defaultdict(list),
                'consultation_type': defaultdict(list),
                'problem_type': defaultdict(list)
            }
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT chunk_id, content_type, metadata FROM chunks")
                
                for row in cursor.fetchall():
                    chunk_id, content_type, metadata_json = row
                    
                    # 添加内容类型索引
                    attribute_index['content_type'][content_type].append(chunk_id)
                    
                    # 处理元数据
                    try:
                        metadata = json.loads(metadata_json) if metadata_json else {}
                    except:
                        metadata = {}
                    
                    # 产品类型索引
                    if 'product_type' in metadata:
                        attribute_index['product_type'][metadata['product_type']].append(chunk_id)
                    
                    # 机构索引
                    if 'institution' in metadata:
                        attribute_index['institution'][metadata['institution']].append(chunk_id)
                    
                    # 风险等级索引
                    if 'risk_level' in metadata:
                        attribute_index['risk_level'][metadata['risk_level']].append(chunk_id)
                    
                    # 咨询类型索引
                    if 'consultation_type' in metadata:
                        attribute_index['consultation_type'][metadata['consultation_type']].append(chunk_id)
                    
                    # 问题类型索引
                    if 'problem_types' in metadata:
                        problem_types = metadata['problem_types']
                        if isinstance(problem_types, list):
                            for problem_type in problem_types:
                                attribute_index['problem_type'][problem_type].append(chunk_id)
                    
                    # 利率范围索引
                    if 'features' in metadata and isinstance(metadata['features'], dict):
                        features = metadata['features']
                        if 'interest_rate' in features:
                            rate_info = features['interest_rate']
                            if isinstance(rate_info, dict):
                                min_rate = rate_info.get('min', 0)
                                max_rate = rate_info.get('max', 0)
                                rate_range = f"{min_rate}-{max_rate}"
                                attribute_index['interest_rate'][rate_range].append(chunk_id)
            
            # 转换为普通字典保存
            attribute_index_dict = {}
            for attr_type, attr_values in attribute_index.items():
                attribute_index_dict[attr_type] = dict(attr_values)
            
            with open(self.attribute_index_path, 'wb') as f:
                pickle.dump(attribute_index_dict, f)
            
            print("属性索引构建完成")
            return True
            
        except Exception as e:
            print(f"构建属性索引失败: {e}")
            return False
    
    def build_category_index(self, db_path: str) -> bool:
        """构建分类索引"""
        try:
            print("开始构建分类索引...")
            
            category_index = defaultdict(list)
            
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT chunk_id, category_path FROM chunks")
                
                for row in cursor.fetchall():
                    chunk_id, category_path = row
                    
                    if category_path:
                        # 添加完整路径
                        category_index[category_path].append(chunk_id)
                        
                        # 添加父级路径
                        path_parts = category_path.split('/')
                        for i in range(1, len(path_parts)):
                            parent_path = '/'.join(path_parts[:i+1])
                            category_index[parent_path].append(chunk_id)
            
            # 转换为字典格式保存
            category_index_dict = dict(category_index)
            
            with open(self.category_index_path, 'wb') as f:
                pickle.dump(category_index_dict, f)
            
            print(f"分类索引构建完成，包含 {len(category_index_dict)} 个分类")
            return True
            
        except Exception as e:
            print(f"构建分类索引失败: {e}")
            return False
    
    def _load_embeddings(self, db_path: str) -> tuple:
        """从数据库加载向量数据"""
        embeddings = []
        chunk_ids = []
        
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT chunk_id, embedding FROM chunks WHERE embedding IS NOT NULL")
            
            for row in cursor.fetchall():
                chunk_id, embedding_blob = row
                
                try:
                    # 解析向量数据
                    embedding_str = embedding_blob.decode('utf-8')
                    embedding = json.loads(embedding_str)
                    
                    if isinstance(embedding, list) and len(embedding) > 0:
                        embeddings.append(embedding)
                        chunk_ids.append(chunk_id)
                        
                except Exception as e:
                    print(f"解析向量失败 {chunk_id}: {e}")
                    continue
        
        return embeddings, chunk_ids
    
    def _extract_words(self, text: str) -> List[str]:
        """从文本中提取词语"""
        import re
        
        # 简单的中文分词
        # 提取中文词语和数字
        words = re.findall(r'[\u4e00-\u9fff]+|\d+', text)
        
        # 过滤短词和常见停用词
        stopwords = {'的', '了', '在', '是', '有', '和', '与', '或', '等', '及'}
        words = [word for word in words if len(word) > 1 and word not in stopwords]
        
        return words
    
    def load_vector_index(self) -> Optional[faiss.Index]:
        """加载向量索引"""
        try:
            if os.path.exists(self.vector_index_path):
                return faiss.read_index(self.vector_index_path)
            return None
        except Exception as e:
            print(f"加载向量索引失败: {e}")
            return None
    
    def load_chunk_mapping(self) -> Optional[List[str]]:
        """加载chunk映射"""
        try:
            mapping_path = os.path.join(self.index_dir, "chunk_mapping.pkl")
            if os.path.exists(mapping_path):
                with open(mapping_path, 'rb') as f:
                    return pickle.load(f)
            return None
        except Exception as e:
            print(f"加载chunk映射失败: {e}")
            return None
    
    def load_keyword_index(self) -> Optional[Dict[str, List[str]]]:
        """加载关键词索引"""
        try:
            if os.path.exists(self.keyword_index_path):
                with open(self.keyword_index_path, 'rb') as f:
                    return pickle.load(f)
            return None
        except Exception as e:
            print(f"加载关键词索引失败: {e}")
            return None
    
    def load_attribute_index(self) -> Optional[Dict[str, Dict[str, List[str]]]]:
        """加载属性索引"""
        try:
            if os.path.exists(self.attribute_index_path):
                with open(self.attribute_index_path, 'rb') as f:
                    return pickle.load(f)
            return None
        except Exception as e:
            print(f"加载属性索引失败: {e}")
            return None
    
    def load_category_index(self) -> Optional[Dict[str, List[str]]]:
        """加载分类索引"""
        try:
            if os.path.exists(self.category_index_path):
                with open(self.category_index_path, 'rb') as f:
                    return pickle.load(f)
            return None
        except Exception as e:
            print(f"加载分类索引失败: {e}")
            return None

if __name__ == "__main__":
    # 测试索引构建器
    builder = IndexBuilder()
    
    # 使用新的数据库路径
    test_db_path = "./data/knowledge_base/knowledge.db"
    
    if os.path.exists(test_db_path):
        # 构建各种索引
        print("构建向量索引...")
        success = builder.build_vector_index(test_db_path)
        print(f"向量索引构建: {'成功' if success else '失败'}")
        
        print("构建关键词索引...")
        success = builder.build_keyword_index(test_db_path)
        print(f"关键词索引构建: {'成功' if success else '失败'}")
        
        print("构建属性索引...")
        success = builder.build_attribute_index(test_db_path)
        print(f"属性索引构建: {'成功' if success else '失败'}")
        
        print("构建分类索引...")
        success = builder.build_category_index(test_db_path)
        print(f"分类索引构建: {'成功' if success else '失败'}")
        
        # 测试加载索引
        print("\n测试加载索引...")
        vector_index = builder.load_vector_index()
        print(f"向量索引: {'加载成功' if vector_index else '加载失败'}")
        
        keyword_index = builder.load_keyword_index()
        print(f"关键词索引: {'加载成功' if keyword_index else '加载失败'}")
        if keyword_index:
            print(f"关键词数量: {len(keyword_index)}")
    else:
        print(f"数据库不存在: {test_db_path}")