# knowledge_base/knowledge_base_manager.py
import os
import json
import sqlite3
from typing import Dict, List, Any, Optional
try:
    from .chunk_splitter import ChunkSplitter
    from .index_builder import IndexBuilder
    from .product_processor import ProductProcessor
    from .case_processor import CaseProcessor
    from .policy_processor import PolicyProcessor
    from .risk_processor import RiskProcessor
    from .dialogue_processor import DialogueProcessor
except Exception as e:
    from chunk_splitter import ChunkSplitter
    from index_builder import IndexBuilder
    from product_processor import ProductProcessor
    from case_processor import CaseProcessor
    from policy_processor import PolicyProcessor
    from risk_processor import RiskProcessor
    from dialogue_processor import DialogueProcessor
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from llm_api import call_embedding_api

class KnowledgeBaseManager:
    """知识库管理器"""
    
    def __init__(self, db_path: str = "./data/knowledge_base/knowledge.db"):
        self.db_path = db_path
        self.chunk_splitter = ChunkSplitter()
        self.index_builder = IndexBuilder("./data/knowledge_base/index")
        self.product_processor = ProductProcessor()
        self.case_processor = CaseProcessor()
        self.policy_processor = PolicyProcessor()
        self.risk_processor = RiskProcessor()
        self.dialogue_processor = DialogueProcessor()
        
        # 初始化数据库
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建文档表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS documents (
                    id INTEGER PRIMARY KEY,
                    doc_id TEXT UNIQUE,
                    doc_type TEXT,
                    title TEXT,
                    content TEXT,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建块表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS chunks (
                    id INTEGER PRIMARY KEY,
                    chunk_id TEXT UNIQUE,
                    doc_id TEXT,
                    content TEXT,
                    content_type TEXT,
                    embedding BLOB,
                    keywords TEXT,
                    category_path TEXT,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (doc_id) REFERENCES documents(doc_id)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_doc_type ON documents(doc_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_type ON chunks(content_type)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_category ON chunks(category_path)')
            
            conn.commit()
    
    def load_and_process_synthesis_data(self, synthesis_dir: str = "./data/synthesis"):
        """加载并处理合成数据目录下的所有数据"""
        print("开始加载和处理合成数据...")
        
        # 处理产品数据
        product_files = [
            "products_samples.json"
        ]
        
        for filename in product_files:
            file_path = os.path.join(synthesis_dir, filename)
            if os.path.exists(file_path):
                print(f"处理产品文件: {filename}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    products = json.load(f)
                self.add_product_data(products)
        
        # 处理案例数据
        case_files = [
            "cases_samples.json",
            "risk_cases_samples.json"
        ]
        
        for filename in case_files:
            file_path = os.path.join(synthesis_dir, filename)
            if os.path.exists(file_path):
                print(f"处理案例文件: {filename}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    cases = json.load(f)
                if filename == "risk_cases_samples.json":
                    self.add_risk_case_data(cases)
                else:
                    self.add_case_data(cases)
        
        # 处理政策数据
        policy_files = [
            "regulatory_policies_samples.json",
            "policy_samples.json"
        ]
        
        for filename in policy_files:
            file_path = os.path.join(synthesis_dir, filename)
            if os.path.exists(file_path):
                print(f"处理政策文件: {filename}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    policies = json.load(f)
                self.add_policy_data(policies)
        
        # 处理对话数据
        dialogue_files = [
            "loan_dialogue_synthesis_samples_multi_turn.json",
            "loan_dialogue_synthesis_samples_consultation.json"
        ]
        
        for filename in dialogue_files:
            file_path = os.path.join(synthesis_dir, filename)
            if os.path.exists(file_path):
                print(f"处理对话文件: {filename}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    dialogues = json.load(f)
                self.add_dialogue_data(dialogues)
        
        # 处理客户档案数据
        profile_files = [
            "profile_samples.json"
        ]
        
        for filename in profile_files:
            file_path = os.path.join(synthesis_dir, filename)
            if os.path.exists(file_path):
                print(f"处理客户档案文件: {filename}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    profiles = json.load(f)
                self.add_profile_data(profiles)
        
        print("所有合成数据处理完成！")
    
    def add_risk_case_data(self, risk_cases: List[Dict[str, Any]]) -> int:
        """添加风险案例数据"""
        processed_count = 0
        
        # 使用风险处理器处理数据
        chunks = self.risk_processor.process_risk_cases(risk_cases)
        
        with sqlite3.connect(self.db_path) as conn:
            for chunk in chunks:
                try:
                    # 提取文档ID
                    doc_id = chunk.get('metadata', {}).get('case_id', f"RISK_CASE_{processed_count}")
                    
                    # 保存文档（如果是完整案例块）
                    if chunk.get('content_type') == 'risk_case_full':
                        self._save_document(
                            conn, doc_id, "risk_case",
                            f"风险案例 {doc_id}",
                            chunk['content'],
                            chunk.get('metadata', {})
                        )
                    
                    # 保存块
                    self._save_chunk(conn, chunk, doc_id)
                    
                    if chunk.get('content_type') == 'risk_case_full':
                        processed_count += 1
                        
                except Exception as e:
                    print(f"处理风险案例块失败 {chunk.get('chunk_id', 'unknown')}: {e}")
        
        print(f"已添加 {processed_count} 个风险案例到知识库")
        return processed_count
    
    def add_dialogue_data(self, dialogues: List[Dict[str, Any]]) -> int:
        """添加对话数据"""
        processed_count = 0
        
        # 使用对话处理器处理数据
        chunks = self.dialogue_processor.process_dialogue_data(dialogues)
        
        with sqlite3.connect(self.db_path) as conn:
            for chunk in chunks:
                try:
                    # 提取文档ID
                    doc_id = chunk.get('metadata', {}).get('dialog_id', f"DIALOG_{processed_count}")
                    
                    # 保存文档（如果是完整对话块）
                    if chunk.get('content_type') == 'dialog_full':
                        self._save_document(
                            conn, doc_id, "dialogue",
                            f"客服对话 {doc_id}",
                            chunk['content'],
                            chunk.get('metadata', {})
                        )
                    
                    # 保存块
                    self._save_chunk(conn, chunk, doc_id)
                    
                    if chunk.get('content_type') == 'dialog_full':
                        processed_count += 1
                        
                except Exception as e:
                    print(f"处理对话块失败 {chunk.get('chunk_id', 'unknown')}: {e}")
        
        print(f"已添加 {processed_count} 个对话到知识库")
        return processed_count
    
    def add_profile_data(self, profiles: List[Dict[str, Any]]) -> int:
        """添加客户档案数据"""
        processed_count = 0
        
        with sqlite3.connect(self.db_path) as conn:
            for profile in profiles:
                try:
                    # 处理客户档案数据
                    processed_profile = self._process_profile_data(profile)
                    
                    # 保存文档
                    doc_id = profile.get('profile_id', f"PROFILE_{processed_count}")
                    self._save_document(
                        conn, doc_id, "profile",
                        processed_profile['title'],
                        processed_profile['content'],
                        processed_profile['metadata']
                    )
                    
                    # 切分文档
                    chunks = self._split_profile_data(processed_profile)
                    
                    # 保存块
                    for chunk in chunks:
                        self._save_chunk(conn, chunk, doc_id)
                    
                    processed_count += 1
                    
                except Exception as e:
                    print(f"处理客户档案失败 {profile.get('profile_id', 'unknown')}: {e}")
        
        print(f"已添加 {processed_count} 个客户档案到知识库")
        return processed_count
    
    def _process_profile_data(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """处理客户档案数据"""
        profile_id = profile.get('profile_id', '')
        basic_info = profile.get('basic_info', {})
        income_info = profile.get('income_info', {})
        
        # 生成标题
        age = basic_info.get('age', '')
        occupation = basic_info.get('occupation', '')
        city = basic_info.get('city', '')
        title = f"客户档案{profile_id}：{age}岁{occupation}（{city}）"
        
        # 生成内容
        content_parts = []
        content_parts.append(f"基本信息：{age}岁{occupation}，{basic_info.get('education', '')}学历，{basic_info.get('marital_status', '')}，居住在{city}")
        content_parts.append(f"收入情况：月收入{income_info.get('monthly_income', '')}，年收入{income_info.get('annual_income', '')}，收入稳定性{income_info.get('income_stability', '')}")
        
        assets = profile.get('assets', {})
        if assets:
            content_parts.append(f"资产状况：总资产{assets.get('total_assets', '')}，流动资产{assets.get('liquid_assets', '')}，房产价值{assets.get('real_estate_value', '')}")
        
        credit_info = profile.get('credit_info', {})
        if credit_info:
            content_parts.append(f"信用情况：征信评分{credit_info.get('credit_score', '')}，信用等级{credit_info.get('credit_level', '')}，逾期记录{credit_info.get('overdue_records', '')}次")
        
        risk_assessment = profile.get('risk_assessment', {})
        if risk_assessment:
            content_parts.append(f"风险评估：{risk_assessment.get('risk_level', '')}，评分{risk_assessment.get('risk_score', '')}分")
        
        content = "。".join(content_parts)
        
        return {
            'title': title,
            'content': content,
            'metadata': profile
        }
    
    def _split_profile_data(self, profile_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """切分客户档案数据"""
        chunks = []
        profile_id = profile_data.get('metadata', {}).get('profile_id', '')
        
        # 基本信息块
        basic_chunk = {
            'chunk_id': f"{profile_id}_BASIC",
            'content': f"客户基本信息：{profile_data['content'][:200]}",
            'content_type': 'profile_basic',
            'keywords': self._extract_profile_keywords(profile_data),
            'category_path': f"客户档案/基本信息",
            'metadata': profile_data.get('metadata', {})
        }
        chunks.append(basic_chunk)
        
        # 风险评估块
        metadata = profile_data.get('metadata', {})
        risk_assessment = metadata.get('risk_assessment', {})
        if risk_assessment:
            risk_chunk = {
                'chunk_id': f"{profile_id}_RISK",
                'content': f"风险评估：{risk_assessment.get('risk_level', '')}风险，评分{risk_assessment.get('risk_score', '')}分，建议：{risk_assessment.get('recommendation', '')}",
                'content_type': 'profile_risk',
                'keywords': [risk_assessment.get('risk_level', ''), '风险评估'],
                'category_path': f"客户档案/风险评估",
                'metadata': {'profile_id': profile_id, 'risk_assessment': risk_assessment}
            }
            chunks.append(risk_chunk)
        
        return chunks
    
    def _extract_profile_keywords(self, profile_data: Dict[str, Any]) -> List[str]:
        """提取客户档案关键词"""
        keywords = []
        metadata = profile_data.get('metadata', {})
        
        basic_info = metadata.get('basic_info', {})
        keywords.extend([
            basic_info.get('occupation', ''),
            basic_info.get('city', ''),
            basic_info.get('education', ''),
            basic_info.get('marital_status', '')
        ])
        
        risk_assessment = metadata.get('risk_assessment', {})
        if risk_assessment:
            keywords.append(risk_assessment.get('risk_level', ''))
        
        profile_tags = metadata.get('profile_tags', [])
        keywords.extend(profile_tags)
        
        return [kw for kw in keywords if kw][:10]
    
    def add_product_data(self, products: List[Dict[str, Any]]) -> int:
        """添加产品数据"""
        processed_count = 0
        
        with sqlite3.connect(self.db_path) as conn:
            for product in products:
                try:
                    # 处理产品数据
                    processed_product = self.product_processor.process(product)
                    
                    # 保存文档
                    doc_id = product.get('product_id', f"PROD_{processed_count}")
                    self._save_document(
                        conn, doc_id, "product", 
                        processed_product['title'],
                        processed_product['content'],
                        processed_product['metadata']
                    )
                    
                    # 切分文档
                    chunks = self.chunk_splitter.split_product(processed_product)
                    
                    # 保存块
                    for chunk in chunks:
                        self._save_chunk(conn, chunk, doc_id)
                    
                    processed_count += 1
                    
                except Exception as e:
                    print(f"处理产品数据失败 {product.get('product_id', 'unknown')}: {e}")
        
        print(f"已添加 {processed_count} 个产品到知识库")
        return processed_count
    
    def add_case_data(self, cases: List[Dict[str, Any]]) -> int:
        """添加案例数据"""
        processed_count = 0
        
        with sqlite3.connect(self.db_path) as conn:
            for case in cases:
                try:
                    # 处理案例数据
                    processed_case = self.case_processor.process(case)
                    
                    # 保存文档
                    doc_id = case.get('case_id', f"CASE_{processed_count}")
                    self._save_document(
                        conn, doc_id, "case",
                        processed_case['title'],
                        processed_case['content'],
                        processed_case['metadata']
                    )
                    
                    # 切分文档
                    chunks = self.chunk_splitter.split_case(processed_case)
                    
                    # 保存块
                    for chunk in chunks:
                        self._save_chunk(conn, chunk, doc_id)
                    
                    processed_count += 1
                    
                except Exception as e:
                    print(f"处理案例数据失败 {case.get('case_id', 'unknown')}: {e}")
        
        print(f"已添加 {processed_count} 个案例到知识库")
        return processed_count
    
    def add_policy_data(self, policies: List[Dict[str, Any]]) -> int:
        """添加政策数据"""
        processed_count = 0
        
        with sqlite3.connect(self.db_path) as conn:
            for policy in policies:
                try:
                    # 处理政策数据
                    processed_policy = self.policy_processor.process(policy)
                    
                    # 保存文档
                    doc_id = policy.get('policy_id', f"POLICY_{processed_count}")
                    self._save_document(
                        conn, doc_id, "policy",
                        processed_policy['title'],
                        processed_policy['content'],
                        processed_policy['metadata']
                    )
                    
                    # 切分文档
                    chunks = self.chunk_splitter.split_policy(processed_policy)
                    
                    # 保存块
                    for chunk in chunks:
                        self._save_chunk(conn, chunk, doc_id)
                    
                    processed_count += 1
                    
                except Exception as e:
                    print(f"处理政策数据失败 {policy.get('policy_id', 'unknown')}: {e}")
        
        print(f"已添加 {processed_count} 个政策到知识库")
        return processed_count
    
    def _save_document(self, conn: sqlite3.Connection, doc_id: str, doc_type: str,
                      title: str, content: str, metadata: Dict[str, Any]):
        """保存文档"""
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO documents 
            (doc_id, doc_type, title, content, metadata)
            VALUES (?, ?, ?, ?, ?)
        ''', (doc_id, doc_type, title, content, json.dumps(metadata, ensure_ascii=False)))
    
    def _save_chunk(self, conn: sqlite3.Connection, chunk: Dict[str, Any], doc_id: str):
        """保存文档块"""
        try:
            # 获取向量
            embedding = call_embedding_api(chunk['content'])
            embedding_blob = json.dumps(embedding).encode('utf-8')
            
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO chunks
                (chunk_id, doc_id, content, content_type, embedding, keywords, category_path, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                chunk['chunk_id'],
                doc_id,
                chunk['content'],
                chunk['content_type'],
                embedding_blob,
                json.dumps(chunk.get('keywords', []), ensure_ascii=False),
                chunk.get('category_path', ''),
                json.dumps(chunk.get('metadata', {}), ensure_ascii=False)
            ))
            
        except Exception as e:
            print(f"保存文档块失败 {chunk.get('chunk_id', 'unknown')}: {e}")
    
    def search_chunks(self, query: str, content_types: Optional[List[str]] = None, 
                     limit: int = 10) -> List[Dict[str, Any]]:
        """搜索文档块"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 构建查询条件
            where_conditions = []
            params = []
            
            if content_types:
                placeholders = ','.join(['?' for _ in content_types])
                where_conditions.append(f"content_type IN ({placeholders})")
                params.extend(content_types)
            
            # 关键词搜索
            if query:
                where_conditions.append("content LIKE ?")
                params.append(f"%{query}%")
            
            where_clause = "WHERE " + " AND ".join(where_conditions) if where_conditions else ""
            
            sql = f'''
                SELECT chunk_id, content, content_type, keywords, category_path, metadata
                FROM chunks
                {where_clause}
                ORDER BY id DESC
                LIMIT ?
            '''
            params.append(limit)
            
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            chunks = []
            for row in results:
                chunk = {
                    'chunk_id': row[0],
                    'content': row[1],
                    'content_type': row[2],
                    'keywords': json.loads(row[3]) if row[3] else [],
                    'category_path': row[4],
                    'metadata': json.loads(row[5]) if row[5] else {}
                }
                chunks.append(chunk)
            
            return chunks
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取知识库统计信息"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 文档统计
            cursor.execute("SELECT doc_type, COUNT(*) FROM documents GROUP BY doc_type")
            doc_stats = dict(cursor.fetchall())
            
            # 块统计
            cursor.execute("SELECT content_type, COUNT(*) FROM chunks GROUP BY content_type")
            chunk_stats = dict(cursor.fetchall())
            
            # 总数统计
            cursor.execute("SELECT COUNT(*) FROM documents")
            total_docs = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM chunks")
            total_chunks = cursor.fetchone()[0]
            
            return {
                'total_documents': total_docs,
                'total_chunks': total_chunks,
                'documents_by_type': doc_stats,
                'chunks_by_type': chunk_stats
            }
    
    def build_indexes(self):
        """构建索引"""
        print("开始构建索引...")
        self.index_builder.build_vector_index(self.db_path)
        self.index_builder.build_keyword_index(self.db_path)
        self.index_builder.build_attribute_index(self.db_path)
        self.index_builder.build_category_index(self.db_path)
        print("索引构建完成")

if __name__ == "__main__":
    # 测试知识库管理器
    print("初始化知识库管理器...")
    manager = KnowledgeBaseManager()
    
    # 加载和处理合成数据
    synthesis_dir = "./data/synthesis"
    if os.path.exists(synthesis_dir):
        manager.load_and_process_synthesis_data(synthesis_dir)
        
        # 构建索引
        manager.build_indexes()
        
        # 显示统计信息
        stats = manager.get_statistics()
        print("\n知识库构建完成！")
        print(f"总文档数: {stats['total_documents']}")
        print(f"总文档块数: {stats['total_chunks']}")
        print(f"文档类型分布: {stats['documents_by_type']}")
        print(f"文档块类型分布: {stats['chunks_by_type']}")
        
        # 测试搜索
        print("\n测试搜索功能...")
        results = manager.search_chunks("贷款", limit=5)
        print(f"搜索'贷款'找到 {len(results)} 个结果:")
        for result in results:
            print(f"- {result['chunk_id']}: {result['content'][:100]}...")
    else:
        print(f"合成数据目录不存在: {synthesis_dir}")