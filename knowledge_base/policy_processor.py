# knowledge_base/policy_processor.py
import json
import re
from typing import Dict, List, Any

class PolicyProcessor:
    """监管政策处理器"""
    
    def __init__(self):
        self.required_fields = ['policy_id', 'title', 'issuer']
        self.policy_types = ['法律法规', '部门规章', '规范性文件', '通知公告', '指导意见']
        self.compliance_keywords = ['应当', '必须', '禁止', '不得', '严格', '加强', '完善', '建立']
    
    def process(self, policy_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理政策数据"""
        try:
            # 对于政策更新数据，添加缺失字段
            policy_data = self._normalize_policy_data(policy_data)
            
            # 验证必要字段
            self._validate_policy_data(policy_data)
            
            # 生成标题
            title = self._generate_title(policy_data)
            
            # 生成内容
            content = self._generate_content(policy_data)
            
            # 提取元数据
            metadata = self._extract_metadata(policy_data)
            
            return {
                'title': title,
                'content': content,
                'metadata': metadata
            }
            
        except Exception as e:
            raise Exception(f"处理政策数据失败: {e}")
    
    def _normalize_policy_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化政策数据，处理缺失字段"""
        normalized_data = data.copy()
        
        # 如果是政策更新数据，添加必要字段
        if 'update_id' in data and 'policy_id' not in data:
            normalized_data['policy_id'] = data['update_id']
        
        if 'update_content' in data and 'title' not in data:
            update_content = data['update_content']
            # 从更新内容中提取标题
            if len(update_content) > 50:
                normalized_data['title'] = update_content[:47] + "..."
            else:
                normalized_data['title'] = update_content
        
        if 'issuer' not in data:
            # 尝试从其他字段推断发布机构
            if 'original_policy_id' in data:
                normalized_data['issuer'] = "监管机构"
            else:
                normalized_data['issuer'] = "未知机构"
        
        # 确保有基本的content结构
        if 'content' not in data:
            normalized_data['content'] = {}
        
        # 为政策更新添加内容结构
        if 'update_type' in data:
            content = normalized_data.setdefault('content', {})
            content['scope'] = f"适用于{data.get('update_type', '政策更新')}"
            content['key_provisions'] = [data.get('update_content', '')]
            content['compliance_requirements'] = [data.get('implementation_guidance', '按照更新要求执行')]
        
        return normalized_data
    
    def _validate_policy_data(self, data: Dict[str, Any]):
        """验证政策数据"""
        for field in self.required_fields:
            if field not in data:
                raise ValueError(f"缺少必要字段: {field}")
    
    def _generate_title(self, data: Dict[str, Any]) -> str:
        """生成政策标题"""
        title = data.get('title', '')
        issuer = data.get('issuer', '')
        effective_date = data.get('effective_date', '')
        
        if effective_date:
            return f"{issuer}：{title}（{effective_date}生效）"
        else:
            return f"{issuer}：{title}"
    
    def _generate_content(self, data: Dict[str, Any]) -> str:
        """生成政策内容描述"""
        content_parts = []
        
        # 基本信息
        basic_info = f"{data['issuer']}发布的《{data['title']}》"
        if 'effective_date' in data:
            basic_info += f"于{data['effective_date']}生效"
        content_parts.append(basic_info)
        
        # 适用范围
        policy_content = data.get('content', {})
        if isinstance(policy_content, dict):
            scope = policy_content.get('scope', '')
            if scope:
                content_parts.append(f"适用范围：{scope}")
            
            # 主要条款
            key_provisions = policy_content.get('key_provisions', [])
            if key_provisions and isinstance(key_provisions, list):
                provisions_text = "；".join(str(p) for p in key_provisions)
                content_parts.append(f"主要条款：{provisions_text}")
            
            # 合规要求
            compliance_requirements = policy_content.get('compliance_requirements', [])
            if compliance_requirements and isinstance(compliance_requirements, list):
                compliance_text = "；".join(str(r) for r in compliance_requirements)
                content_parts.append(f"合规要求：{compliance_text}")
        
        # 业务影响
        business_impact = data.get('business_impact', {})
        if isinstance(business_impact, dict):
            affected_products = business_impact.get('affected_products', [])
            if affected_products and isinstance(affected_products, list):
                products_text = "、".join(str(p) for p in affected_products)
                content_parts.append(f"影响产品：{products_text}")
            
            operational_guidance = business_impact.get('operational_guidance', '')
            if operational_guidance:
                content_parts.append(f"操作指导：{operational_guidance}")
        
        # 特殊处理政策更新内容
        if 'update_type' in data:
            update_type = data.get('update_type', '')
            update_content = data.get('update_content', '')
            reason = data.get('reason', '')
            
            content_parts.append(f"更新类型：{update_type}")
            if update_content:
                content_parts.append(f"更新内容：{update_content}")
            if reason:
                content_parts.append(f"更新原因：{reason}")
        
        return "。".join(content_parts) + "。"
    
    def _extract_metadata(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取政策元数据"""
        metadata = {}
        
        # 复制原始数据
        for key, value in data.items():
            metadata[key] = value
        
        # 添加处理后的字段
        metadata['processed'] = True
        metadata['content_type'] = 'regulatory_policy'
        
        # 政策类型分类
        metadata['policy_type'] = self._classify_policy_type(data)
        
        # 合规强度评估
        metadata['compliance_level'] = self._assess_compliance_level(data)
        
        # 业务相关性
        metadata['business_relevance'] = self._assess_business_relevance(data)
        
        # 提取合规要点
        metadata['compliance_points'] = self._extract_compliance_points(data)
        
        # 风险提示
        metadata['risk_warnings'] = self._extract_risk_warnings(data)
        
        # 操作指导
        metadata['operational_guidelines'] = self._extract_operational_guidelines(data)
        
        return metadata
    
    def _classify_policy_type(self, data: Dict[str, Any]) -> str:
        """分类政策类型"""
        title = data.get('title', '').lower()
        issuer = data.get('issuer', '').lower()
        
        # 检查是否为政策更新
        if 'update_type' in data:
            update_type = data.get('update_type', '')
            return f"政策{update_type}"
        
        # 根据发布机构判断
        if '银保监会' in issuer or '央行' in issuer or '人民银行' in issuer:
            return '金融监管政策'
        elif '国务院' in issuer:
            return '国家政策'
        elif '部委' in issuer or '部' in issuer:
            return '部门规章'
        
        # 根据标题关键词判断
        if any(word in title for word in ['法', '条例', '规定']):
            return '法律法规'
        elif any(word in title for word in ['办法', '细则', '规则']):
            return '实施办法'
        elif any(word in title for word in ['通知', '公告', '意见']):
            return '政策文件'
        
        return '其他政策'
    
    def _assess_compliance_level(self, data: Dict[str, Any]) -> str:
        """评估合规强度"""
        content = str(data.get('content', {}))
        title = data.get('title', '')
        
        # 统计强制性词汇
        mandatory_count = sum(1 for word in ['必须', '应当', '禁止', '不得'] if word in content)
        
        # 根据政策类型和强制性词汇数量判断
        policy_type = self._classify_policy_type(data)
        
        if policy_type in ['法律法规', '金融监管政策'] or mandatory_count >= 5:
            return '强制'
        elif mandatory_count >= 2 or '规定' in title:
            return '要求'
        else:
            return '指导'
    
    def _assess_business_relevance(self, data: Dict[str, Any]) -> List[str]:
        """评估业务相关性"""
        relevance = []
        
        content_str = str(data.get('content', {})) + data.get('title', '')
        
        # 产品相关性
        if any(word in content_str for word in ['贷款', '信贷', '放贷']):
            relevance.append('贷款业务')
        
        if any(word in content_str for word in ['抵押', '担保']):
            relevance.append('担保业务')
        
        if any(word in content_str for word in ['征信', '信用']):
            relevance.append('征信管理')
        
        if any(word in content_str for word in ['风险', '风控']):
            relevance.append('风险管理')
        
        if any(word in content_str for word in ['客户', '消费者']):
            relevance.append('客户管理')
        
        # 机构相关性
        if any(word in content_str for word in ['银行', '金融机构']):
            relevance.append('银行业务')
        
        if any(word in content_str for word in ['小微', '中小企业']):
            relevance.append('小微金融')
        
        return list(set(relevance))
    
    def _extract_compliance_points(self, data: Dict[str, Any]) -> List[Dict[str, str]]:
        """提取合规要点"""
        compliance_points = []
        
        policy_content = data.get('content', {})
        if isinstance(policy_content, dict):
            # 从主要条款中提取
            key_provisions = policy_content.get('key_provisions', [])
            if isinstance(key_provisions, list):
                for provision in key_provisions:
                    provision_str = str(provision)
                    if any(keyword in provision_str for keyword in self.compliance_keywords):
                        compliance_points.append({
                            'requirement': self._extract_requirement_type(provision_str),
                            'details': provision_str
                        })
            
            # 从合规要求中提取
            compliance_requirements = policy_content.get('compliance_requirements', [])
            if isinstance(compliance_requirements, list):
                for requirement in compliance_requirements:
                    requirement_str = str(requirement)
                    compliance_points.append({
                        'requirement': self._extract_requirement_type(requirement_str),
                        'details': requirement_str
                    })
        
        return compliance_points
    
    def _extract_requirement_type(self, text: str) -> str:
        """提取要求类型"""
        text_str = str(text)
        if '审查' in text_str or '审核' in text_str:
            return '审查要求'
        elif '评估' in text_str or '评价' in text_str:
            return '评估要求'
        elif '管理' in text_str or '控制' in text_str:
            return '管理要求'
        elif '报告' in text_str or '披露' in text_str:
            return '报告要求'
        elif '禁止' in text_str or '不得' in text_str:
            return '禁止性要求'
        else:
            return '一般要求'
    
    def _extract_risk_warnings(self, data: Dict[str, Any]) -> List[str]:
        """提取风险提示"""
        warnings = []
        
        content_str = str(data.get('content', {}))
        
        # 常见风险提示模式
        risk_patterns = [
            r'(违反.*的.*风险)',
            r'(可能.*处罚)',
            r'(应当防范.*风险)',
            r'(严禁.*行为)'
        ]
        
        for pattern in risk_patterns:
            matches = re.findall(pattern, content_str)
            warnings.extend(matches)
        
        # 预定义风险提示
        if '违规' in content_str:
            warnings.append('违规操作风险')
        
        if '处罚' in content_str:
            warnings.append('监管处罚风险')
        
        if '风险' in content_str and '控制' in content_str:
            warnings.append('风险控制不当风险')
        
        return list(set(warnings))
    
    def _extract_operational_guidelines(self, data: Dict[str, Any]) -> List[str]:
        """提取操作指导"""
        guidelines = []
        
        business_impact = data.get('business_impact', {})
        if isinstance(business_impact, dict):
            operational_guidance = business_impact.get('operational_guidance', '')
            if operational_guidance:
                # 按句号分割操作指导
                guidance_sentences = str(operational_guidance).split('；')
                guidelines.extend([s.strip() for s in guidance_sentences if s.strip()])
        
        # 从政策内容中提取操作性语句
        policy_content = data.get('content', {})
        if isinstance(policy_content, dict):
            compliance_requirements = policy_content.get('compliance_requirements', [])
            if isinstance(compliance_requirements, list):
                for requirement in compliance_requirements:
                    requirement_str = str(requirement)
                    if any(word in requirement_str for word in ['建立', '完善', '加强', '制定']):
                        guidelines.append(requirement_str)
        
        # 从更新指导中提取
        implementation_guidance = data.get('implementation_guidance', '')
        if implementation_guidance:
            guidelines.append(str(implementation_guidance))
        
        return list(set(guidelines))

if __name__ == "__main__":
    # 测试政策处理器
    processor = PolicyProcessor()
    
    # 测试标准政策数据
    test_policy = {
        "policy_id": "POLICY001",
        "title": "个人贷款管理暂行办法",
        "issuer": "中国银保监会",
        "effective_date": "2024-01-01",
        "content": {
            "scope": "适用于银行业金融机构办理的个人贷款业务",
            "key_provisions": [
                "贷款用途应当明确、合法",
                "贷款金额应当与借款人还款能力相匹配"
            ],
            "compliance_requirements": [
                "严格审查借款人资质",
                "建立贷后管理制度"
            ]
        }
    }
    
    # 测试政策更新数据
    test_update = {
        "update_id": "UPDATE001",
        "update_type": "修订",
        "update_date": "2024-10-01",
        "update_content": "对风险管理实施细则中的风险评估流程进行了修订",
        "reason": "随着业务发展需要更新风险管理要求"
    }
    
    try:
        print("处理标准政策数据...")
        result1 = processor.process(test_policy)
        print(f"标题: {result1['title']}")
        print(f"政策类型: {result1['metadata']['policy_type']}")
        
        print("\n处理政策更新数据...")
        result2 = processor.process(test_update)
        print(f"标题: {result2['title']}")
        print(f"政策类型: {result2['metadata']['policy_type']}")
        
    except Exception as e:
        print(f"处理失败: {e}")