# knowledge_base/risk_processor.py
import json
import math
import re
from typing import Dict, List, Any, Optional

class RiskProcessor:
    """风险评估处理器"""
    
    def __init__(self):
        # 风险等级映射
        self.risk_levels = {
            "很低": {"score_range": (80, 100), "description": "风险极低，优质客户"},
            "低": {"score_range": (65, 79), "description": "风险较低，标准客户"},
            "中": {"score_range": (45, 64), "description": "风险中等，需谨慎评估"},
            "高": {"score_range": (25, 44), "description": "风险较高，严格审核"},
            "很高": {"score_range": (0, 24), "description": "风险很高，建议拒绝"}
        }
        
        # 风险因子权重
        self.factor_weights = {
            "demographic": 0.15,
            "financial": 0.35,
            "credit": 0.35,
            "behavioral": 0.15
        }
        
        # 违约概率区间
        self.default_probability_ranges = {
            "很低": (0.001, 0.010),
            "低": (0.010, 0.030),
            "中": (0.030, 0.080),
            "高": (0.080, 0.200),
            "很高": (0.200, 0.500)
        }
    
    def _safe_extract_number(self, value: Any) -> float:
        """安全提取数字值"""
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            # 移除非数字字符，提取数字
            match = re.search(r'(\d+(?:\.\d+)?)', value)
            if match:
                return float(match.group(1))
        return 0.0
    
    def _safe_extract_percentage(self, value: Any) -> float:
        """安全提取百分比值"""
        if isinstance(value, str):
            if '%' in value:
                # 移除百分号并转换
                cleaned = value.replace('%', '').strip()
                try:
                    return float(cleaned) / 100
                except ValueError:
                    return 0.0
            else:
                # 提取数字部分
                match = re.search(r'(\d+(?:\.\d+)?)', value)
                if match:
                    num = float(match.group(1))
                    # 如果数字大于1，假设是百分比形式
                    if num > 1:
                        return num / 100
                    else:
                        return num
        elif isinstance(value, (int, float)):
            if 0 <= value <= 1:
                return value
            else:
                return value / 100
        return 0.0
    
    def process_risk_models(self, risk_models: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理风险模型数据"""
        processed_chunks = []
        
        for model in risk_models:
            chunks = self._process_single_risk_model(model)
            processed_chunks.extend(chunks)
        
        return processed_chunks
    
    def process_risk_cases(self, risk_cases: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理风险评估案例数据"""
        processed_chunks = []
        
        for case in risk_cases:
            chunks = self._process_single_risk_case(case)
            processed_chunks.extend(chunks)
        
        return processed_chunks
    
    def _process_single_risk_model(self, model: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理单个风险模型"""
        chunks = []
        model_id = model.get("model_id", "")
        
        try:
            # 1. 模型基本信息块
            basic_chunk = self._create_model_basic_chunk(model)
            chunks.append(basic_chunk)
            
            # 2. 风险因子说明块
            factors_chunk = self._create_factors_explanation_chunk(model)
            chunks.append(factors_chunk)
            
            # 3. 评分规则块
            scoring_chunk = self._create_scoring_rules_chunk(model)
            chunks.append(scoring_chunk)
            
            # 4. 风险等级标准块
            thresholds_chunk = self._create_thresholds_chunk(model)
            chunks.append(thresholds_chunk)
            
            # 5. 模型性能指标块
            performance_chunk = self._create_performance_chunk(model)
            chunks.append(performance_chunk)
            
        except Exception as e:
            print(f"处理风险模型 {model_id} 失败: {e}")
        
        return chunks
    
    def _process_single_risk_case(self, case: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理单个风险评估案例"""
        chunks = []
        case_id = case.get("case_id", "")
        
        try:
            # 1. 完整案例块
            full_case_chunk = self._create_full_case_chunk(case)
            chunks.append(full_case_chunk)
            
            # 2. 风险分析块
            analysis_chunk = self._create_risk_analysis_chunk(case)
            chunks.append(analysis_chunk)
            
            # 3. 决策过程块
            decision_chunk = self._create_decision_process_chunk(case)
            chunks.append(decision_chunk)
            
            # 4. 相似客户参考块
            reference_chunk = self._create_customer_reference_chunk(case)
            chunks.append(reference_chunk)
            
        except Exception as e:
            print(f"处理风险案例 {case_id} 失败: {e}")
        
        return chunks
    
    def _create_full_case_chunk(self, case: Dict[str, Any]) -> Dict[str, Any]:
        """创建完整案例块"""
        case_id = case.get("case_id", "")
        customer_info = case.get("customer_info", {})
        assessment_result = case.get("assessment_result", {})
        business_decision = case.get("business_decision", {})
        
        # 构建案例描述
        content_parts = [
            f"风险评估案例：{case_id}",
            f"评估日期：{case.get('assessment_date', '未知')}",
            f"模型版本：{case.get('model_version', '未知')}"
        ]
        
        # 客户信息
        content_parts.append(f"\n客户信息：")
        content_parts.append(f"年龄：{customer_info.get('age', '未知')}岁")
        content_parts.append(f"职业：{customer_info.get('occupation', '未知')}")
        content_parts.append(f"月收入：{customer_info.get('monthly_income', '未知')}")
        content_parts.append(f"征信评分：{customer_info.get('credit_score', '未知')}")
        content_parts.append(f"申请金额：{customer_info.get('application_amount', '未知')}")
        
        # 评估结果
        content_parts.append(f"\n风险评估结果：")
        content_parts.append(f"风险评分：{assessment_result.get('total_risk_score', '未知')}")
        content_parts.append(f"风险等级：{assessment_result.get('risk_level', '未知')}")
        
        # 处理违约概率的百分比格式
        default_prob = assessment_result.get('default_probability', '0%')
        if isinstance(default_prob, str):
            # 直接使用字符串中的百分比
            content_parts.append(f"违约概率：{default_prob}")
        else:
            try:
                prob_value = self._safe_extract_percentage(default_prob)
                content_parts.append(f"违约概率：{prob_value:.1%}")
            except (ValueError, TypeError):
                content_parts.append(f"违约概率：{default_prob}")
        
        # 业务决策
        content_parts.append(f"\n业务决策：")
        content_parts.append(f"最终决策：{business_decision.get('final_decision', '未知')}")
        if business_decision.get('final_decision') == '批准':
            content_parts.append(f"批准金额：{business_decision.get('approved_amount', '未知')}")
            content_parts.append(f"执行利率：{business_decision.get('interest_rate', '未知')}")
        
        content = "\n".join(content_parts)
        
        # 提取关键词
        keywords = [
            "风险评估", "评估案例", case_id,
            customer_info.get('occupation', ''),
            assessment_result.get('risk_level', ''),
            business_decision.get('final_decision', '')
        ]
        
        return {
            "chunk_id": f"{case_id}_FULL",
            "content_type": "risk_case_full",
            "content": content,
            "metadata": {
                "case_id": case_id,
                "customer_age": customer_info.get('age'),
                "customer_occupation": customer_info.get('occupation'),
                "application_amount": customer_info.get('application_amount'),
                "risk_score": assessment_result.get('total_risk_score'),
                "risk_level": assessment_result.get('risk_level'),
                "final_decision": business_decision.get('final_decision')
            },
            "keywords": [kw for kw in keywords if kw][:10],
            "category_path": f"风险管理/评估案例/{assessment_result.get('risk_level', '未分级')}"
        }
    
    def _create_risk_analysis_chunk(self, case: Dict[str, Any]) -> Dict[str, Any]:
        """创建风险分析块"""
        case_id = case.get("case_id", "")
        assessment_input = case.get("assessment_input", {})
        assessment_result = case.get("assessment_result", {})
        
        content_parts = ["风险分析详情："]
        
        # 分数构成
        score_breakdown = assessment_result.get("score_breakdown", {})
        if score_breakdown:
            content_parts.append("\n风险评分构成：")
            for category, score in score_breakdown.items():
                category_name = self._translate_category_name(category)
                score_val = self._safe_extract_number(score)
                content_parts.append(f"- {category_name}：{score_val}分")
        
        # 风险因子
        risk_factors = assessment_result.get("risk_factors", [])
        if risk_factors:
            content_parts.append(f"\n识别的风险因子：")
            for factor in risk_factors:
                content_parts.append(f"- {factor}")
        
        # 缓释建议
        mitigation = assessment_result.get("mitigation_suggestions", [])
        if mitigation:
            content_parts.append(f"\n风险缓释建议：")
            for suggestion in mitigation:
                content_parts.append(f"- {suggestion}")
        
        # 详细输入数据分析
        content_parts.append(f"\n关键评估数据：")
        
        financial_factors = assessment_input.get("financial_factors", {})
        if financial_factors:
            monthly_income = financial_factors.get("monthly_income", 0)
            debt_ratio_raw = financial_factors.get("debt_to_income", 0)
            debt_ratio = self._safe_extract_percentage(debt_ratio_raw)
            content_parts.append(f"月收入：{monthly_income}，负债率：{debt_ratio:.1%}")
        
        credit_factors = assessment_input.get("credit_factors", {})
        if credit_factors:
            credit_score = credit_factors.get("credit_score", 0)
            overdue = credit_factors.get("overdue_records", 0)
            overdue_num = self._safe_extract_number(overdue)
            content_parts.append(f"征信评分：{credit_score}分，逾期记录：{overdue_num:.0f}次")
        
        content = "\n".join(content_parts)
        
        keywords = [
            "风险分析", "评分构成", "风险因子", "缓释建议",
            assessment_result.get('risk_level', ''),
            *risk_factors[:3]  # 取前3个风险因子
        ]
        
        return {
            "chunk_id": f"{case_id}_ANALYSIS",
            "content_type": "risk_analysis",
            "content": content,
            "metadata": {
                "case_id": case_id,
                "total_score": assessment_result.get('total_risk_score'),
                "risk_level": assessment_result.get('risk_level'),
                "risk_factors_count": len(risk_factors),
                "mitigation_count": len(mitigation),
                "score_breakdown": score_breakdown
            },
            "keywords": [kw for kw in keywords if kw][:10],
            "category_path": "风险管理/风险分析/分析详情"
        }
    
    def _create_decision_process_chunk(self, case: Dict[str, Any]) -> Dict[str, Any]:
        """创建决策过程块"""
        case_id = case.get("case_id", "")
        assessment_result = case.get("assessment_result", {})
        business_decision = case.get("business_decision", {})
        
        content_parts = ["决策过程分析："]
        
        # 风险评估到决策的逻辑
        risk_level = assessment_result.get('risk_level', '')
        final_decision = business_decision.get('final_decision', '')
        
        content_parts.append(f"风险等级：{risk_level}")
        content_parts.append(f"最终决策：{final_decision}")
        
        # 条件和限制
        conditions = business_decision.get('conditions', [])
        if conditions:
            content_parts.append(f"\n审批条件：")
            for condition in conditions:
                content_parts.append(f"- {condition}")
        
        # 决策参数
        if final_decision == '批准':
            approved_amount = business_decision.get('approved_amount', 0)
            interest_rate = business_decision.get('interest_rate', 0)
            content_parts.append(f"\n批准参数：")
            content_parts.append(f"批准金额：{approved_amount}")
            content_parts.append(f"执行利率：{interest_rate}")
        
        # 决策逻辑说明
        content_parts.append(f"\n决策逻辑：")
        if risk_level in ['很低', '低']:
            content_parts.append("风险等级较低，符合标准放贷条件")
        elif risk_level == '中':
            content_parts.append("风险等级中等，需要综合考虑其他因素")
        else:
            content_parts.append("风险等级较高，需要严格审核或拒绝")
        
        content = "\n".join(content_parts)
        
        keywords = [
            "决策过程", "业务决策", risk_level, final_decision,
            "审批条件", "决策逻辑", "风险控制"
        ]
        
        return {
            "chunk_id": f"{case_id}_DECISION",
            "content_type": "decision_process",
            "content": content,
            "metadata": {
                "case_id": case_id,
                "risk_level": risk_level,
                "final_decision": final_decision,
                "has_conditions": len(conditions) > 0,
                "approved_amount": business_decision.get('approved_amount'),
                "interest_rate": business_decision.get('interest_rate')
            },
            "keywords": [kw for kw in keywords if kw][:10],
            "category_path": f"风险管理/决策过程/{final_decision}"
        }
    
    def _create_customer_reference_chunk(self, case: Dict[str, Any]) -> Dict[str, Any]:
        """创建客户参考块"""
        case_id = case.get("case_id", "")
        customer_info = case.get("customer_info", {})
        assessment_result = case.get("assessment_result", {})
        
        # 客户画像总结
        age = self._safe_extract_number(customer_info.get('age', 0))
        occupation = customer_info.get('occupation', '')
        application_amount = customer_info.get('application_amount', '')
        risk_level = assessment_result.get('risk_level', '')
        
        content_parts = [
            f"相似客户参考：",
            f"客户画像：{age:.0f}岁{occupation}，申请{application_amount}",
            f"风险评估：{risk_level}风险，评分{assessment_result.get('total_risk_score', 0)}"
        ]
        
        # 适用场景
        content_parts.append(f"\n适用参考场景：")
        
        # 年龄群体
        age_group = self._get_age_group(int(age))
        content_parts.append(f"- {age_group}客户群体")
        
        # 职业类型
        if occupation:
            content_parts.append(f"- {occupation}职业类型")
        
        # 金额区间
        amount_val = self._safe_extract_number(application_amount)
        amount_range = self._get_amount_range(int(amount_val))
        content_parts.append(f"- {amount_range}申请金额")
        
        # 风险等级
        content_parts.append(f"- {risk_level}风险客户")
        
        # 参考价值
        content_parts.append(f"\n参考价值：")
        content_parts.append(f"该案例可作为相似客户风险评估的参考标准")
        
        content = "\n".join(content_parts)
        
        keywords = [
            "客户参考", "相似客户", occupation, age_group,
            amount_range, risk_level, "参考案例"
        ]
        
        return {
            "chunk_id": f"{case_id}_REFERENCE",
            "content_type": "customer_reference",
            "content": content,
            "metadata": {
                "case_id": case_id,
                "customer_age": age,
                "customer_occupation": occupation,
                "application_amount": application_amount,
                "risk_level": risk_level,
                "age_group": age_group,
                "amount_range": amount_range
            },
            "keywords": [kw for kw in keywords if kw][:10],
            "category_path": f"风险管理/客户参考/{risk_level}风险"
        }
    
    # 保留其他辅助方法...
    def _translate_category_name(self, category: str) -> str:
        """翻译类别名称"""
        translations = {
            "demographic": "人口统计",
            "financial": "财务状况",
            "credit": "征信状况",
            "behavioral": "行为特征",
            "external": "外部因素"
        }
        return translations.get(category, category)
    
    def _get_age_group(self, age: int) -> str:
        """获取年龄组"""
        if age < 25:
            return "年轻"
        elif age < 35:
            return "青年"
        elif age < 50:
            return "中年"
        else:
            return "中老年"
    
    def _get_amount_range(self, amount: int) -> str:
        """获取金额范围"""
        if amount < 50000:
            return "小额"
        elif amount < 200000:
            return "中额"
        else:
            return "大额"

if __name__ == "__main__":
    # 测试风险处理器
    processor = RiskProcessor()
    
    # 测试风险案例数据  
    test_case = {
        "case_id": "RISK_CASE_001",
        "customer_info": {"age": 32, "occupation": "软件工程师", "application_amount": "150000元"},
        "assessment_result": {
            "total_risk_score": "72分",
            "risk_level": "低",
            "default_probability": "2%",
            "risk_factors": ["工作年限较短"],
            "score_breakdown": {"financial": "75分", "credit": "80分"}
        },
        "business_decision": {"final_decision": "批准", "approved_amount": "120000元"}
    }
    
    print("处理风险案例数据...")
    case_chunks = processor.process_risk_cases([test_case])
    
    print(f"生成了 {len(case_chunks)} 个案例块")
    
    for chunk in case_chunks:
        print(f"\n块ID: {chunk['chunk_id']}")
        print(f"类型: {chunk['content_type']}")
        print(f"关键词: {chunk['keywords']}")