# knowledge_base/chunk_splitter.py
import re
import json
from typing import Dict, List, Any

class ChunkSplitter:
    """文档切分器"""
    
    def __init__(self, max_chunk_size: int = 512, overlap_size: int = 50):
        self.max_chunk_size = max_chunk_size
        self.overlap_size = overlap_size
    
    def split_product(self, product_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """切分产品数据"""
        chunks = []
        product_id = product_data.get('metadata', {}).get('product_id', 'UNKNOWN')
        
        # 基本信息块
        basic_info_chunk = self._create_product_basic_chunk(product_data, product_id)
        chunks.append(basic_info_chunk)
        
        # 申请条件块
        requirements_chunk = self._create_product_requirements_chunk(product_data, product_id)
        chunks.append(requirements_chunk)
        
        # 产品特征块
        features_chunk = self._create_product_features_chunk(product_data, product_id)
        chunks.append(features_chunk)
        
        return chunks
    
    def split_case(self, case_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """切分案例数据"""
        chunks = []
        case_id = case_data.get('metadata', {}).get('case_id', 'UNKNOWN')
        
        # 客户画像块
        profile_chunk = self._create_case_profile_chunk(case_data, case_id)
        chunks.append(profile_chunk)
        
        # 申请信息块
        application_chunk = self._create_case_application_chunk(case_data, case_id)
        chunks.append(application_chunk)
        
        # 审批结果块
        result_chunk = self._create_case_result_chunk(case_data, case_id)
        chunks.append(result_chunk)
        
        # 综合分析块
        analysis_chunk = self._create_case_analysis_chunk(case_data, case_id)
        chunks.append(analysis_chunk)
        
        return chunks
    
    def split_policy(self, policy_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """切分政策数据"""
        chunks = []
        policy_id = policy_data.get('metadata', {}).get('policy_id', 'UNKNOWN')
        
        # 按段落切分政策内容
        content = policy_data.get('content', '')
        paragraphs = self._split_by_paragraphs(content)
        
        for i, paragraph in enumerate(paragraphs):
            if len(paragraph.strip()) > 50:  # 只保留有意义的段落
                chunk = {
                    'chunk_id': f"{policy_id}_PARA_{i+1}",
                    'content': paragraph.strip(),
                    'content_type': 'policy_paragraph',
                    'keywords': self._extract_keywords(paragraph),
                    'category_path': f"监管政策/{policy_data.get('metadata', {}).get('issuer', 'unknown')}",
                    'metadata': {
                        'policy_id': policy_id,
                        'paragraph_index': i + 1,
                        'policy_title': policy_data.get('title', ''),
                        'issuer': policy_data.get('metadata', {}).get('issuer', '')
                    }
                }
                chunks.append(chunk)
        
        return chunks
    
    def split_dialog(self, dialog_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """切分对话数据"""
        chunks = []
        dialog_id = dialog_data.get('metadata', {}).get('dialog_id', 'UNKNOWN')
        
        # 问答对切分
        messages = dialog_data.get('metadata', {}).get('messages', [])
        qa_pairs = self._extract_qa_pairs(messages)
        
        for i, qa_pair in enumerate(qa_pairs):
            chunk = {
                'chunk_id': f"{dialog_id}_QA_{i+1}",
                'content': f"客户问题：{qa_pair['question']}\n客服回答：{qa_pair['answer']}",
                'content_type': 'dialog_qa',
                'keywords': self._extract_keywords(qa_pair['question'] + ' ' + qa_pair['answer']),
                'category_path': f"客服对话/{dialog_data.get('metadata', {}).get('consultation_type', 'general')}",
                'metadata': {
                    'dialog_id': dialog_id,
                    'qa_index': i + 1,
                    'consultation_type': dialog_data.get('metadata', {}).get('consultation_type', ''),
                    'question_type': self._classify_question_type(qa_pair['question'])
                }
            }
            chunks.append(chunk)
        
        return chunks
    
    def _safe_extract_number(self, value: Any) -> float:
        """安全提取数字值"""
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            # 移除非数字字符，提取数字
            match = re.search(r'(\d+(?:\.\d+)?)', value)
            if match:
                return float(match.group(1))
        return 0.0
    
    def _create_product_basic_chunk(self, product_data: Dict[str, Any], product_id: str) -> Dict[str, Any]:
        """创建产品基本信息块"""
        metadata = product_data.get('metadata', {})
        content_parts = []
        
        content_parts.append(f"产品名称：{metadata.get('name', 'N/A')}")
        content_parts.append(f"产品类型：{metadata.get('type', 'N/A')}")
        content_parts.append(f"发行机构：{metadata.get('institution', 'N/A')}")
        
        if 'interest_rate' in metadata:
            rate = metadata['interest_rate']
            content_parts.append(f"年化利率：{rate.get('min', 0)}%-{rate.get('max', 0)}%")
        
        content = "，".join(content_parts)
        
        return {
            'chunk_id': f"{product_id}_BASIC",
            'content': content,
            'content_type': 'product_basic_info',
            'keywords': self._extract_keywords(content),
            'category_path': f"金融产品/{metadata.get('type', 'unknown')}/{metadata.get('name', 'unknown')}",
            'metadata': {
                'product_id': product_id,
                'product_type': metadata.get('type', ''),
                'institution': metadata.get('institution', ''),
                'chunk_type': 'basic_info'
            }
        }
    
    def _create_product_requirements_chunk(self, product_data: Dict[str, Any], product_id: str) -> Dict[str, Any]:
        """创建产品申请条件块"""
        metadata = product_data.get('metadata', {})
        requirements = metadata.get('requirements', {})
        
        content_parts = []
        for key, value in requirements.items():
            if key == 'credit_score':
                content_parts.append(f"征信要求：{value}")
            elif key == 'monthly_income':
                content_parts.append(f"收入要求：{value}")
            elif key == 'age_range':
                content_parts.append(f"年龄要求：{value}")
            elif key == 'employment_years':
                content_parts.append(f"工作年限：{value}")
            else:
                content_parts.append(f"{key}：{value}")
        
        content = "，".join(content_parts)
        
        return {
            'chunk_id': f"{product_id}_REQ",
            'content': f"申请条件：{content}",
            'content_type': 'product_requirements',
            'keywords': self._extract_keywords(content) + ['申请条件', '要求'],
            'category_path': f"金融产品/{metadata.get('type', 'unknown')}/申请条件",
            'metadata': {
                'product_id': product_id,
                'chunk_type': 'requirements',
                'requirements': requirements
            }
        }
    
    def _create_product_features_chunk(self, product_data: Dict[str, Any], product_id: str) -> Dict[str, Any]:
        """创建产品特征块"""
        metadata = product_data.get('metadata', {})
        features = metadata.get('features', {})
        
        content_parts = []
        for key, value in features.items():
            if key == 'amount_range':
                content_parts.append(f"贷款额度：{value}")
            elif key == 'term_range':
                content_parts.append(f"贷款期限：{value}")
            elif key == 'repayment_method':
                if isinstance(value, list):
                    content_parts.append(f"还款方式：{'/'.join(value)}")
                else:
                    content_parts.append(f"还款方式：{value}")
            else:
                content_parts.append(f"{key}：{value}")
        
        content = "，".join(content_parts)
        
        return {
            'chunk_id': f"{product_id}_FEAT",
            'content': f"产品特征：{content}",
            'content_type': 'product_features',
            'keywords': self._extract_keywords(content) + ['产品特征', '额度', '期限'],
            'category_path': f"金融产品/{metadata.get('type', 'unknown')}/产品特征",
            'metadata': {
                'product_id': product_id,
                'chunk_type': 'features',
                'features': features
            }
        }
    
    def _create_case_profile_chunk(self, case_data: Dict[str, Any], case_id: str) -> Dict[str, Any]:
        """创建案例客户画像块"""
        metadata = case_data.get('metadata', {})
        profile = metadata.get('customer_profile', {})
        
        content = f"客户画像：{profile.get('age', 'N/A')}岁{profile.get('occupation', 'N/A')}，" \
                 f"月收入{profile.get('monthly_income', 'N/A')}元，" \
                 f"征信评分{profile.get('credit_score', 'N/A')}分，" \
                 f"工作{profile.get('employment_years', 'N/A')}年"
        
        return {
            'chunk_id': f"{case_id}_PROFILE",
            'content': content,
            'content_type': 'case_profile',
            'keywords': self._extract_keywords(content) + ['客户画像', '案例'],
            'category_path': f"客户案例/客户画像",
            'metadata': {
                'case_id': case_id,
                'chunk_type': 'profile',
                'customer_profile': profile
            }
        }
    
    def _create_case_application_chunk(self, case_data: Dict[str, Any], case_id: str) -> Dict[str, Any]:
        """创建案例申请信息块"""
        metadata = case_data.get('metadata', {})
        application = metadata.get('application', {})
        
        content = f"申请信息：申请金额{application.get('amount', 'N/A')}元，" \
                 f"用途{application.get('purpose', 'N/A')}，" \
                 f"期限{application.get('term', 'N/A')}"
        
        return {
            'chunk_id': f"{case_id}_APP",
            'content': content,
            'content_type': 'case_application',
            'keywords': self._extract_keywords(content) + ['申请信息', '申请金额'],
            'category_path': f"客户案例/申请信息",
            'metadata': {
                'case_id': case_id,
                'chunk_type': 'application',
                'application': application
            }
        }
    
    def _create_case_result_chunk(self, case_data: Dict[str, Any], case_id: str) -> Dict[str, Any]:
        """创建案例审批结果块"""
        metadata = case_data.get('metadata', {})
        result = metadata.get('approval_result', {})
        
        if result.get('status') == '批准':
            content = f"审批结果：批准，批准金额{result.get('approved_amount', 'N/A')}元，" \
                     f"最终利率{result.get('final_rate', 'N/A')}%，" \
                     f"批准理由：{result.get('approval_reason', 'N/A')}"
        else:
            content = f"审批结果：拒绝，拒绝理由：{result.get('rejection_reason', 'N/A')}"
        
        return {
            'chunk_id': f"{case_id}_RESULT",
            'content': content,
            'content_type': 'case_result',
            'keywords': self._extract_keywords(content) + ['审批结果', '批准', '拒绝'],
            'category_path': f"客户案例/审批结果",
            'metadata': {
                'case_id': case_id,
                'chunk_type': 'result',
                'approval_result': result
            }
        }
    
    def _create_case_analysis_chunk(self, case_data: Dict[str, Any], case_id: str) -> Dict[str, Any]:
        """创建案例综合分析块"""
        metadata = case_data.get('metadata', {})
        
        # 综合案例信息
        profile = metadata.get('customer_profile', {})
        application = metadata.get('application', {})
        result = metadata.get('approval_result', {})
        
        risk_level = self._assess_risk_level(profile)
        success_factors = self._identify_success_factors(profile, result)
        
        content = f"案例分析：{profile.get('age', 'N/A')}岁{profile.get('occupation', 'N/A')}，" \
                 f"申请{application.get('amount', 'N/A')}元，" \
                 f"风险等级：{risk_level}，" \
                 f"关键因素：{'/'.join(success_factors)}，" \
                 f"最终结果：{result.get('status', 'N/A')}"
        
        return {
            'chunk_id': f"{case_id}_ANALYSIS",
            'content': content,
            'content_type': 'case_analysis',
            'keywords': self._extract_keywords(content) + ['案例分析', risk_level] + success_factors,
            'category_path': f"客户案例/案例分析",
            'metadata': {
                'case_id': case_id,
                'chunk_type': 'analysis',
                'risk_level': risk_level,
                'success_factors': success_factors
            }
        }
    
    def _split_by_paragraphs(self, text: str) -> List[str]:
        """按段落切分文本"""
        # 按双换行符切分
        paragraphs = re.split(r'\n\s*\n', text)
        
        # 进一步按句号切分长段落
        result = []
        for para in paragraphs:
            if len(para) > self.max_chunk_size:
                sentences = re.split(r'[。！？]', para)
                current_chunk = ""
                for sentence in sentences:
                    if len(current_chunk + sentence) > self.max_chunk_size:
                        if current_chunk:
                            result.append(current_chunk.strip())
                        current_chunk = sentence
                    else:
                        current_chunk += sentence + "。"
                if current_chunk:
                    result.append(current_chunk.strip())
            else:
                result.append(para.strip())
        
        return [p for p in result if p]
    
    def _extract_qa_pairs(self, messages: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """提取问答对"""
        qa_pairs = []
        
        for i in range(len(messages) - 1):
            if messages[i]['role'] == 'customer' and messages[i+1]['role'] == 'advisor':
                qa_pairs.append({
                    'question': messages[i]['content'],
                    'answer': messages[i+1]['content']
                })
        
        return qa_pairs
    
    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取
        financial_terms = [
            '贷款', '利率', '额度', '期限', '征信', '收入', '申请', '审批',
            '银行', '金融', '风险', '担保', '抵押', '信用', '还款', '逾期'
        ]
        
        keywords = []
        for term in financial_terms:
            if term in text:
                keywords.append(term)
        
        return list(set(keywords))
    
    def _assess_risk_level(self, profile: Dict[str, Any]) -> str:
        """评估风险等级"""
        credit_score = self._safe_extract_number(profile.get('credit_score', 600))
        monthly_income = self._safe_extract_number(profile.get('monthly_income', 5000))
        employment_years = self._safe_extract_number(profile.get('employment_years', 1))
        
        score = 0
        if credit_score >= 750:
            score += 3
        elif credit_score >= 650:
            score += 2
        elif credit_score >= 600:
            score += 1
        
        if monthly_income >= 10000:
            score += 2
        elif monthly_income >= 5000:
            score += 1
        
        if employment_years >= 2:
            score += 1
        
        if score >= 5:
            return "低风险"
        elif score >= 3:
            return "中风险"
        else:
            return "高风险"
    
    def _identify_success_factors(self, profile: Dict[str, Any], result: Dict[str, Any]) -> List[str]:
        """识别成功因素"""
        factors = []
        
        credit_score = self._safe_extract_number(profile.get('credit_score', 0))
        if credit_score >= 700:
            factors.append("征信良好")
        
        monthly_income = self._safe_extract_number(profile.get('monthly_income', 0))
        if monthly_income >= 8000:
            factors.append("收入稳定")
        
        employment_years = self._safe_extract_number(profile.get('employment_years', 0))
        if employment_years >= 2:
            factors.append("工作稳定")
        
        if result.get('status') == '批准':
            factors.append("审批通过")
        
        return factors
    
    def _classify_question_type(self, question: str) -> str:
        """分类问题类型"""
        if any(word in question for word in ['利率', '费率', '成本']):
            return "利率咨询"
        elif any(word in question for word in ['条件', '要求', '资格']):
            return "条件咨询"
        elif any(word in question for word in ['额度', '金额', '多少钱']):
            return "额度咨询"
        elif any(word in question for word in ['流程', '步骤', '怎么申请']):
            return "流程咨询"
        else:
            return "一般咨询"

if __name__ == "__main__":
    splitter = ChunkSplitter()
    
    # 测试产品切分
    test_product = {
        'title': '工商银行个人消费贷',
        'content': '工商银行个人消费贷款产品介绍...',
        'metadata': {
            'product_id': 'LOAN001',
            'name': '个人消费贷',
            'type': '消费贷',
            'institution': '工商银行',
            'interest_rate': {'min': 4.35, 'max': 18.0},
            'features': {'amount_range': '1-50万', 'term_range': '6-60个月'},
            'requirements': {'credit_score': '≥650', 'monthly_income': '≥5000'}
        }
    }
    
    chunks = splitter.split_product(test_product)
    print(f"产品切分结果：{len(chunks)} 个块")
    for chunk in chunks:
        print(f"- {chunk['chunk_id']}: {chunk['content'][:50]}...")
        print(f"  关键词: {chunk['keywords']}")