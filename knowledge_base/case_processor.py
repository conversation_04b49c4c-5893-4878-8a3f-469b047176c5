# knowledge_base/case_processor.py
import json
import re
from typing import Dict, List, Any

class CaseProcessor:
    """客户案例处理器"""
    
    def __init__(self):
        self.required_fields = ['case_id', 'customer_profile', 'application', 'approval_result']
    
    def process(self, case_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理案例数据"""
        try:
            # 验证必要字段
            self._validate_case_data(case_data)
            
            # 生成标题
            title = self._generate_title(case_data)
            
            # 生成内容
            content = self._generate_content(case_data)
            
            # 提取元数据
            metadata = self._extract_metadata(case_data)
            
            return {
                'title': title,
                'content': content,
                'metadata': metadata
            }
            
        except Exception as e:
            raise Exception(f"处理案例数据失败: {e}")
    
    def _validate_case_data(self, data: Dict[str, Any]):
        """验证案例数据"""
        for field in self.required_fields:
            if field not in data:
                raise ValueError(f"缺少必要字段: {field}")
    
    def _generate_title(self, data: Dict[str, Any]) -> str:
        """生成案例标题"""
        case_id = data.get('case_id', '')
        profile = data.get('customer_profile', {})
        application = data.get('application', {})
        result = data.get('approval_result', {})
        
        age = profile.get('age', 'N/A')
        occupation = profile.get('occupation', 'N/A')
        amount = application.get('amount', 'N/A')
        status = result.get('status', 'N/A')
        
        return f"案例{case_id}：{age}岁{occupation}申请{amount}元贷款{status}"
    
    def _generate_content(self, data: Dict[str, Any]) -> str:
        """生成案例内容描述"""
        content_parts = []
        
        # 客户基本信息
        profile = data.get('customer_profile', {})
        profile_desc = self._generate_profile_description(profile)
        content_parts.append(f"客户画像：{profile_desc}")
        
        # 申请信息
        application = data.get('application', {})
        app_desc = self._generate_application_description(application)
        content_parts.append(f"申请信息：{app_desc}")
        
        # 审批结果
        result = data.get('approval_result', {})
        result_desc = self._generate_result_description(result)
        content_parts.append(f"审批结果：{result_desc}")
        
        # 案例分析
        analysis = self._generate_case_analysis(data)
        content_parts.append(f"案例分析：{analysis}")
        
        return "。".join(content_parts) + "。"
    
    def _safe_extract_number(self, value: Any) -> float:
        """安全提取数字值"""
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            # 移除非数字字符，提取数字
            match = re.search(r'(\d+(?:\.\d+)?)', value)
            if match:
                return float(match.group(1))
        return 0.0
    
    def _safe_extract_percentage(self, value: Any) -> float:
        """安全提取百分比值"""
        if isinstance(value, str):
            if '%' in value:
                # 移除百分号并转换
                cleaned = value.replace('%', '').strip()
                try:
                    return float(cleaned) / 100
                except ValueError:
                    return 0.0
            else:
                # 提取数字部分
                match = re.search(r'(\d+(?:\.\d+)?)', value)
                if match:
                    num = float(match.group(1))
                    # 如果数字大于1，假设是百分比形式
                    if num > 1:
                        return num / 100
                    else:
                        return num
        elif isinstance(value, (int, float)):
            if 0 <= value <= 1:
                return value
            else:
                return value / 100
        return 0.0
    
    def _generate_profile_description(self, profile: Dict[str, Any]) -> str:
        """生成客户画像描述"""
        desc_parts = []
        
        age = profile.get('age', 'N/A')
        occupation = profile.get('occupation', 'N/A')
        desc_parts.append(f"{age}岁{occupation}")
        
        monthly_income = profile.get('monthly_income', 0)
        if monthly_income:
            # 安全提取收入数字
            income_num = self._safe_extract_number(monthly_income)
            desc_parts.append(f"月收入{income_num:.0f}元")
        
        credit_score = profile.get('credit_score', 0)
        if credit_score:
            # 安全提取征信分数
            score_num = self._safe_extract_number(credit_score)
            desc_parts.append(f"征信评分{score_num:.0f}分")
        
        employment_years = profile.get('employment_years', 0)
        if employment_years:
            # 安全提取工作年限
            years_num = self._safe_extract_number(employment_years)
            desc_parts.append(f"工作{years_num:.0f}年")
        
        debt_to_income = profile.get('debt_to_income', 0)
        if debt_to_income:
            # 安全提取负债率
            debt_ratio = self._safe_extract_percentage(debt_to_income)
            desc_parts.append(f"负债率{debt_ratio:.1%}")
        
        education = profile.get('education', '')
        if education:
            desc_parts.append(f"{education}学历")
        
        return "，".join(desc_parts)
    
    def _generate_application_description(self, application: Dict[str, Any]) -> str:
        """生成申请信息描述"""
        desc_parts = []
        
        amount = application.get('amount', 0)
        if amount:
            amount_num = self._safe_extract_number(amount)
            desc_parts.append(f"申请金额{amount_num:.0f}元")
        
        purpose = application.get('purpose', '')
        if purpose:
            desc_parts.append(f"用途{purpose}")
        
        term = application.get('term', '')
        if term:
            desc_parts.append(f"期限{term}")
        
        urgency = application.get('urgency', '')
        if urgency:
            desc_parts.append(f"紧急程度{urgency}")
        
        return "，".join(desc_parts)
    
    def _generate_result_description(self, result: Dict[str, Any]) -> str:
        """生成审批结果描述"""
        status = result.get('status', '')
        
        if status == '批准':
            desc_parts = ['审批通过']
            
            reason = result.get('reason', '')
            if reason:
                desc_parts.append(f"批准理由：{reason}")
            
            conditions = result.get('conditions', [])
            if conditions and isinstance(conditions, list):
                desc_parts.append(f"附加条件：{'/'.join(conditions)}")
            
        else:
            desc_parts = [f"审批结果：{status}"]
            
            reason = result.get('reason', '')
            if reason:
                desc_parts.append(f"原因：{reason}")
        
        return "，".join(desc_parts)
    
    def _generate_case_analysis(self, data: Dict[str, Any]) -> str:
        """生成案例分析"""
        analysis_parts = []
        
        # 风险评估
        risk_level = self._assess_customer_risk(data.get('customer_profile', {}))
        analysis_parts.append(f"风险等级{risk_level}")
        
        # 成功因素分析
        success_factors = self._identify_success_factors(data)
        if success_factors:
            analysis_parts.append(f"关键因素：{'/'.join(success_factors)}")
        
        # 可比性分析
        comparable_profiles = self._identify_comparable_profiles(data.get('customer_profile', {}))
        if comparable_profiles:
            analysis_parts.append(f"可比客户：{'/'.join(comparable_profiles)}")
        
        # 成功率预估
        success_probability = self._estimate_success_probability(data)
        analysis_parts.append(f"预估成功率{success_probability}%")
        
        return "，".join(analysis_parts)
    
    def _assess_customer_risk(self, profile: Dict[str, Any]) -> str:
        """评估客户风险等级"""
        score = 0
        
        # 征信评分
        credit_score = self._safe_extract_number(profile.get('credit_score', 600))
        if credit_score >= 750:
            score += 3
        elif credit_score >= 650:
            score += 2
        elif credit_score >= 600:
            score += 1
        
        # 收入水平
        monthly_income = self._safe_extract_number(profile.get('monthly_income', 5000))
        if monthly_income >= 15000:
            score += 3
        elif monthly_income >= 10000:
            score += 2
        elif monthly_income >= 5000:
            score += 1
        
        # 工作稳定性
        employment_years = self._safe_extract_number(profile.get('employment_years', 1))
        if employment_years >= 3:
            score += 2
        elif employment_years >= 1:
            score += 1
        
        # 负债率
        debt_to_income = self._safe_extract_percentage(profile.get('debt_to_income', 0.5))
        if debt_to_income <= 0.3:
            score += 2
        elif debt_to_income <= 0.5:
            score += 1
        
        # 年龄
        age = self._safe_extract_number(profile.get('age', 30))
        if 25 <= age <= 45:
            score += 1
        
        if score >= 8:
            return "低风险"
        elif score >= 5:
            return "中风险"
        else:
            return "高风险"
    
    def _identify_success_factors(self, data: Dict[str, Any]) -> List[str]:
        """识别成功因素"""
        factors = []
        profile = data.get('customer_profile', {})
        result = data.get('approval_result', {})
        
        # 正面因素
        credit_score = self._safe_extract_number(profile.get('credit_score', 0))
        if credit_score >= 700:
            factors.append("征信优良")
        
        monthly_income = self._safe_extract_number(profile.get('monthly_income', 0))
        if monthly_income >= 10000:
            factors.append("收入稳定")
        
        employment_years = self._safe_extract_number(profile.get('employment_years', 0))
        if employment_years >= 2:
            factors.append("工作稳定")
        
        debt_to_income = self._safe_extract_percentage(profile.get('debt_to_income', 1))
        if debt_to_income <= 0.3:
            factors.append("负债率低")
        
        occupation = profile.get('occupation', '')
        if occupation in ['公务员', '教师', '医生', '银行员工']:
            factors.append("职业稳定")
        
        education = profile.get('education', '')
        if education in ['本科', '硕士', '博士']:
            factors.append("学历较高")
        
        # 结果相关因素
        if result.get('status') == '批准':
            factors.append("审批通过")
        
        return factors
    
    def _identify_comparable_profiles(self, profile: Dict[str, Any]) -> List[str]:
        """识别可比客户群体"""
        comparable = []
        
        occupation = profile.get('occupation', '')
        if occupation:
            comparable.append(f"{occupation}群体")
        
        age = self._safe_extract_number(profile.get('age', 0))
        if 25 <= age <= 35:
            comparable.append("年轻上班族")
        elif 35 < age <= 45:
            comparable.append("中年群体")
        elif age > 45:
            comparable.append("中老年群体")
        
        monthly_income = self._safe_extract_number(profile.get('monthly_income', 0))
        if monthly_income >= 15000:
            comparable.append("高收入群体")
        elif monthly_income >= 8000:
            comparable.append("中高收入群体")
        elif monthly_income >= 5000:
            comparable.append("中等收入群体")
        
        education = profile.get('education', '')
        if education in ['硕士', '博士']:
            comparable.append("高学历群体")
        elif education == '本科':
            comparable.append("本科学历群体")
        
        return comparable
    
    def _estimate_success_probability(self, data: Dict[str, Any]) -> int:
        """估算成功概率"""
        profile = data.get('customer_profile', {})
        application = data.get('application', {})
        result = data.get('approval_result', {})
        
        # 如果已知结果，基于结果调整
        if result.get('status') == '批准':
            base_prob = 85
        elif result.get('status') == '拒绝':
            base_prob = 15
        else:
            # 基于客户特征估算
            score = 0
            
            credit_score = self._safe_extract_number(profile.get('credit_score', 600))
            if credit_score >= 750:
                score += 30
            elif credit_score >= 700:
                score += 25
            elif credit_score >= 650:
                score += 20
            elif credit_score >= 600:
                score += 10
            
            monthly_income = self._safe_extract_number(profile.get('monthly_income', 5000))
            amount = self._safe_extract_number(application.get('amount', 100000))
            income_ratio = amount / (monthly_income * 12) if monthly_income > 0 else 10
            
            if income_ratio <= 3:
                score += 25
            elif income_ratio <= 5:
                score += 15
            elif income_ratio <= 8:
                score += 5
            
            employment_years = self._safe_extract_number(profile.get('employment_years', 1))
            if employment_years >= 2:
                score += 15
            elif employment_years >= 1:
                score += 10
            
            debt_to_income = self._safe_extract_percentage(profile.get('debt_to_income', 0.5))
            if debt_to_income <= 0.3:
                score += 15
            elif debt_to_income <= 0.5:
                score += 10
            
            base_prob = min(score, 95)
        
        return max(5, min(95, base_prob))
    
    def _extract_metadata(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取案例元数据"""
        metadata = {}
        
        # 复制原始数据
        for key, value in data.items():
            metadata[key] = value
        
        # 添加处理后的字段
        metadata['processed'] = True
        metadata['content_type'] = 'customer_case'
        
        # 风险评估
        profile = data.get('customer_profile', {})
        metadata['risk_level'] = self._assess_customer_risk(profile)
        metadata['success_factors'] = self._identify_success_factors(data)
        metadata['comparable_profiles'] = self._identify_comparable_profiles(profile)
        metadata['success_probability'] = self._estimate_success_probability(data)
        
        # 客户分类
        metadata['customer_category'] = self._categorize_customer(profile)
        
        # 案例类型
        metadata['case_type'] = self._classify_case_type(data)
        
        return metadata
    
    def _categorize_customer(self, profile: Dict[str, Any]) -> str:
        """客户分类"""
        credit_score = self._safe_extract_number(profile.get('credit_score', 600))
        monthly_income = self._safe_extract_number(profile.get('monthly_income', 5000))
        
        if credit_score >= 750 and monthly_income >= 15000:
            return "优质客户"
        elif credit_score >= 700 and monthly_income >= 10000:
            return "优良客户"
        elif credit_score >= 650 and monthly_income >= 5000:
            return "标准客户"
        else:
            return "风险客户"
    
    def _classify_case_type(self, data: Dict[str, Any]) -> str:
        """案例类型分类"""
        result = data.get('approval_result', {})
        profile = data.get('customer_profile', {})
        
        if result.get('status') == '批准':
            credit_score = self._safe_extract_number(profile.get('credit_score', 0))
            if credit_score >= 750:
                return "优质客户成功案例"
            else:
                return "标准客户成功案例"
        elif result.get('status') == '拒绝':
            rejection_reason = result.get('reason', '')
            if '征信' in rejection_reason:
                return "征信问题拒绝案例"
            elif '收入' in rejection_reason:
                return "收入不足拒绝案例"
            else:
                return "其他拒绝案例"
        
        return "一般案例"

if __name__ == "__main__":
    # 测试案例处理器
    processor = CaseProcessor()
    
    test_case = {
        "case_id": "CASE001",
        "customer_profile": {
            "age": 32,
            "occupation": "软件工程师",
            "monthly_income": "15000",
            "credit_score": "720",
            "debt_to_income": "0.3",
            "employment_years": "5",
            "education": "本科"
        },
        "application": {
            "amount": "200000",
            "purpose": "装修房屋",
            "term": "3年"
        },
        "approval_result": {
            "status": "批准",
            "reason": "收入稳定，征信良好，负债率合理",
            "conditions": ["需提供装修合同", "按月还款"]
        }
    }
    
    try:
        result = processor.process(test_case)
        print("处理结果:")
        print(f"标题: {result['title']}")
        print(f"内容: {result['content'][:200]}...")
        print(f"风险等级: {result['metadata']['risk_level']}")
        print(f"客户分类: {result['metadata']['customer_category']}")
        print(f"成功因素: {result['metadata']['success_factors']}")
        print(f"成功概率: {result['metadata']['success_probability']}%")
    except Exception as e:
        print(f"处理失败: {e}")