# knowledge_base/dialogue_processor.py
import json
import re
from typing import Dict, List, Any, Optional, Tuple
try:
    from .chunk_splitter import ChunkSplitter
except Exception as e:
    from chunk_splitter import ChunkSplitter

class DialogueProcessor:
    """对话处理器"""
    
    def __init__(self):
        self.chunk_splitter = ChunkSplitter()
        
        # 对话类型分类
        self.consultation_types = {
            "产品咨询": ["产品介绍", "产品特点", "产品对比", "产品选择"],
            "申请咨询": ["申请条件", "申请流程", "申请材料", "申请进度"],
            "利率咨询": ["利率查询", "利率计算", "利率优惠", "利率调整"],
            "额度咨询": ["额度评估", "额度提升", "额度查询", "额度限制"],
            "还款咨询": ["还款方式", "还款计划", "提前还款", "逾期处理"],
            "问题处理": ["账户问题", "技术问题", "投诉处理", "异议处理"]
        }
        
        # 问题类型识别关键词
        self.problem_keywords = {
            "征信问题": ["征信", "信用记录", "逾期", "黑名单", "信用报告"],
            "收入问题": ["收入", "工资", "流水", "证明", "收入不足"],
            "资产问题": ["房产", "车辆", "存款", "投资", "资产证明"],
            "额度问题": ["额度", "限额", "上限", "下限", "提额"],
            "利率问题": ["利率", "费率", "成本", "利息", "优惠"],
            "流程问题": ["流程", "步骤", "怎么办", "如何", "程序"],
            "条件问题": ["条件", "要求", "资格", "门槛", "标准"],
            "时间问题": ["多久", "时间", "期限", "何时", "什么时候"]
        }
        
        # 情绪词典
        self.emotion_keywords = {
            "积极": ["满意", "好的", "谢谢", "不错", "很好", "赞"],
            "消极": ["不满", "失望", "糟糕", "差", "问题", "投诉"],
            "焦虑": ["着急", "紧急", "快点", "尽快", "担心", "焦虑"],
            "疑惑": ["不懂", "不明白", "不清楚", "疑问", "困惑"]
        }
    
    def process_dialogue_data(self, dialogues: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """处理对话数据"""
        processed_chunks = []
        
        for dialogue in dialogues:
            chunks = self._process_single_dialogue(dialogue)
            processed_chunks.extend(chunks)
        
        return processed_chunks
    
    def _process_single_dialogue(self, dialogue: Dict[str, Any]) -> List[Dict[str, Any]]:
        """处理单个对话"""
        chunks = []
        
        try:
            # 检测对话数据格式
            if 'output' in dialogue and isinstance(dialogue['output'], list):
                # 多轮对话格式
                dialogue_info = self._convert_multi_turn_format(dialogue)
            elif 'input' in dialogue and 'output' in dialogue:
                # 咨询问答格式
                dialogue_info = self._convert_consultation_format(dialogue)
            else:
                # 标准格式
                dialogue_info = dialogue
            
            # 提取对话特征
            dialogue_features = self._extract_dialogue_features(dialogue_info)
            
            # 生成不同粒度的块
            # 1. 完整对话块
            full_dialogue_chunk = self._create_full_dialogue_chunk(dialogue_info, dialogue_features)
            chunks.append(full_dialogue_chunk)
            
            # 2. QA对话块
            qa_chunks = self._create_qa_chunks(dialogue_info, dialogue_features)
            chunks.extend(qa_chunks)
            
            # 3. 问题解决方案块
            solution_chunks = self._create_solution_chunks(dialogue_info, dialogue_features)
            chunks.extend(solution_chunks)
            
            # 4. 标准话术块
            template_chunks = self._create_template_chunks(dialogue_info, dialogue_features)
            chunks.extend(template_chunks)
            
        except Exception as e:
            print(f"处理对话 {dialogue.get('dialog_id', 'unknown')} 失败: {e}")
        
        return chunks
    
    def _convert_multi_turn_format(self, dialogue: Dict[str, Any]) -> Dict[str, Any]:
        """转换多轮对话格式"""
        messages = dialogue.get('output', [])
        metadata = dialogue.get('metadata', {})
        
        # 生成对话ID
        dialog_id = f"DIALOG_{hash(str(dialogue))}"
        
        return {
            "dialog_id": dialog_id,
            "consultation_type": metadata.get('scenario_type', '一般咨询'),
            "messages": messages,
            "consultation_result": {
                "problem_solved": True,
                "satisfaction_score": 4.0
            }
        }
    
    def _convert_consultation_format(self, dialogue: Dict[str, Any]) -> Dict[str, Any]:
        """转换咨询问答格式"""
        input_text = dialogue.get('input', '').strip('[]')
        output_text = dialogue.get('output', '').strip('[]')
        metadata = dialogue.get('metadata', {})
        
        # 生成对话ID
        dialog_id = f"CONSULT_{hash(input_text + output_text)}"
        
        messages = [
            {"role": "customer", "content": input_text},
            {"role": "advisor", "content": output_text}
        ]
        
        return {
            "dialog_id": dialog_id,
            "consultation_type": metadata.get('consultation_type', '一般咨询'),
            "messages": messages,
            "consultation_result": {
                "problem_solved": True,
                "satisfaction_score": 4.0
            }
        }
    
    def _extract_dialogue_features(self, dialogue: Dict[str, Any]) -> Dict[str, Any]:
        """提取对话特征"""
        messages = dialogue.get("messages", [])
        consultation_type = dialogue.get("consultation_type", "")
        
        features = {
            "message_count": len(messages),
            "customer_questions": [],
            "advisor_responses": [],
            "problem_types": [],
            "emotions": [],
            "satisfaction_indicators": [],
            "resolution_status": dialogue.get("consultation_result", {}).get("problem_solved", False)
        }
        
        # 分析每条消息
        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")
            
            if role == "customer":
                features["customer_questions"].append(content)
                # 识别问题类型
                problem_types = self._identify_problem_types(content)
                features["problem_types"].extend(problem_types)
                # 识别情绪
                emotion = self._identify_emotion(content)
                if emotion:
                    features["emotions"].append(emotion)
            
            elif role == "advisor":
                features["advisor_responses"].append(content)
                # 识别满意度指标
                satisfaction = self._identify_satisfaction_indicators(content)
                features["satisfaction_indicators"].extend(satisfaction)
        
        # 去重
        features["problem_types"] = list(set(features["problem_types"]))
        features["emotions"] = list(set(features["emotions"]))
        
        # 分析对话质量
        features["dialogue_quality"] = self._assess_dialogue_quality(features)
        
        return features
    
    def _create_full_dialogue_chunk(self, dialogue: Dict[str, Any], 
                                   features: Dict[str, Any]) -> Dict[str, Any]:
        """创建完整对话块"""
        dialogue_id = dialogue.get("dialog_id", "")
        messages = dialogue.get("messages", [])
        
        # 构建对话内容
        dialogue_text = self._format_dialogue_text(messages)
        
        # 提取关键词
        keywords = self._extract_dialogue_keywords(dialogue, features)
        
        chunk = {
            "chunk_id": f"{dialogue_id}_FULL",
            "content_type": "dialog_full",
            "content": f"完整对话记录：{dialogue_text}",
            "metadata": {
                "dialog_id": dialogue_id,
                "consultation_type": dialogue.get("consultation_type", ""),
                "message_count": features["message_count"],
                "problem_types": features["problem_types"],
                "emotions": features["emotions"],
                "resolution_status": features["resolution_status"],
                "dialogue_quality": features["dialogue_quality"],
                "customer_satisfaction": dialogue.get("consultation_result", {}).get("satisfaction_score", 0)
            },
            "keywords": keywords,
            "category_path": f"客服对话/完整对话/{dialogue.get('consultation_type', '一般咨询')}"
        }
        
        return chunk
    
    def _create_qa_chunks(self, dialogue: Dict[str, Any], 
                         features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建QA对话块"""
        chunks = []
        messages = dialogue.get("messages", [])
        dialogue_id = dialogue.get("dialog_id", "")
        
        # 配对客户问题和顾问回答
        qa_pairs = self._extract_qa_pairs(messages)
        
        for i, (question, answer) in enumerate(qa_pairs):
            if question and answer:
                # 分析QA质量
                qa_quality = self._assess_qa_quality(question, answer)
                
                # 识别问题类型
                problem_type = self._identify_problem_types(question)
                
                chunk = {
                    "chunk_id": f"{dialogue_id}_QA_{i+1:02d}",
                    "content_type": "dialog_qa",
                    "content": f"客户问题：{question}\n专业解答：{answer}",
                    "metadata": {
                        "dialog_id": dialogue_id,
                        "qa_pair_id": i + 1,
                        "question_type": problem_type[0] if problem_type else "一般咨询",
                        "problem_types": problem_type,
                        "qa_quality": qa_quality,
                        "question_length": len(question),
                        "answer_length": len(answer)
                    },
                    "keywords": self._extract_qa_keywords(question, answer),
                    "category_path": f"客服对话/问答对/{problem_type[0] if problem_type else '一般咨询'}"
                }
                
                chunks.append(chunk)
        
        return chunks
    
    def _create_solution_chunks(self, dialogue: Dict[str, Any], 
                              features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建问题解决方案块"""
        chunks = []
        
        if not features["resolution_status"]:
            return chunks
        
        dialogue_id = dialogue.get("dialog_id", "")
        
        # 按问题类型组织解决方案
        for problem_type in features["problem_types"]:
            solution_content = self._extract_solution_for_problem(dialogue, problem_type)
            
            if solution_content:
                chunk = {
                    "chunk_id": f"{dialogue_id}_SOLUTION_{problem_type}",
                    "content_type": "dialog_solution",
                    "content": f"{problem_type}解决方案：{solution_content}",
                    "metadata": {
                        "dialog_id": dialogue_id,
                        "problem_type": problem_type,
                        "solution_effectiveness": "已验证有效",
                        "applicable_scenarios": self._identify_applicable_scenarios(problem_type)
                    },
                    "keywords": [problem_type, "解决方案", "处理方法"],
                    "category_path": f"客服对话/解决方案/{problem_type}"
                }
                
                chunks.append(chunk)
        
        return chunks
    
    def _create_template_chunks(self, dialogue: Dict[str, Any], 
                              features: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建标准话术块"""
        chunks = []
        messages = dialogue.get("messages", [])
        dialogue_id = dialogue.get("dialog_id", "")
        
        # 提取高质量的顾问回答作为话术模板
        quality_responses = []
        for msg in messages:
            if msg.get("role") == "advisor":
                content = msg.get("content", "")
                if self._is_quality_response(content):
                    quality_responses.append(content)
        
        # 为每个问题类型生成话术模板
        for problem_type in features["problem_types"]:
            relevant_responses = self._find_relevant_responses(quality_responses, problem_type)
            
            if relevant_responses:
                template_content = self._create_response_template(relevant_responses, problem_type)
                
                chunk = {
                    "chunk_id": f"{dialogue_id}_TEMPLATE_{problem_type}",
                    "content_type": "dialog_template",
                    "content": f"{problem_type}标准话术：{template_content}",
                    "metadata": {
                        "dialog_id": dialogue_id,
                        "template_type": problem_type,
                        "template_source": "优质对话提取",
                        "usage_scenarios": self._identify_template_scenarios(problem_type),
                        "effectiveness_rating": "高"
                    },
                    "keywords": [problem_type, "标准话术", "客服模板"],
                    "category_path": f"客服对话/标准话术/{problem_type}"
                }
                
                chunks.append(chunk)
        
        return chunks
    
    def _format_dialogue_text(self, messages: List[Dict[str, Any]]) -> str:
        """格式化对话文本"""
        formatted_lines = []
        
        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")
            
            role_name = "客户" if role == "customer" else "客服"
            formatted_lines.append(f"{role_name}：{content}")
        
        return "\n".join(formatted_lines)
    
    def _extract_dialogue_keywords(self, dialogue: Dict[str, Any], 
                                  features: Dict[str, Any]) -> List[str]:
        """提取对话关键词"""
        keywords = []
        
        # 基本关键词
        keywords.append(dialogue.get("consultation_type", ""))
        keywords.extend(features["problem_types"])
        
        # 从消息中提取关键词
        all_content = ""
        for msg in dialogue.get("messages", []):
            all_content += msg.get("content", "") + " "
        
        # 提取金融关键词
        financial_keywords = self._extract_financial_keywords(all_content)
        keywords.extend(financial_keywords)
        
        # 去重并过滤
        keywords = [kw for kw in set(keywords) if kw and len(kw) > 1]
        
        return keywords[:10]  # 限制关键词数量
    
    def _identify_problem_types(self, content: str) -> List[str]:
        """识别问题类型"""
        identified_types = []
        content_lower = content.lower()
        
        for problem_type, keywords in self.problem_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                identified_types.append(problem_type)
        
        return identified_types
    
    def _identify_emotion(self, content: str) -> Optional[str]:
        """识别情绪"""
        content_lower = content.lower()
        
        for emotion, keywords in self.emotion_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                return emotion
        
        return None
    
    def _identify_satisfaction_indicators(self, content: str) -> List[str]:
        """识别满意度指标"""
        indicators = []
        content_lower = content.lower()
        
        positive_indicators = ["感谢", "满意", "解决了", "明白了", "清楚了"]
        negative_indicators = ["不满意", "没解决", "不明白", "还是不懂"]
        
        for indicator in positive_indicators:
            if indicator in content_lower:
                indicators.append("正面反馈")
                break
        
        for indicator in negative_indicators:
            if indicator in content_lower:
                indicators.append("负面反馈")
                break
        
        return indicators
    
    def _assess_dialogue_quality(self, features: Dict[str, Any]) -> str:
        """评估对话质量"""
        score = 0
        
        # 消息数量适中
        msg_count = features["message_count"]
        if 4 <= msg_count <= 12:
            score += 2
        elif msg_count > 12:
            score += 1
        
        # 问题得到解决
        if features["resolution_status"]:
            score += 3
        
        # 情绪积极
        if "积极" in features["emotions"]:
            score += 2
        elif "消极" in features["emotions"]:
            score -= 1
        
        # 满意度指标
        if "正面反馈" in features["satisfaction_indicators"]:
            score += 2
        elif "负面反馈" in features["satisfaction_indicators"]:
            score -= 1
        
        if score >= 6:
            return "优秀"
        elif score >= 4:
            return "良好"
        elif score >= 2:
            return "一般"
        else:
            return "较差"
    
    def _extract_qa_pairs(self, messages: List[Dict[str, Any]]) -> List[Tuple[str, str]]:
        """提取QA对"""
        pairs = []
        current_question = None
        
        for msg in messages:
            role = msg.get("role", "")
            content = msg.get("content", "")
            
            if role == "customer":
                # 如果有未匹配的问题，先保存
                if current_question:
                    pairs.append((current_question, ""))
                current_question = content
            
            elif role == "advisor" and current_question:
                # 匹配问答对
                pairs.append((current_question, content))
                current_question = None
        
        # 处理最后一个未匹配的问题
        if current_question:
            pairs.append((current_question, ""))
        
        return pairs
    
    def _assess_qa_quality(self, question: str, answer: str) -> str:
        """评估QA质量"""
        score = 0
        
        # 答案长度合适
        if 20 <= len(answer) <= 200:
            score += 2
        elif len(answer) > 200:
            score += 1
        
        # 答案包含专业术语
        financial_terms = ["利率", "额度", "征信", "贷款", "申请", "审批"]
        if any(term in answer for term in financial_terms):
            score += 1
        
        # 答案结构化
        if any(marker in answer for marker in ["1.", "①", "首先", "其次", "最后"]):
            score += 1
        
        # 问题明确
        if any(word in question for word in ["什么", "如何", "怎么", "多少", "哪些"]):
            score += 1
        
        if score >= 4:
            return "优秀"
        elif score >= 3:
            return "良好"
        elif score >= 2:
            return "一般"
        else:
            return "较差"
    
    def _extract_qa_keywords(self, question: str, answer: str) -> List[str]:
        """提取QA关键词"""
        keywords = []
        
        # 从问题中提取
        question_keywords = self._extract_financial_keywords(question)
        keywords.extend(question_keywords)
        
        # 从答案中提取
        answer_keywords = self._extract_financial_keywords(answer)
        keywords.extend(answer_keywords)
        
        # 去重
        return list(set(keywords))[:8]
    
    def _extract_financial_keywords(self, text: str) -> List[str]:
        """提取金融关键词"""
        financial_terms = [
            "贷款", "利率", "额度", "征信", "申请", "审批", "银行", "金融",
            "消费贷", "房贷", "车贷", "抵押", "担保", "还款", "分期",
            "年化利率", "月利率", "本金", "利息", "手续费", "违约金"
        ]
        
        keywords = []
        for term in financial_terms:
            if term in text:
                keywords.append(term)
        
        return keywords
    
    def _extract_solution_for_problem(self, dialogue: Dict[str, Any], 
                                    problem_type: str) -> str:
        """提取问题解决方案"""
        messages = dialogue.get("messages", [])
        solution_parts = []
        
        for msg in messages:
            if msg.get("role") == "advisor":
                content = msg.get("content", "")
                # 检查是否包含解决方案相关内容
                if any(keyword in content for keyword in ["建议", "可以", "需要", "应该", "方法"]):
                    if any(keyword in content for keyword in self.problem_keywords.get(problem_type, [])):
                        solution_parts.append(content)
        
        return "；".join(solution_parts)
    
    def _identify_applicable_scenarios(self, problem_type: str) -> List[str]:
        """识别适用场景"""
        scenario_map = {
            "征信问题": ["征信有逾期", "征信空白", "征信查询过多"],
            "收入问题": ["收入不稳定", "收入证明不足", "收入与申请额度不匹配"],
            "额度问题": ["额度不够", "额度评估", "提升额度"],
            "利率问题": ["利率咨询", "利率优惠", "利率计算"],
            "流程问题": ["申请流程", "审批流程", "放款流程"],
            "条件问题": ["申请条件", "准入门槛", "资格审查"]
        }
        
        return scenario_map.get(problem_type, ["一般场景"])
    
    def _is_quality_response(self, content: str) -> bool:
        """判断是否为高质量回答"""
        # 长度合适
        if len(content) < 20 or len(content) > 300:
            return False
        
        # 包含专业信息
        professional_indicators = [
            "根据", "建议", "需要", "可以", "一般来说", "具体",
            "年化利率", "申请条件", "审批流程", "贷款额度"
        ]
        
        if not any(indicator in content for indicator in professional_indicators):
            return False
        
        return True
    
    def _find_relevant_responses(self, responses: List[str], problem_type: str) -> List[str]:
        """查找相关回答"""
        relevant = []
        problem_keywords = self.problem_keywords.get(problem_type, [])
        
        for response in responses:
            if any(keyword in response for keyword in problem_keywords):
                relevant.append(response)
        
        return relevant
    
    def _create_response_template(self, responses: List[str], problem_type: str) -> str:
        """创建回答模板"""
        if not responses:
            return f"针对{problem_type}的标准处理流程"
        
        # 选择最长且最完整的回答作为模板基础
        best_response = max(responses, key=len)
        
        # 简化模板化处理
        template = best_response.replace("您", "[客户]")
        template = template.replace("你", "[客户]")
        
        return template
    
    def _identify_template_scenarios(self, problem_type: str) -> List[str]:
        """识别模板适用场景"""
        return self._identify_applicable_scenarios(problem_type)

if __name__ == "__main__":
    # 测试对话处理器
    processor = DialogueProcessor()
    
    # 测试多轮对话数据
    test_multi_turn = {
        "output": [
            {"role": "customer", "content": "我想了解个人消费贷款的条件"},
            {"role": "advisor", "content": "个人消费贷款的基本条件包括：年龄22-60岁，征信良好，月收入5000元以上，工作满1年。您的情况如何？"}
        ],
        "metadata": {
            "scenario_type": "产品咨询"
        }
    }
    
    # 测试咨询问答数据
    test_consultation = {
        "input": "[我想了解贷款利率情况]",
        "output": "[目前个人消费贷款年化利率在4.35%-18%之间，具体利率根据您的征信情况和还款能力确定]",
        "metadata": {
            "consultation_type": "利率咨询"
        }
    }
    
    print("处理多轮对话数据...")
    chunks1 = processor.process_dialogue_data([test_multi_turn])
    
    print("处理咨询问答数据...")
    chunks2 = processor.process_dialogue_data([test_consultation])
    
    print(f"多轮对话生成了 {len(chunks1)} 个块")
    print(f"咨询问答生成了 {len(chunks2)} 个块")
    
    for chunk in chunks1 + chunks2:
        print(f"\n块ID: {chunk['chunk_id']}")
        print(f"类型: {chunk['content_type']}")
        print(f"内容: {chunk['content'][:100]}...")