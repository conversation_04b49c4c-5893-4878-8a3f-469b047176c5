# knowledge_base/product_processor.py
import json
import re
from typing import Dict, List, Any

class ProductProcessor:
    """产品信息处理器"""
    
    def __init__(self):
        self.required_fields = ['product_id', 'name', 'type', 'institution']
        self.optional_fields = ['interest_rate', 'features', 'requirements', 'approval_rate']
    
    def process(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理产品数据"""
        try:
            # 验证必要字段
            self._validate_product_data(product_data)
            
            # 生成标题
            title = self._generate_title(product_data)
            
            # 生成内容
            content = self._generate_content(product_data)
            
            # 提取元数据
            metadata = self._extract_metadata(product_data)
            
            return {
                'title': title,
                'content': content,
                'metadata': metadata
            }
            
        except Exception as e:
            raise Exception(f"处理产品数据失败: {e}")
    
    def _validate_product_data(self, data: Dict[str, Any]):
        """验证产品数据"""
        for field in self.required_fields:
            if field not in data:
                raise ValueError(f"缺少必要字段: {field}")
    
    def _generate_title(self, data: Dict[str, Any]) -> str:
        """生成产品标题"""
        institution = data.get('institution', '')
        name = data.get('name', '')
        product_type = data.get('type', '')
        
        return f"{institution}{name}（{product_type}）"
    
    def _generate_content(self, data: Dict[str, Any]) -> str:
        """生成产品内容描述"""
        content_parts = []
        
        # 基本信息
        basic_info = f"{data['name']}是{data['institution']}推出的{data['type']}产品"
        content_parts.append(basic_info)
        
        # 利率信息
        if 'interest_rate' in data:
            rate_info = data['interest_rate']
            if isinstance(rate_info, dict):
                min_rate = self._safe_extract_number(rate_info.get('min', 0))
                max_rate = self._safe_extract_number(rate_info.get('max', 0))
                rate_type = rate_info.get('type', '年化利率')
                rate_desc = f"{rate_type}{min_rate}%-{max_rate}%"
                content_parts.append(rate_desc)
        
        # 产品特征
        if 'features' in data:
            features = data['features']
            if isinstance(features, dict):
                feature_parts = []
                
                if 'amount_range' in features:
                    feature_parts.append(f"贷款额度{features['amount_range']}")
                
                if 'term_range' in features:
                    feature_parts.append(f"贷款期限{features['term_range']}")
                
                if 'repayment_methods' in features:
                    repayment = features['repayment_methods']
                    if isinstance(repayment, list):
                        repayment_str = '/'.join(repayment)
                    else:
                        repayment_str = str(repayment)
                    feature_parts.append(f"还款方式{repayment_str}")
                
                if 'guarantee_type' in features:
                    feature_parts.append(f"担保方式{features['guarantee_type']}")
                
                if feature_parts:
                    content_parts.append("，".join(feature_parts))
        
        # 申请条件
        if 'requirements' in data:
            requirements = data['requirements']
            if isinstance(requirements, dict):
                req_parts = []
                
                basic_req = requirements.get('basic_requirements', {})
                if isinstance(basic_req, dict):
                    if 'credit_score' in basic_req:
                        req_parts.append(f"征信要求{basic_req['credit_score']}")
                    
                    if 'income_requirement' in basic_req:
                        req_parts.append(f"收入要求{basic_req['income_requirement']}")
                    
                    if 'age_range' in basic_req:
                        req_parts.append(f"年龄要求{basic_req['age_range']}")
                    
                    if 'employment_years' in basic_req:
                        req_parts.append(f"工作年限{basic_req['employment_years']}")
                
                if req_parts:
                    content_parts.append(f"申请条件：{'/'.join(req_parts)}")
        
        # 审批信息
        if 'approval_info' in data:
            approval_info = data['approval_info']
            if isinstance(approval_info, dict):
                if 'approval_rate' in approval_info:
                    approval_rate = self._safe_extract_percentage(approval_info['approval_rate'])
                    content_parts.append(f"历史批准率{approval_rate:.1f}%")
                
                if 'avg_approval_time' in approval_info:
                    approval_time = approval_info['avg_approval_time']
                    content_parts.append(f"平均审批时间{approval_time}")
        
        return "，".join(content_parts) + "。"
    
    def _safe_extract_number(self, value: Any) -> float:
        """安全提取数字值"""
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            # 移除百分号和其他非数字字符
            cleaned = re.sub(r'[^\d.]', '', value)
            try:
                return float(cleaned) if cleaned else 0.0
            except ValueError:
                return 0.0
        return 0.0
    
    def _safe_extract_percentage(self, value: Any) -> float:
        """安全提取百分比值"""
        if isinstance(value, str):
            if '%' in value:
                # 移除百分号并转换
                cleaned = value.replace('%', '').strip()
                try:
                    return float(cleaned)
                except ValueError:
                    return 0.0
            else:
                # 尝试按约字提取
                match = re.search(r'约?(\d+(?:\.\d+)?)%?', value)
                if match:
                    return float(match.group(1))
        elif isinstance(value, (int, float)):
            # 如果是小数，转换为百分比
            if 0 <= value <= 1:
                return value * 100
            else:
                return value
        return 0.0
    
    def _extract_metadata(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取产品元数据"""
        metadata = {}
        
        # 复制所有原始数据
        for key, value in data.items():
            metadata[key] = value
        
        # 添加处理后的字段
        metadata['processed'] = True
        metadata['content_type'] = 'financial_product'
        
        # 标准化产品类型
        metadata['standardized_type'] = self._standardize_product_type(data.get('type', ''))
        
        # 提取关键属性
        metadata['key_attributes'] = self._extract_key_attributes(data)
        
        # 目标客户群
        metadata['target_customers'] = self._identify_target_customers(data)
        
        return metadata
    
    def _standardize_product_type(self, product_type: str) -> str:
        """标准化产品类型"""
        type_mapping = {
            '消费贷': 'consumer_loan',
            '抵押贷': 'mortgage_loan',
            '经营贷': 'business_loan',
            '信用卡': 'credit_card',
            '房贷': 'housing_loan',
            '车贷': 'auto_loan'
        }
        return type_mapping.get(product_type, product_type.lower())
    
    def _extract_key_attributes(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """提取关键属性"""
        attributes = {}
        
        # 利率相关
        if 'interest_rate' in data:
            rate_info = data['interest_rate']
            if isinstance(rate_info, dict):
                min_rate = self._safe_extract_number(rate_info.get('min', 0))
                max_rate = self._safe_extract_number(rate_info.get('max', 0))
                attributes['min_rate'] = min_rate
                attributes['max_rate'] = max_rate
                attributes['avg_rate'] = (min_rate + max_rate) / 2
        
        # 额度相关
        if 'features' in data and isinstance(data['features'], dict):
            features = data['features']
            if 'amount_range' in features:
                amount_range = features['amount_range']
                # 尝试解析额度范围
                attributes['amount_info'] = self._parse_amount_range(amount_range)
        
        # 风险等级评估
        attributes['risk_level'] = self._assess_product_risk(data)
        
        return attributes
    
    def _parse_amount_range(self, amount_range: str) -> Dict[str, Any]:
        """解析额度范围"""
        # 处理多种格式的额度范围
        patterns = [
            r'(\d+)万?元?\s*-\s*(\d+)万',  # 1万-50万
            r'(\d+)\s*-\s*(\d+)万',       # 1-50万
            r'(\d+)元?\s*-\s*(\d+)元?',   # 10000元-500000元
            r'(\d+)\s*-\s*(\d+)',         # 10000-500000
        ]
        
        for pattern in patterns:
            match = re.search(pattern, amount_range)
            if match:
                try:
                    min_amount = int(match.group(1))
                    max_amount = int(match.group(2))
                    
                    # 如果包含万字，需要转换
                    if '万' in amount_range:
                        min_amount *= 10000
                        max_amount *= 10000
                    
                    return {
                        'min_amount': min_amount,
                        'max_amount': max_amount,
                        'range_text': amount_range
                    }
                except (ValueError, TypeError):
                    continue
        
        return {'range_text': amount_range}
    
    def _assess_product_risk(self, data: Dict[str, Any]) -> str:
        """评估产品风险等级"""
        product_type = data.get('type', '')
        
        # 根据产品类型判断基础风险
        type_risk_map = {
            '抵押贷': '低风险',
            '房贷': '低风险',
            '消费贷': '中风险',
            '车贷': '中风险',
            '经营贷': '高风险',
            '信用卡': '中风险'
        }
        
        base_risk = type_risk_map.get(product_type, '中风险')
        
        # 根据利率调整风险等级
        if 'interest_rate' in data:
            rate_info = data['interest_rate']
            if isinstance(rate_info, dict):
                max_rate = self._safe_extract_number(rate_info.get('max', 10))
                if max_rate > 15:
                    base_risk = '高风险'
                elif max_rate < 6:
                    base_risk = '低风险'
        
        return base_risk
    
    def _identify_target_customers(self, data: Dict[str, Any]) -> List[str]:
        """识别目标客户群"""
        customers = []
        
        product_type = data.get('type', '')
        
        # 根据产品类型判断目标客户
        if product_type == '消费贷':
            customers.extend(['上班族', '有稳定收入人群'])
        elif product_type == '抵押贷':
            customers.extend(['有房产人群', '大额资金需求者'])
        elif product_type == '经营贷':
            customers.extend(['小微企业主', '个体工商户'])
        elif product_type == '房贷':
            customers.extend(['购房者', '首套房买家', '改善型购房者'])
        elif product_type == '车贷':
            customers.extend(['购车者', '有驾照人群'])
        
        # 根据申请条件细化目标客户
        if 'requirements' in data:
            requirements = data['requirements']
            if isinstance(requirements, dict):
                basic_req = requirements.get('basic_requirements', {})
                if isinstance(basic_req, dict):
                    income_req = basic_req.get('income_requirement', '')
                    if '10000' in str(income_req):
                        customers.append('高收入人群')
                    elif '5000' in str(income_req):
                        customers.append('中等收入人群')
                    
                    credit_req = basic_req.get('credit_score', '')
                    if '700' in str(credit_req):
                        customers.append('征信优良客户')
        
        return list(set(customers))  # 去重

if __name__ == "__main__":
    # 测试产品处理器
    processor = ProductProcessor()
    
    test_product = {
        "product_id": "LOAN001",
        "name": "个人消费贷款",
        "type": "消费贷",
        "institution": "工商银行",
        "interest_rate": {"min": "4.35%", "max": "18.0%", "type": "年化利率"},
        "features": {
            "amount_range": "1万-50万元",
            "term_range": "6个月-5年",
            "repayment_methods": ["等额本息", "等额本金"],
            "guarantee_type": "信用贷款"
        },
        "requirements": {
            "basic_requirements": {
                "credit_score": "≥650",
                "income_requirement": "≥5000元",
                "age_range": "22-60岁",
                "employment_years": "≥1年"
            }
        },
        "approval_info": {
            "approval_rate": "约70%",
            "avg_approval_time": "3-5个工作日"
        }
    }
    
    try:
        result = processor.process(test_product)
        print("处理结果:")
        print(f"标题: {result['title']}")
        print(f"内容: {result['content']}")
        print(f"目标客户: {result['metadata']['target_customers']}")
        print(f"风险等级: {result['metadata']['key_attributes']['risk_level']}")
    except Exception as e:
        print(f"处理失败: {e}")